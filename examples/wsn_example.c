/*
 * Copyright (c) 2024, Xi'an Helon-Electric Co., Ltd.
 *
 * Author: kevin<PERSON><PERSON><PERSON>
 *
 * CONFIDENTIAL AND PROPRIETARY
 * 严格保密，泄露必究
 *
 * Change Logs:
 * Date           Author       Notes
 * 2024-07-10    kevin.zhao  first version
 */

#include <rtthread.h>
#include <rtdevice.h>
#include "components/wsn/core/msg_bus/wsn_msg_bus.h"
#include "components/wsn/core/msg_bus/wsn_msg_bus_adapter.h"
#include "components/wsn/core/data_proc/wsn_data_proc.h"
#include "components/wsn/core/data_proc/wsn_data_proc_adapter.h"

#define DBG_TAG              "example.wsn"
#define DBG_LVL              DBG_INFO
#include <rtdbg.h>

/* 示例设备名称 */
#define EXAMPLE_DEVICE_NAME  "dev_demo"

/* 温度传感器消息 */
#define MSG_SENSOR_TEMP      (WSN_MSG_SENSOR_DATA | 0x01)
#define MSG_SENSOR_HUMID     (WSN_MSG_SENSOR_DATA | 0x02)

/* 传感器数据结构 */
typedef struct {
    rt_uint32_t timestamp;   /* 时间戳 */
    float value;             /* 数值 */
    rt_uint8_t unit;         /* 单位 */
    rt_uint8_t status;       /* 状态 */
} sensor_data_t;

/* 处理后数据结构 */
typedef struct {
    rt_uint32_t timestamp;   /* 时间戳 */
    float avg_value;         /* 平均值 */
    float max_value;         /* 最大值 */
    float min_value;         /* 最小值 */
    rt_uint8_t sample_count; /* 样本数量 */
    rt_uint8_t unit;         /* 单位 */
} processed_data_t;

/* 温度数据处理函数 */
static rt_err_t temperature_data_processor(wsn_data_proc_item_t *item, void *user_data)
{
    sensor_data_t *raw_data = (sensor_data_t *)item->data;
    processed_data_t *proc_data = NULL;
    
    /* 参数检查 */
    if (item == RT_NULL || raw_data == RT_NULL || item->data_size != sizeof(sensor_data_t))
    {
        LOG_E("Invalid temperature data");
        return -RT_ERROR;
    }
    
    LOG_D("Processing temperature data: %.2f %s, status: %d",
         raw_data->value, raw_data->unit == 0 ? "°C" : "°F", raw_data->status);
         
    /* 分配处理结果内存 */
    proc_data = (processed_data_t *)rt_malloc(sizeof(processed_data_t));
    if (proc_data == RT_NULL)
    {
        LOG_E("Failed to allocate memory for processed data");
        return -RT_ENOMEM;
    }
    
    /* 演示：简单处理逻辑，在实际应用中可能更复杂 */
    proc_data->timestamp = raw_data->timestamp;
    proc_data->avg_value = raw_data->value; /* 单样本直接作为平均值 */
    proc_data->max_value = raw_data->value;
    proc_data->min_value = raw_data->value;
    proc_data->sample_count = 1;
    proc_data->unit = raw_data->unit;
    
    /* 释放原始数据，替换为处理结果 */
    rt_free(item->data);
    item->data = proc_data;
    item->data_size = sizeof(processed_data_t);
    
    LOG_I("Temperature data processed");
    return RT_EOK;
}

/* 湿度数据处理函数 */
static rt_err_t humidity_data_processor(wsn_data_proc_item_t *item, void *user_data)
{
    sensor_data_t *raw_data = (sensor_data_t *)item->data;
    processed_data_t *proc_data = NULL;
    
    /* 参数检查 */
    if (item == RT_NULL || raw_data == RT_NULL || item->data_size != sizeof(sensor_data_t))
    {
        LOG_E("Invalid humidity data");
        return -RT_ERROR;
    }
    
    LOG_D("Processing humidity data: %.2f%%, status: %d",
         raw_data->value, raw_data->status);
         
    /* 分配处理结果内存 */
    proc_data = (processed_data_t *)rt_malloc(sizeof(processed_data_t));
    if (proc_data == RT_NULL)
    {
        LOG_E("Failed to allocate memory for processed data");
        return -RT_ENOMEM;
    }
    
    /* 演示：简单处理逻辑，在实际应用中可能更复杂 */
    proc_data->timestamp = raw_data->timestamp;
    proc_data->avg_value = raw_data->value; /* 单样本直接作为平均值 */
    proc_data->max_value = raw_data->value;
    proc_data->min_value = raw_data->value;
    proc_data->sample_count = 1;
    proc_data->unit = raw_data->unit;
    
    /* 释放原始数据，替换为处理结果 */
    rt_free(item->data);
    item->data = proc_data;
    item->data_size = sizeof(processed_data_t);
    
    LOG_I("Humidity data processed");
    return RT_EOK;
}

/* 温度消息回调函数 */
static rt_int8_t temperature_msg_callback(wsn_message_t msg, void *user_data)
{
    if (msg == RT_NULL || msg->data == RT_NULL)
    {
        LOG_E("Invalid temperature message");
        return WSN_MSG_ERROR;
    }
    
    sensor_data_t *temp_data = (sensor_data_t *)msg->data;
    
    LOG_I("Temperature received: %.2f %s, status: %d",
         temp_data->value, temp_data->unit == 0 ? "°C" : "°F", temp_data->status);
    
    /* 返回继续，允许其他订阅者处理 */
    return WSN_MSG_CONTINUE;
}

/* 湿度消息回调函数 */
static rt_int8_t humidity_msg_callback(wsn_message_t msg, void *user_data)
{
    if (msg == RT_NULL || msg->data == RT_NULL)
    {
        LOG_E("Invalid humidity message");
        return WSN_MSG_ERROR;
    }
    
    sensor_data_t *humid_data = (sensor_data_t *)msg->data;
    
    LOG_I("Humidity received: %.2f%%, status: %d",
         humid_data->value, humid_data->status);
    
    /* 返回继续，允许其他订阅者处理 */
    return WSN_MSG_CONTINUE;
}

/* 处理结果回调函数 */
static rt_err_t process_result_callback(struct wsn_msg_data_processed *result, void *user_data)
{
    if (result == RT_NULL || result->data == RT_NULL)
    {
        LOG_E("Invalid process result");
        return -RT_ERROR;
    }
    
    processed_data_t *proc_data = (processed_data_t *)result->data;
    
    LOG_I("Process result (ID: %u):", result->process_id);
    LOG_I("  Timestamp: %u", proc_data->timestamp);
    LOG_I("  Average: %.2f", proc_data->avg_value);
    LOG_I("  Max: %.2f", proc_data->max_value);
    LOG_I("  Min: %.2f", proc_data->min_value);
    LOG_I("  Samples: %d", proc_data->sample_count);
    
    return RT_EOK;
}

/* 消息生成线程 */
static void msg_generator_thread_entry(void *parameter)
{
    rt_tick_t period = *(rt_tick_t *)parameter;  /* 消息生成周期 */
    rt_uint32_t count = 0;
    rt_err_t ret;
    sensor_data_t temp_data, humid_data;
    
    /* 等待初始化完成 */
    rt_thread_mdelay(100);
    
    while (1)
    {
        /* 模拟温度数据 */
        temp_data.timestamp = rt_tick_get();
        temp_data.value = 20.0f + (float)(rt_tick_get() % 100) / 10.0f;  /* 20.0 - 30.0℃ */
        temp_data.unit = 0;  /* 摄氏度 */
        temp_data.status = 0;  /* 正常 */
        
        /* 发布温度消息 */
        ret = wsn_msg_publish(EXAMPLE_DEVICE_NAME, MSG_SENSOR_TEMP, 
                            &temp_data, sizeof(temp_data), 0);
        if (ret != RT_EOK)
        {
            LOG_E("Failed to publish temperature message, error: %d", ret);
        }
        
        /* 等待一段时间 */
        rt_thread_mdelay(period / 2);
        
        /* 模拟湿度数据 */
        humid_data.timestamp = rt_tick_get();
        humid_data.value = 40.0f + (float)(rt_tick_get() % 200) / 10.0f;  /* 40.0 - 60.0% */
        humid_data.unit = 0;  /* 百分比 */
        humid_data.status = 0;  /* 正常 */
        
        /* 发布湿度消息 */
        ret = wsn_msg_publish(EXAMPLE_DEVICE_NAME, MSG_SENSOR_HUMID, 
                            &humid_data, sizeof(humid_data), 0);
        if (ret != RT_EOK)
        {
            LOG_E("Failed to publish humidity message, error: %d", ret);
        }
        
        count++;
        LOG_D("Generated message pair #%u", count);
        
        /* 等待下一周期 */
        rt_thread_mdelay(period / 2);
    }
}

/* WSN示例初始化 */
static int wsn_example_init(void)
{
    wsn_device_adapter_t adapter;
    wsn_data_proc_mgr_t data_mgr;
    rt_err_t ret;
    rt_thread_t thread;
    static rt_tick_t period = 5000;  /* 5秒生成一对消息 */
    
    /* 初始化消息总线 */
    ret = wsn_msg_adapter_init();
    if (ret != RT_EOK)
    {
        LOG_E("Failed to initialize message bus adapter");
        return ret;
    }
    
    /* 初始化数据处理适配器 */
    ret = wsn_data_proc_adapter_init();
    if (ret != RT_EOK)
    {
        LOG_E("Failed to initialize data processor adapter");
        return ret;
    }
    
    /* 创建设备适配器 */
    adapter = wsn_device_adapter_create(EXAMPLE_DEVICE_NAME);
    if (adapter == RT_NULL)
    {
        LOG_E("Failed to create device adapter");
        goto _error_cleanup;
    }
    
    /* 激活设备适配器 */
    ret = wsn_device_adapter_set_active(adapter, RT_TRUE);
    if (ret != RT_EOK)
    {
        LOG_E("Failed to activate device adapter");
        goto _error_cleanup;
    }
    
    /* 创建直接路由规则 */
    ret = wsn_create_direct_route(adapter, MSG_SENSOR_TEMP);
    if (ret != RT_EOK)
    {
        LOG_E("Failed to create temperature route");
        goto _error_cleanup;
    }
    
    ret = wsn_create_direct_route(adapter, MSG_SENSOR_HUMID);
    if (ret != RT_EOK)
    {
        LOG_E("Failed to create humidity route");
        goto _error_cleanup;
    }
    
    /* 订阅消息 */
    ret = wsn_msg_subscribe("temp_subscriber", MSG_SENSOR_TEMP, 
                          temperature_msg_callback, RT_NULL);
    if (ret != RT_EOK)
    {
        LOG_E("Failed to subscribe temperature message");
        goto _error_cleanup;
    }
    
    ret = wsn_msg_subscribe("humid_subscriber", MSG_SENSOR_HUMID, 
                          humidity_msg_callback, RT_NULL);
    if (ret != RT_EOK)
    {
        LOG_E("Failed to subscribe humidity message");
        goto _error_cleanup;
    }
    
    /* 创建数据处理管理器 */
    data_mgr = wsn_data_proc_mgr_create("sensor_proc");
    if (data_mgr == RT_NULL)
    {
        LOG_E("Failed to create data processor manager");
        goto _error_cleanup;
    }
    
    /* 注册处理器 */
    ret = wsn_data_proc_mgr_register(data_mgr, WSN_DATA_PROC_TYPE_SENSOR,
                                   temperature_data_processor, RT_NULL);
    if (ret != RT_EOK)
    {
        LOG_E("Failed to register temperature processor");
        goto _error_cleanup;
    }
    
    /* 绑定数据处理器到设备 */
    ret = wsn_data_proc_bind_to_device(EXAMPLE_DEVICE_NAME, data_mgr, 
                                     RT_NULL, 0);  /* 使用默认消息类型 */
    if (ret != RT_EOK)
    {
        LOG_E("Failed to bind data processor to device");
        goto _error_cleanup;
    }
    
    /* 创建消息生成线程 */
    thread = rt_thread_create("msg_gen",
                            msg_generator_thread_entry,
                            &period,
                            2048,
                            20,
                            10);
    if (thread == RT_NULL)
    {
        LOG_E("Failed to create message generator thread");
        goto _error_cleanup;
    }
    
    /* 启动线程 */
    rt_thread_startup(thread);
    
    LOG_I("WSN example initialized successfully");
    return RT_EOK;
    
_error_cleanup:
    wsn_msg_adapter_deinit();
    wsn_data_proc_adapter_deinit();
    return -RT_ERROR;
}
MSH_CMD_EXPORT(wsn_example_init, WSN example); 