import os
import re
import json

# 定义搜索路径
semtech_path = 'semtech'
swl_path = 'SWL2001-4.8.0'

# 存储结果的列表
results = []

# 文件和行号映射关系
file_line_mapping = {
    'semtech\\proto\\modem\\radio_planner.c': {
        'target_file': 'SWL2001-4.8.0/lbm_lib/smtc_modem_core/radio_planner/src/radio_planner.c',
        'line_mapping': {1192: 1212}  # semtech行号: SWL行号
    },
    'semtech\\proto\\modem\\smtc_modem.c': {
        'target_file': 'SWL2001-4.8.0/lbm_lib/smtc_modem_core/smtc_modem.c',
        'line_mapping': {192: 246}  # semtech行号: SWL行号
    },
    'semtech\\proto\\crypto\\smtc_modem_crypto.c': {
        'target_file': 'SWL2001-4.8.0/lbm_lib/smtc_modem_core/smtc_modem_crypto/smtc_modem_crypto.c',
        'line_mapping': {166: 192}  # semtech行号: SWL行号
    },
    'semtech\\proto\\lr1mac\\lr1_stack_mac_layer.c': {
        'target_file': 'SWL2001-4.8.0/lbm_lib/smtc_modem_core/lr1mac/src/lr1_stack_mac_layer.c',
        'line_mapping': {334: 370, 487: 523}  # semtech行号: SWL行号
    }
}

# 递归遍历目录查找包含rt_weak的行
def find_rt_weak(directory):
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.c') or file.endswith('.h'):
                file_path = os.path.join(root, file)
                relative_path = os.path.relpath(file_path)
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        lines = f.readlines()
                        for line_num, line in enumerate(lines, 1):
                            if 'rt_weak' in line:
                                results.append({
                                    'file': relative_path,
                                    'line_number': line_num,
                                    'content': line.strip()
                                })
                except Exception as e:
                    print(f"Error reading {file_path}: {e}")

# 更新SWL2001-4.8.0中的对应文件
def update_swl_files():
    updated_files = []
    
    for item in results:
        semtech_file = item['file']
        if semtech_file in file_line_mapping:
            mapping = file_line_mapping[semtech_file]
            swl_file = mapping['target_file']
            semtech_line_number = item['line_number']
            
            # 检查是否有对应的行号映射
            if semtech_line_number in mapping['line_mapping']:
                swl_line_number = mapping['line_mapping'][semtech_line_number]
                content = item['content']
                
                try:
                    # 读取SWL文件
                    with open(swl_file, 'r', encoding='utf-8', errors='ignore') as f:
                        lines = f.readlines()
                    
                    # 检查对应行是否已经包含rt_weak
                    if swl_line_number <= len(lines) and 'rt_weak' not in lines[swl_line_number-1]:
                        # 提取函数声明
                        function_match = re.search(r'(void|int|uint\w+_t|smtc_modem_crypto_return_code_t|[a-zA-Z_]+_t)\s+([a-zA-Z0-9_]+)\s*\(', lines[swl_line_number-1])
                        if function_match:
                            return_type = function_match.group(1)
                            function_name = function_match.group(2)
                            
                            # 在函数声明前添加rt_weak
                            modified_line = lines[swl_line_number-1].replace(f"{return_type} {function_name}", f"rt_weak {return_type} {function_name}")
                            lines[swl_line_number-1] = modified_line
                            
                            # 写回文件
                            with open(swl_file, 'w', encoding='utf-8') as f:
                                f.writelines(lines)
                            
                            updated_files.append({
                                'semtech_file': semtech_file,
                                'semtech_line': semtech_line_number,
                                'swl_file': swl_file,
                                'swl_line': swl_line_number,
                                'status': 'updated'
                            })
                            print(f"Updated {swl_file} at line {swl_line_number}")
                        else:
                            updated_files.append({
                                'semtech_file': semtech_file,
                                'semtech_line': semtech_line_number,
                                'swl_file': swl_file,
                                'swl_line': swl_line_number,
                                'status': 'no_function_match'
                            })
                            print(f"Could not find function declaration in {swl_file} at line {swl_line_number}")
                    else:
                        updated_files.append({
                            'semtech_file': semtech_file,
                            'semtech_line': semtech_line_number,
                            'swl_file': swl_file,
                            'swl_line': swl_line_number,
                            'status': 'already_has_rt_weak'
                        })
                        print(f"{swl_file} at line {swl_line_number} already has rt_weak")
                except Exception as e:
                    updated_files.append({
                        'semtech_file': semtech_file,
                        'semtech_line': semtech_line_number,
                        'swl_file': swl_file,
                        'swl_line': swl_line_number,
                        'status': 'error',
                        'error': str(e)
                    })
                    print(f"Error updating {swl_file}: {e}")
            else:
                updated_files.append({
                    'semtech_file': semtech_file,
                    'semtech_line': semtech_line_number,
                    'status': 'no_line_mapping'
                })
                print(f"No line mapping found for {semtech_file} line {semtech_line_number}")
        else:
            updated_files.append({
                'semtech_file': semtech_file,
                'semtech_line': semtech_line_number,
                'status': 'no_file_mapping'
            })
            print(f"No file mapping found for {semtech_file}")
    
    return updated_files

# 执行搜索
find_rt_weak(semtech_path)

# 将结果保存为JSON文件
with open('rt_weak_results.json', 'w', encoding='utf-8') as f:
    json.dump(results, f, indent=4)

print(f"Found {len(results)} occurrences of rt_weak. Results saved to rt_weak_results.json")

# 更新SWL2001-4.8.0中的文件
update_results = update_swl_files()

# 将更新结果保存为JSON文件
with open('update_results.json', 'w', encoding='utf-8') as f:
    json.dump(update_results, f, indent=4)

# 打印更新结果
print("\n更新结果:")
for item in update_results:
    if item['status'] == 'updated':
        print(f"- {item['semtech_file']} 行 {item['semtech_line']} -> {item['swl_file']} 行 {item['swl_line']} (已更新)")
    elif item['status'] == 'already_has_rt_weak':
        print(f"- {item['semtech_file']} 行 {item['semtech_line']} -> {item['swl_file']} 行 {item['swl_line']} (已包含rt_weak)")
    elif item['status'] == 'no_function_match':
        print(f"- {item['semtech_file']} 行 {item['semtech_line']} -> {item['swl_file']} 行 {item['swl_line']} (未找到函数声明)")
    elif item['status'] == 'error':
        print(f"- {item['semtech_file']} 行 {item['semtech_line']} -> {item['swl_file']} 行 {item['swl_line']} (更新出错: {item['error']})")
    elif item['status'] == 'no_line_mapping':
        print(f"- {item['semtech_file']} 行 {item['semtech_line']} -> 未找到对应行号")
    elif item['status'] == 'no_file_mapping':
        print(f"- {item['semtech_file']} 行 {item['semtech_line']} -> 未找到对应文件") 