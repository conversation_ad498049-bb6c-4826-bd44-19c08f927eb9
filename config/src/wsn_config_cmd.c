/*
 * Copyright (c) 2024, Xi'an Helon-Electric Co., Ltd.
 *
 * Author: kevin<PERSON><PERSON><PERSON>
 *
 * CONFIDENTIAL AND PROPRIETARY
 * 严格保密，泄露必究
 *
 * Change Logs:
 * Date           Author       Notes
 * 2024-09-15    kevin.zhao    first version
 */ 

#include <rtthread.h>
#include <rtdevice.h>
#include "wsn_config.h"
#include "uid.h"

#define DBG_TAG "wsn_config_cmd"
#define DBG_LVL DBG_INFO
#include <rtdbg.h>

/**
 * @brief 显示芯片ID和校验码
 */
static void wsn_config_show_chip_id(void)
{
    uint32_t chip_id[4] = {0};
    rt_err_t ret;
    
    /* 获取ChipId */
    ret = wsn_config_get_chip_id(chip_id);
    if (ret != RT_EOK)
    {
        rt_kprintf("Failed to get chip ID\n");
        return;
    }
    
    /* 显示信息 */
    rt_kprintf("Chip ID: [0x%08X, 0x%08X, 0x%08X, 0x%08X]\n", 
               chip_id[0], chip_id[1], chip_id[2], chip_id[3]);
    
    /* 获取并显示芯片码 */
    uint32_t chip_code = wsn_config_get_chip_code();
    rt_kprintf("Chip Code: 0x%08X\n", chip_code);
}

/**
 * @brief 更新设备信息（仅限厂家使用）
 */
static void wsn_config_update_device_info(uint32_t chip_code)
{
    wsn_config_device_info_t device_info;
    rt_err_t ret;
    
    /* 获取当前设备信息 */
    ret = wsn_config_get_device_info(&device_info);
    if (ret != RT_EOK)
    {
        rt_kprintf("Failed to get device info\n");
        return;
    }
    
    /* 修改设备信息 */
    rt_strncpy((char*)device_info.manufacturer, "Helon-Electric", sizeof(device_info.manufacturer) - 1);
    rt_strncpy((char*)device_info.description, "WSN-Node-Updated", sizeof(device_info.description) - 1);
    device_info.hardware_version = 0x01010000; /* ******* */
    
    /* 使用Chip Code验证并更新设备信息 */
    ret = wsn_config_set_device_info(&device_info, chip_code);
    if (ret != RT_EOK)
    {
        rt_kprintf("Failed to update device info\n");
        return;
    }
    
    rt_kprintf("Device info updated successfully\n");
}

/**
 * @brief 尝试直接更新设备信息（预期会失败）
 */
static void wsn_config_try_direct_update(void)
{
    wsn_config_device_info_t device_info;
    rt_err_t ret;
    
    /* 获取当前设备信息 */
    ret = wsn_config_get_device_info(&device_info);
    if (ret != RT_EOK)
    {
        rt_kprintf("Failed to get device info\n");
        return;
    }
    
    /* 修改设备信息 */
    rt_strncpy((char*)device_info.manufacturer, "Unauthorized", sizeof(device_info.manufacturer) - 1);
    device_info.hardware_version = 0x02000000; /* ******* */
    
    /* 尝试直接更新（预期会失败） */
    ret = wsn_config_set_device_info_internal(&device_info);
    if (ret != RT_EOK)
    {
        rt_kprintf("Direct update failed as expected\n");
    }
    else
    {
        rt_kprintf("WARNING: Direct update succeeded unexpectedly\n");
    }
}

/**
 * @brief WSN配置保护演示命令
 */
static void wsn_config_protect_cmd(int argc, char *argv[])
{
    if (argc < 2)
    {
        rt_kprintf("Usage:\n");
        rt_kprintf("  wsn_config_protect show        - Show chip ID and code\n");
        rt_kprintf("  wsn_config_protect try_update  - Try unauthorized update\n");
        rt_kprintf("  wsn_config_protect update CODE - Update with verification code\n");
        return;
    }

    if (rt_strcmp(argv[1], "show") == 0)
    {
        wsn_config_show_chip_id();
    }
    else if (rt_strcmp(argv[1], "try_update") == 0)
    {
        wsn_config_try_direct_update();
    }
    else if (rt_strcmp(argv[1], "update") == 0)
    {
        if (argc < 3)
        {
            rt_kprintf("Missing chip code parameter\n");
            return;
        }
        
        uint32_t chip_code = strtoul(argv[2], NULL, 0);
        wsn_config_update_device_info(chip_code);
    }
    else
    {
        rt_kprintf("Unknown command: %s\n", argv[1]);
    }
}
MSH_CMD_EXPORT(wsn_config_protect_cmd, wsn config protection commands); 