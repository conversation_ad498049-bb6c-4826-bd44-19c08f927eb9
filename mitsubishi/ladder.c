//
// Created by kevin on 2021-12-13.
//

#include <math.h>
#include "ladder.h"
#include "io.h"
#include "reg.h"
#include "address_mapping.h"
uint16_t plc_scan_time; //
uint8_t  error_led;
uint8_t  run_led;
uint16_t commParm;
extern uint16_t code[];
#define PLC_LAD_START_ADDR code+46
const uint8_t  HEX_ASC []= {'0','1','2','3','4','5','6','7','8','9','A','B','C','D','E','F'};
const uint8_t  ASC_HEX0[]= {0x00,0x01,0x02,0x03,0x04,0x05,0x06,0x07,0x08,0x09,0x0a,0x0b,0x0c,0x0d,0x0e,0x0f,
                            0x00,0x01,0x02,0x03,0x04,0x05,0x06,0x07,0x08,0x09,0x0a,0x0b,0x0c,0x0d,0x0e,0x0f,
                            0x00,0x01,0x02,0x03,0x04,0x05,0x06,0x07,0x08,0x09,0x0a,0x0b,0x0c,0x0d,0x0e,0x0f,
                            0x00,0x01,0x02,0x03,0x04,0x05,0x06,0x07,0x08,0x09,0x0a,0x0b,0x0c,0x0d,0x0e,0x0f,
                            0x00,0x0a,0x0b,0x0c,0x0d,0x0e,0x0f,0x07,0x08,0x09,0x0a,0x0b,0x0c,0x0d,0x0e,0x0f
};
// 译码、编码
const uint16_t DE[]= {0x0000,0x0001,0x0003,0x0007,0x000F,0X001F,0X003F,0X007F,0X00FF,0X01FF,0X03FF,0X07FF,0X0FFF,0X1FFF,0X3FFF,0X7FFF,0XFFFF};
const uint8_t  SEGD_LED[]= {0x3f,0x06,0x5b,0x4f,0x66,0x6d,0x7d,0x27,0x7f,0x6f,0x77,0x7c,0x39,0x5e,0x79,0x71};

extern unsigned char Y0P,Y1P;                       //
extern unsigned short Plus_CMP0,Plus_CMP1;         //脉冲标志位
extern uint8_t  X_DIY;	                           //滤波时间
extern uint16_t plc_scan_time;                     //扫描时间

extern void RTC_Set(uint16_t syear,uint8_t smon,uint8_t sday,uint8_t hour,uint8_t min,uint8_t sec);//时间修改程序

extern void timer_enable(uint16_t timer_number); // 此处在定时器2里面，用于开启定时器处理工
extern void timer_disble(uint16_t timer_number); // 此处在定时器2里面
static uint8_t PLC_ACC_BIT,PLC_MPS_BIT;           // 程序执行专用(运算栈及分线栈)
static const uint16_t *PLC_Addr;                  // PLC程序指针
static const uint16_t *PLC_Err;                   // PLC出错步
static uint8_t T_number,C_number;                 // T&C地址缓存寄存器
static uint16_t T_value;                          // T比较缓存寄存器
static int32_t C_value;                           // C比较缓存寄存器
static uint32_t mov_d_addr;                       // K?M&Y&S&X指令缓存
static const uint16_t *PLC_P_Addr[129];	         // 方便调子程序取指针
static const uint16_t *p_save[129];               // 调子程序时保存上一个执行点位
uint8_t  Flag_bit=0xff,Transfer_bit,Transfer_bit1;// 其实就是k的标志位  减小函数的量，减轻CPU负担

uint16_t process[64];                             // 调子程序时保存上一个子程序值
uint32_t trade;                                   // 作用于加减法，减小函数的量，减轻CPU负担
uint16_t Transfer=0;                              // 作用多地传递和成批传递 减小函数的量，减轻CPU负担
uint8_t  edit_prog;                                // 从新编程缓存寄存器
extern uint8_t Write_Pro_flag;

float_union FLOAT;
s32_union   u32data,u32data1;
u64_union   u64data,u64data2;

/***************************************************FOR**************************************************/
struct
{
    const uint16_t *Addr[7];  //FOR 地址记录
    uint16_t cycle[7];        //当前循环的次数
    uint16_t count[7];        //目标循环的次数
    uint8_t  point;           //for指向的点数
} FOR_CMD;

/***************************************************STL**************************************************/
static uint16_t PLC_STL_Addr;	   // STL指令地址号
static uint8_t  PLC_STL_Status;    // STL指令当前状态 0整套程序没有STL状态，程序1为STL有状态，2为STL停止状态
static uint8_t  PLC_STL_CMD;	   // STL标志
static uint8_t  PLC_STL_Count;     // 计数线圈数量
static uint16_t PLC_STL_Coil[256]; // 线圈缓存寄存器
/********************************************************************************************************/

// kevin，20170317编写
struct MC_MCR
{
//	 uint16_t MC_Addr;    // MC指令地址号
    uint16_t PLC_MC_BIT; // MC指令当前状态 0整套程序没有MC状态，程序1为MC有状态，2为MC停止状态
    uint8_t  MC_Flg;     // MC标志
    int8_t  MC_SFR;	    // MC累加栈
} MC;

// 2019年11月优化、新增
static uint32_t pow10(uint32_t y)
{
    uint32_t dat = 1;
    while(y--) dat*=10;
    return dat;
}


uint8_t PLC_LDP_TEST(void)	                        //查看是不上升沿
{
    if(PLC_PL_BIT_TEST(PLC_Addr-PLC_LAD_START_ADDR)==off)//上升沿判断
    {
        if(PLC_ACC_BIT&0X01)			                      //当前值判断
        {
            PLC_PL_BIT_ON(PLC_Addr-PLC_LAD_START_ADDR);   //
            return 1;
        }
        else return 0;
    }
    else
    {
        if(!(PLC_ACC_BIT&0x01))						             //当前值判断
            PLC_PL_BIT_OFF(PLC_Addr-PLC_LAD_START_ADDR);    //
        return 0;
    }
}


// 2019年11月优化、新增
static uint8_t PLC_BIT_PLS_TEST(uint16_t M)	                     //查看是不上升沿
{
    if(PLC_PL_BIT_TEST(PLC_Addr-PLC_LAD_START_ADDR)==off)//上升沿判断
    {
        if(PLC_BIT_TEST(M))			                       //当前值判断
        {
            PLC_PL_BIT_ON(PLC_Addr-PLC_LAD_START_ADDR);
            return 1;
        }
        else return 0;
    }
    else
    {
        if(!PLC_BIT_TEST(M))						             //当前值判断
            PLC_PL_BIT_OFF(PLC_Addr-PLC_LAD_START_ADDR);    //
        return 0;
    }
}

/*********************************************
  函数功能：PLC代码错误处理程序
  err_id=01:指令出错(未识别指令)
  err_id=02:指令出错(暂不支持指令)
  err_id=10:数据出错(无法识别数据类型)
  err_id=11:数据出错(数据读取地址超出)
  err_id=12:数据出错(变址Z地址未知)
  err_id=13:数据出错(变址Z地址超出)
  err_id=20:CJ指令地址出错
  D8061,M8061=PC硬件错误
  D8063,M8063=链接,通信错误
  D8064,M8064=参数错误
  D8065,M8065=语法错误
  D8066,M8066=回路错误
  D8067,M8067=运算错误
  D8068,M8068=运算错误锁存
***********************************************/
void PLC_PROG_ERROR(uint16_t err,uint16_t err_id)
{
// 2019年11月优化、新增
    PLC_BIT_ON(err);                              //出错标志  kevin 屏蔽
    ERR_LAMP_ON;                                   //错误指示灯
    D8012=0;	                                     //扫描时间
    if (D8068==0)D8067=err_id;                      //语法错误
    // TOBO define 0x800605D
    if (D8068==0)D8068=(PLC_Err-(uint16_t*)(0x800605D)); //保存出错PC步
    D8069=D8068;
}

static void LD(uint16_t start_addr) // 起始地址，加元件编号值
{
    // 为STL状态区，全局步进
    if( (PLC_STL_Status == 1) || (MC.MC_Flg == 1))
    {
        if(PLC_STL_Status == 1)
        {
            PLC_ACC_BIT<<=1;
            if(PLC_BIT_TEST(start_addr)&& PLC_BIT_TEST(PLC_STL_Addr))
                PLC_ACC_BIT |=0x01;
        }

        if (MC.MC_Flg == 1) // MC,MCR指令
        {
//			if(PLC_BIT_TEST(start_addr)&& PLC_BIT_TEST(MC.MC_Addr))
            if(PLC_BIT_TEST(start_addr)&& (MC.PLC_MC_BIT & 0x01))
                PLC_ACC_BIT = (MC.PLC_MC_BIT & 0x01);
            else
                PLC_ACC_BIT = (MC.PLC_MC_BIT & 0x00);
        }
    }
    else
    {
        PLC_ACC_BIT<<=1;
        if(PLC_BIT_TEST(start_addr))
            PLC_ACC_BIT |=0x01;
    }
}


static void LDI(uint16_t start_addr)
{
    if( (PLC_STL_Status == 1) || (MC.MC_Flg == 1))
    {
        if(PLC_STL_Status == 1)                          //为STL状态区  全局步进
        {
            PLC_ACC_BIT<<=1;
            if((!(PLC_BIT_TEST(start_addr)))&&(PLC_BIT_TEST(PLC_STL_Addr)))
                PLC_ACC_BIT |=0x01;
        }

        if (MC.MC_Flg == 1) // MC,MCR指令
        {
            if(PLC_BIT_TEST(start_addr)&& (MC.PLC_MC_BIT & 0x01))
                PLC_ACC_BIT = (MC.PLC_MC_BIT & 0x00);
            else
                PLC_ACC_BIT = (MC.PLC_MC_BIT & 0x01);
        }
    }
    else
    {
        PLC_ACC_BIT<<=1;
        if(PLC_BIT_TEST(start_addr));
        else
            PLC_ACC_BIT |=0x01;
    }
}

void AND(uint16_t start_addr)
{
    if((PLC_BIT_TEST(start_addr))&&(PLC_ACC_BIT&0X01))
        PLC_ACC_BIT|=0X01;
    else
        PLC_ACC_BIT&=0XFE;
}

static void ANI(uint16_t start_addr)
{
    if((!(PLC_BIT_TEST(start_addr)))&&(PLC_ACC_BIT&0X01))
        PLC_ACC_BIT|=0X01;
    else
        PLC_ACC_BIT&=0XFE;
}

static void OR(uint16_t start_addr)
{
    if((PLC_BIT_TEST(start_addr))||(PLC_ACC_BIT&0X01))
        PLC_ACC_BIT|=0X01;
    else
        PLC_ACC_BIT&=0XFE;
}

static void ORI(uint16_t start_addr)
{
    if((!(PLC_BIT_TEST(start_addr)))||(PLC_ACC_BIT&0X01))
        PLC_ACC_BIT|=0X01;
    else
        PLC_ACC_BIT&=0XFE;
}

void OUT(uint16_t start_addr)
{
    if (PLC_STL_CMD == 1)                 //判断是不是进入步进模式
    {
        if (PLC_STL_Status == 1)           //是不是打开STL模式
        {
            if(start_addr < 0X000A)//判断开始步进 S000-S009
            {
                if((PLC_ACC_BIT&0x01)==0x01)
                {
                    PLC_BIT_OFF(PLC_STL_Addr); //OFF
                    PLC_BIT_ON(start_addr);    //ON
                }
            }
            else
            {
                if(PLC_BIT_TEST(PLC_STL_Addr))
                {
                    if((PLC_ACC_BIT&0x01)==0x01)
                    {
                        PLC_BIT_ON(start_addr); //ON
                        PLC_STL_Coil[PLC_STL_Count++]=start_addr;//记录步进中ON线圈地址 位下个步进成立清除用
                    }
                    else
                        PLC_BIT_OFF(start_addr); //OFF
                }

            }
        }
        else
        {
            if(start_addr < 0X000A)//判断开始步进 S000-S009
            {
                if(PLC_ACC_BIT & 0x01)
                {
                    PLC_BIT_ON(start_addr); //ON
                }
            }
            else
            {
                if(PLC_ACC_BIT&0x01)
                    PLC_BIT_ON(start_addr); //ON
                else
                    PLC_BIT_OFF(start_addr); //OFF
            }
        }
    }
    else
    {
        if(PLC_ACC_BIT&0X01)
            PLC_BIT_ON(start_addr); //ON
        else
            PLC_BIT_OFF(start_addr); //OFF
    }
}


static void PLC_BIT_SET(uint16_t start_addr)//位设置
{
    uint8_t temp;
    if(PLC_ACC_BIT&0x01)
    {
        if (PLC_STL_Status == 1) // 为STL状态区
        {
            for(temp=0; temp<=PLC_STL_Count; temp++)
                PLC_BIT_OFF(PLC_STL_Coil[temp]); //清除上次ON线圈状态

            PLC_BIT_OFF(PLC_STL_Addr); // OFF
            PLC_BIT_ON(start_addr); // ON
            PLC_STL_Count=0; // 清除上次记录ON线圈数量
        }
        else
        {
            PLC_BIT_ON(start_addr); // 0N
        }
    }
}


static void RST(uint16_t start_addr)//复位位
{
    if((PLC_ACC_BIT&0X01)==0X01)
        PLC_BIT_OFF(start_addr);     //OFF
}

static void RET(void)
{
    PLC_STL_Status =0;               //退出步进模式 让程序进入梯形图
}

void STL(uint16_t start_addr)	      //步进 模式
{
    PLC_STL_CMD = 1;            //全局程序启用步进标志
    PLC_STL_Status = 1;         //启动步进模式
    PLC_STL_Addr = start_addr;  //记录步进地址
    PLC_ACC_BIT<<=1;
    if(PLC_BIT_TEST(PLC_STL_Addr))
        PLC_ACC_BIT |=0x01;
}

// kevin，20160926优化
static void other_function(uint8_t process_addr)
{
    switch(process_addr)
    {
        case 0xF8: //块串联 ANB
        {
            PLC_ACC_BIT = (PLC_ACC_BIT >> 1)   & ((PLC_ACC_BIT & 0x01)|0xFE);
            break;
        }
        case 0xF9: //块并联 ORB
        {
            PLC_ACC_BIT = (PLC_ACC_BIT >> 1)   | (PLC_ACC_BIT & 0x01);
            break;
        }
        case 0xFA: //进栈   MPS
        {
            PLC_MPS_BIT = (PLC_MPS_BIT << 1)   | (PLC_ACC_BIT & 0x01);
            break;
        }
        case 0xFB: //读栈   MRD
        {
            PLC_ACC_BIT = (PLC_ACC_BIT & 0xfe) | (PLC_MPS_BIT & 0x01);
            break;
        }
        case 0xFC: //出栈   MPP
        {
            PLC_ACC_BIT = (PLC_ACC_BIT & 0xfe) | (PLC_MPS_BIT & 0x01),PLC_MPS_BIT >>= 1;
            break;
        }
        case 0xFD: // 取反   INV
        {
            PLC_ACC_BIT = (PLC_ACC_BIT & 0xfe) | (~PLC_ACC_BIT & 0x01);
            break;
        }
        case 0xFF: //取反   POP
        {
            break;
        }
        default:
        {
            PLC_PROG_ERROR(M8064,02);
            break;
        }
    }
}

// 2019年11月优化、新增
static void PLS(void)              //M1536~M3071位PLS指令函数
{
    if(PLC_ACC_BIT&0x01)
    {
        if(PLC_PL_BIT_TEST(PLC_Addr-PLC_LAD_START_ADDR)==0)
        {
            PLC_PL_BIT_ON(PLC_Addr-PLC_LAD_START_ADDR);
            PLC_BIT_ON((0x2fff&*PLC_Addr));
        }
        else {
            PLC_BIT_OFF((0x2fff&*PLC_Addr));
        }
    }
    else {
        PLC_PL_BIT_OFF(PLC_Addr-PLC_LAD_START_ADDR);
    }
    PLC_Addr++;
}

// 2019年11月优化、新增
static void PLF(void)              //M1536~M3071位PLF指令函数
{
    if(PLC_ACC_BIT&0x01)
    {
        if(PLC_PL_BIT_TEST(PLC_Addr-PLC_LAD_START_ADDR)==0)
        {
            PLC_PL_BIT_ON(PLC_Addr-PLC_LAD_START_ADDR);
        }
    }
    else
    {
        if(PLC_PL_BIT_TEST(PLC_Addr-PLC_LAD_START_ADDR))
        {
            PLC_PL_BIT_OFF(PLC_Addr-PLC_LAD_START_ADDR);
            PLC_BIT_ON((0x2fff&*PLC_Addr));
        }
        else {
            PLC_BIT_OFF((0x2fff&*PLC_Addr));
        }
    }
    PLC_Addr++;
}

// kevin，20170708注：有可能有问题？？？
static void RESET_T(uint8_t process_addr) //定时器复位
{
    if(PLC_ACC_BIT&0x01)//当前值是否有效
    {
        PLC_BIT_OFF(COIL_T_OVERFLOW_ADDR + process_addr);//溢出线圈

        PLC_BIT_OFF(COIL_T_ENABLE_ADDR + process_addr);//使能线圈 0x1600？
        PLC_BIT_ON(COIL_T_RESET_ADDR + process_addr); //复位线圈 0x3800？

        plc_16BitBuf[HELON_REG_T_START_ADDR + process_addr]=0;//实际计数即当前值
    }
    else
    {
        PLC_BIT_OFF(COIL_T_RESET_ADDR + process_addr);//复位线圈 0x3800？
    }
}


// kevin，20170423
static void RESET_C(uint8_t process_addr) //定时器复位
{
    static uint16_t *p_data;
    if((PLC_ACC_BIT&0x01)==0x01) //当前值是否有效
    {
        if((process_addr >= 200) && (process_addr <= 255))
        {
            /* kevin，20170423新增，编码器 */
#if ENCODE_FUNC ==1
            if(process_addr == 236)
            {
                if(S_Cap_Pulse_Pro[0].used==1)
                {
                    Encoder_A0_Restore();
                    C236 =0;
                    S_Cap_Pulse_Pro[0].used=0;
                }
            }
            else if(process_addr == 239)
            {
                if(S_Cap_Pulse_Pro[1].used==1)
                {
                    Encoder_A1_Restore();
                    C239 =0;
                    S_Cap_Pulse_Pro[1].used=0;
                }
            }
            else if(process_addr == 251)
            {
                if(S_Cap_Pulse_Pro[0].used==1)
                {
                    Encoder_AB0_Restore();
                    S_Cap_Pulse_Pro[0].used=0;
                }
            }
            else if(process_addr == 253)
            {
                if(S_Cap_Pulse_Pro[1].used==1)
                {
                    Encoder_AB1_Restore();
                    S_Cap_Pulse_Pro[1].used=0;
                }
            }
//			else
#endif /* #if ENCODE_FUNC ==1	*/
//			{
            p_data = plc_16BitBuf + HELON_REG_C_START_ADDR + process_addr; //指向值地址
            *p_data =0; //清零地址 高位
            p_data +=1; //因为是32位
            *p_data =0; //清零地址 低位
            //TODO BUG ox00E0 不是C的位地址，应该为COIL_C_OVERFLOW_ADDR kevin
            PLC_BIT_OFF( COIL_C_OVERFLOW_ADDR + process_addr); //指向溢出线圈并清溢出线圈 bug fix 0x00e0-->0x0e00 by kevin
//			}
        }
        else
        {
            p_data= plc_16BitBuf + HELON_REG_C_START_ADDR + process_addr; //指向值地址
            *p_data=0; //清零地址
            //TODO BUG ox00E0 不是C的位地址，应该为COIL_C_OVERFLOW_ADDR kevin
            PLC_BIT_OFF(COIL_C_OVERFLOW_ADDR+process_addr); //指向溢出线圈并清溢出线圈 bug fix 0x00e0-->0x0e00 by kevin
        }
    }
    OUT(COIL_C_RESET_ADDR+process_addr);   //
}

static void RST_T_C(void)                            //所有T_C位RST指令函数
{
    switch(*PLC_Addr/0x100)
    {
        case 0x86:
            RESET_T(*PLC_Addr),PLC_Addr++;
            break;//复位T
        case 0x8E:
            RESET_C(*PLC_Addr),PLC_Addr++;
            break;//复位C
    }
}


static void MOV_TO_K_H(uint8_t i,uint32_t data,uint8_t addr)//进行 MOV ?? K?X&Y&S&M 地址计算
{
    uint8_t LL_BIT;                                 //需要向左移动多少位用寄存器
    uint16_t JOB_ADDR;
    int64_t MOV_DATA_64BIT,MOV_DATA_64BIT_BACKUP,MOV_DATA_BACKUP1;  //移动32位数据
    mov_d_addr |=addr<<8;
    mov_d_addr +=Transfer;                      //成批传递和多点时 Transfer会有数据平时都是0
    LL_BIT =mov_d_addr%0x20;			        //需要移动多少位
    JOB_ADDR =(mov_d_addr/0x20)*4;              //所在的起始地址
    JOB_ADDR= address_bit_mapping(JOB_ADDR);        //address mapping by kevin
    switch(i)
    {   //移动位数,需要的数据做好处理,将数据移动需要的位数,后面要将数据取反用
        case 0x82: // 传送K1即4位
        {
            MOV_DATA_64BIT_BACKUP =data&0X0000000F;
            MOV_DATA_64BIT_BACKUP <<=LL_BIT;
            MOV_DATA_BACKUP1 =~(0X0000000F<<LL_BIT);
            break;
        }
        case 0x84: // 传送K2即8位
        {
            MOV_DATA_64BIT_BACKUP =data&0X000000FF;
            MOV_DATA_64BIT_BACKUP <<=LL_BIT;
            MOV_DATA_BACKUP1 =~(0X000000FF<<LL_BIT);
            break;
        }
        case 0x86: // 传送K3即12位
        {
            MOV_DATA_64BIT_BACKUP=data&0X00000FFF;
            MOV_DATA_64BIT_BACKUP<<=LL_BIT;
            MOV_DATA_BACKUP1=~(0X00000FFF<<LL_BIT);
            break;
        }
        case 0x88: // 传送K4即16位
        {
            MOV_DATA_64BIT_BACKUP =data&0X0000FFFF;
            MOV_DATA_64BIT_BACKUP <<=LL_BIT;
            MOV_DATA_BACKUP1 =~(0X0000FFFF<<LL_BIT);
            break;
        }
        case 0x8A: //传送K5即20位
        {
            MOV_DATA_64BIT_BACKUP=data&0X000FFFFF;
            MOV_DATA_64BIT_BACKUP<<=LL_BIT;
            MOV_DATA_BACKUP1=~(0X000FFFFF<<LL_BIT);
            break;
        }
        case 0x8C: //传送K6即24位
        {
            MOV_DATA_64BIT_BACKUP=data&0X00FFFFFF;
            MOV_DATA_64BIT_BACKUP<<=LL_BIT;
            MOV_DATA_BACKUP1=~(0X00FFFFFF<<LL_BIT);
            break;
        }
        case 0x8E: //传送K7即28位
        {
            MOV_DATA_64BIT_BACKUP=data&0X0FFFFFFF;
            MOV_DATA_64BIT_BACKUP<<=LL_BIT;
            MOV_DATA_BACKUP1=~(0X0FFFFFFF<<LL_BIT);
            break;
        }
        case 0x90: //传送K8即32位
        {
            MOV_DATA_64BIT_BACKUP=data&0XFFFFFFFF;
            MOV_DATA_64BIT_BACKUP<<=LL_BIT;
            MOV_DATA_BACKUP1=~(0XFFFFFFFF<<LL_BIT);
            break;
        }
        default:
        {
            PLC_Addr+=3;
            break;  //遇到不支持的命令
        }
    }
//    MOV_DATA_64BIT=PLC_RAM64(RAM_ADDR+JOB_ADDR);
    MOV_DATA_64BIT=PLC_R_RAM_64BIT(JOB_ADDR);
    MOV_DATA_64BIT&=MOV_DATA_BACKUP1;             //将需要传送的位置清空
    MOV_DATA_64BIT|=MOV_DATA_64BIT_BACKUP;        //传入需要到的位置
//    PLC_RAM64(RAM_ADDR+JOB_ADDR)=MOV_DATA_64BIT;  //将数据传到目标位置
    PLC_W_RAM_64BIT(JOB_ADDR,MOV_DATA_64BIT);  //将数据传到目标位置
}


static signed int MOV_K(uint8_t Addr)	              //进行K?X&Y&S&M计算
{
    static uint16_t LL_BIT,JOB_ADDR;                   //需要向左移动多少位用寄存器
    static uint64_t MOV_DATA_64BIT;               //移动64位数据
    mov_d_addr|=(Addr<<8);
    mov_d_addr+=Transfer;                         //成批传递和多点时 Transfer会有数据平时都是0
    LL_BIT=mov_d_addr%0x20;						            //需要移动多少位
    JOB_ADDR=(mov_d_addr/0x20)*4;                 //所在的起始地址
    JOB_ADDR= address_bit_mapping(JOB_ADDR);        //address mapping by kevin
//    MOV_DATA_64BIT=PLC_RAM64(RAM_ADDR+JOB_ADDR), //change by kevin
    MOV_DATA_64BIT=PLC_R_RAM_64BIT(JOB_ADDR),         // new by kevin
            MOV_DATA_64BIT>>=LL_BIT;
    return  (signed int)MOV_DATA_64BIT;
}

//计算出高位地址
uint16_t D_C_T_addr(uint8_t l_value)
{
    static uint16_t temp;
    switch(*PLC_Addr/0x100)
    {
        case 0x80: //大于等于D8000
        {
            temp=l_value+((*PLC_Addr%0x100)*0x100),temp= HELON_REG_D8000_START_ADDR + temp / 2,PLC_Addr++;
            break;
        }
        case 0x82: //算出T的地址，将地址变为值
        {
            temp=l_value+((*PLC_Addr%0x100)*0x100),temp= HELON_REG_T_START_ADDR + temp / 2,PLC_Addr++;
            break;
        }
        case 0x84: //算出C的地址，将地址变为值
        {
            temp=l_value+((*PLC_Addr%0x100)*0x100),temp= HELON_REG_C_START_ADDR + temp / 2,PLC_Addr++;
            break;
        }
        case 0x86: //算出D的地址，将地址变为值
        {
            temp=l_value+((*PLC_Addr%0x100)*0x100),temp= HELON_REG_D_START_ADDR + temp / 2,PLC_Addr++;
            break;
        }
        case 0x88: //大于等于D1000
        {
            temp=l_value+((*PLC_Addr%0x100)*0x100),temp= HELON_REG_D_START_ADDR + temp / 2 + 1000,PLC_Addr++;
            break;
        }
    }
    return temp;
}

//=======================================================================================================
// 函数名称:  static uint16_t addr_value(void)
// 功能描述： 计算PLC地址或k的实数
// 输　入:  void
// 输　出:  地址或数据
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2015年6月2日
// 备  注:
//-------------------------------------------------------------------------------------------------------
// 修改人:
// 日　期:
// 备  注: 优化程序数量测试中部分指令测试  优化后返回地址 k是数据
//-------------------------------------------------------------------------------------------------------
//=======================================================================================================
static uint16_t addr_value(void)
{
    static uint8_t temp;
    static uint16_t temp1;
    switch(*PLC_Addr/0x100)
    {
        case 0x80: //算出K值
        case 0x82: //算出H值
        {   // kevin，20190528拓宽应用
            temp=*PLC_Addr;
            PLC_Addr++;
            temp1=*PLC_Addr<<8|temp;
            PLC_Addr++;
            break;
        }
        case 0x84:
        {
            temp=*PLC_Addr;
            PLC_Addr++;
            temp1=*PLC_Addr<<8|temp;
            PLC_Addr++;
            Flag_bit=0;
            break;//进行如 K4M0 之类的传送
        }
        case 0x86:
        {
            temp=*PLC_Addr;
            PLC_Addr++;
            temp1=D_C_T_addr(temp);
            break;//算出D、C、T的地址
        }
    }
    return temp1;
}

//=======================================================================================================
// 函数名称:  static uint32_t addr_value_prog(void)
// 功能描述： 计算PLC地址或k的实数
// 输　入:  void
// 输　出:  地址或数据
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2015年6月23日
// 备  注:
//-------------------------------------------------------------------------------------------------------
// 修改人:
// 日　期:
// 备  注:
//=======================================================================================================
static uint32_t addr_value_prog(void)
{
    static uint32_t temp;
    uint16_t Type_F,temp2,Data1,Data2;
    Data1=*PLC_Addr;
    PLC_Addr++;
    Data2=*PLC_Addr;
    temp2=Type_F=0;
    Type_F   = (Data1 & 0xff00);
    Type_F  |= (Data2 >> 8);

    temp2  = (Data2 << 8);
    temp2 |=mov_d_addr=(uint8_t)Data1;

    if(Type_F == 0x8680)      temp=RAM_D8000_ADDR+temp2,                  PLC_Addr++;//算出D的地址 D8000
    else if(Type_F == 0x8682) temp=RAM_T_ADDR+temp2,                      PLC_Addr++;//算出T的地址
    else if(Type_F == 0x8684) temp=RAM_C_ADDR+temp2,                      PLC_Addr++;//算出C的地址
    else if(Type_F == 0x8686) temp=RAM_D_ADDR+temp2,                      PLC_Addr++;//算出D的地址
    else if(Type_F == 0x8688) temp=RAM_D1000_ADDR+temp2,                  PLC_Addr++;//大于等于D1000
    else if(Type_F == 0x8482) temp=MOV_K(*PLC_Addr)&0X0000000F,Flag_bit=0,PLC_Addr++;//进行如 K4M0 之类的传送
    else if(Type_F == 0x8484) temp=MOV_K(*PLC_Addr)&0X000000FF,Flag_bit=0,PLC_Addr++;//进行如 K4M0 之类的传送
    else if(Type_F == 0x8486) temp=MOV_K(*PLC_Addr)&0X00000FFF,Flag_bit=0,PLC_Addr++;//进行如 K4M0 之类的传送
    else if(Type_F == 0x8488) temp=MOV_K(*PLC_Addr)&0X0000FFFF,Flag_bit=0,PLC_Addr++;//进行如 K4M0 之类的传送
    else if(Type_F == 0x848A) temp=MOV_K(*PLC_Addr)&0X000FFFFF,Flag_bit=0,PLC_Addr++;//进行如 K4M0 之类的传送
    else if(Type_F == 0x848C) temp=MOV_K(*PLC_Addr)&0X00FFFFFF,Flag_bit=0,PLC_Addr++;//进行如 K4M0 之类的传送
    else if(Type_F == 0x848E) temp=MOV_K(*PLC_Addr)&0X0FFFFFFF,Flag_bit=0,PLC_Addr++;//进行如 K4M0 之类的传送
    else if(Type_F == 0x8490) temp=MOV_K(*PLC_Addr),           Flag_bit=0,PLC_Addr++;//进行如 K4M0 之类的传送
    return temp;
}

unsigned short V0_V3(uint16_t temp1)
{
    uint8_t temp=PLC_v_z_addr(temp1);
    if(temp==0)       return D8029;
    else if(temp==1)  return D8183;
    else if(temp==2)  return D8185;
    else if(temp==3)  return D8187;
    else	return 0;
}

unsigned short V4_V7(uint16_t temp1)
{
    uint8_t temp=PLC_v_z_addr(temp1);
    if(temp==0)       return D8189;
    else if(temp==1)  return D8191;
    else if(temp==2)  return D8193;
    else if(temp==3)  return D8195;
    else	return 0;
}

unsigned short Z0_Z3(uint16_t temp1)
{
    uint8_t temp=PLC_v_z_addr(temp1);
    if(temp==0)       return D8028;
    else if(temp==1)  return D8182;
    else if(temp==2)  return D8184;
    else if(temp==3)  return D8186;
    else	return 0;
}

unsigned short Z4_Z7(uint16_t temp1)
{
    uint8_t temp=PLC_v_z_addr(temp1);
    if(temp==0)       return D8188;
    else if(temp==1)  return D8190;
    else if(temp==2)  return D8192;
    else if(temp==3)  return D8194;
    else	return 0;
}

unsigned int DZ0_Z3(uint16_t temp1)
{
    uint8_t temp=PLC_v_z_addr(temp1);
    if(temp==0)       return D8028+D8029*0X10000;
    else if(temp==1)  return D8182+D8183*0X10000;
    else if(temp==2)  return D8184+D8184*0X10000;
    else if(temp==3)  return D8186+D8185*0X10000;
    else	return 0;
}

unsigned short DZ4_Z7(uint16_t temp1)
{
    uint8_t temp=PLC_v_z_addr(temp1);
    if(temp==0)       return D8188+D8189*0X10000;
    else if(temp==1)  return D8190+D8191*0X10000;
    else if(temp==2)  return D8192+D8193*0X10000;
    else if(temp==3)  return D8194+D8193*0X10000;
    else	return 0;
}
//=======================================================================================================
// 函数名称:  static void target(void)
// 功能描述： 加减法“与”“或”“异或”共用赋值函数	如DMOV、DADD、DSUB等指令的结果传递出去
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2015年6月23日
// 备  注:
//=======================================================================================================
static void D_target(void)
{
    uint16_t Type_F,temp2,Data1,Data2;
    Data1=*PLC_Addr;
    PLC_Addr++;
    Data2=*PLC_Addr;
    temp2=Type_F=0;
    Type_F   = (Data1 & 0xff00);
    Type_F  |= (Data2 >> 8);
    /************************************/
    temp2  = (Data2 << 8);
    temp2 |=mov_d_addr=(uint8_t)Data1;
    /************************************/
    switch(Type_F)
    {
        case 0x8480:
            MOV_TO_K_H(Type_F,trade,*PLC_Addr),PLC_Addr++,Transfer_bit=1;
            break; //进行如 K4M0 之类的传送
        case 0x8482:
            MOV_TO_K_H(Type_F,trade,*PLC_Addr),PLC_Addr++,Transfer_bit=1;
            break; //进行如 K4M0 之类的传送
        case 0x8484:
            MOV_TO_K_H(Type_F,trade,*PLC_Addr),PLC_Addr++,Transfer_bit=1;
            break; //进行如 K4M0 之类的传送
        case 0x8486:
            MOV_TO_K_H(Type_F,trade,*PLC_Addr),PLC_Addr++,Transfer_bit=1;
            break; //进行如 K4M0 之类的传送
        case 0x8488:
            MOV_TO_K_H(Type_F,trade,*PLC_Addr),PLC_Addr++,Transfer_bit=1;
            break; //进行如 K4M0 之类的传送
        case 0x848A:
            MOV_TO_K_H(Type_F,trade,*PLC_Addr),PLC_Addr++,Transfer_bit=1;
            break; //进行如 K4M0 之类的传送
        case 0x848C:
            MOV_TO_K_H(Type_F,trade,*PLC_Addr),PLC_Addr++,Transfer_bit=1;
            break; //进行如 K4M0 之类的传送
        case 0x848E:
            MOV_TO_K_H(Type_F,trade,*PLC_Addr),PLC_Addr++,Transfer_bit=1;
            break; //进行如 K4M0 之类的传送
        case 0x8490:
            MOV_TO_K_H(Type_F,trade,*PLC_Addr),PLC_Addr++,Transfer_bit=1;
            break; //进行如 K4M0 之类的传送

        case 0x8680:
            PLC_W_RAM_32BIT((RAM_D8000_ADDR+temp2+Transfer),trade),PLC_Addr++;
            break; //算出D的地址 D8000
        case 0x8682:
            PLC_W_RAM_32BIT((RAM_T_ADDR+temp2+Transfer),trade),PLC_Addr++;
            break; //算出T的地址
        case 0x8684:
            PLC_W_RAM_32BIT((RAM_C_ADDR+temp2+Transfer),trade),PLC_Addr++;
            break; //算出C的地址
        case 0x8686:
            PLC_W_RAM_32BIT((RAM_D_ADDR+temp2+Transfer),trade),PLC_Addr++;
            break; //算出D的地址
        case 0x8688:
            PLC_W_RAM_32BIT((RAM_D1000_ADDR+temp2+Transfer),trade),PLC_Addr++;
            break; //大于等于D1000
            /************************************************K1M0寄存器"Z"*******************************************************************/
        case 0xA482:
            mov_d_addr=+DZ0_Z3(temp2),MOV_TO_K_H(Type_F,trade,PLC_D_C_T_addr(temp2)/0x100),PLC_Addr++;
            break;//K1M0的V0-V3
        case 0xA483:
            mov_d_addr=+DZ4_Z7(temp2),MOV_TO_K_H(Type_F,trade,PLC_D_C_T_addr(temp2)/0x100),PLC_Addr++;
            break;//K1M0的V4-V7
            /************************************************K2M0寄存器"Z"*******************************************************************/
        case 0xA484:
            mov_d_addr=+DZ0_Z3(temp2),MOV_TO_K_H(Type_F,trade,PLC_D_C_T_addr(temp2)/0x100),PLC_Addr++;
            break;//K2M0的V0-V3
        case 0xA485:
            mov_d_addr=+DZ4_Z7(temp2),MOV_TO_K_H(Type_F,trade,PLC_D_C_T_addr(temp2)/0x100),PLC_Addr++;
            break;//K2M0的V4-V7
            /************************************************K3M0寄存器"Z"*******************************************************************/
        case 0xA486:
            mov_d_addr=+DZ0_Z3(temp2),MOV_TO_K_H(Type_F,trade,PLC_D_C_T_addr(temp2)/0x100),PLC_Addr++;
            break;//K3M0的V0-V3
        case 0xA487:
            mov_d_addr=+DZ4_Z7(temp2),MOV_TO_K_H(Type_F,trade,PLC_D_C_T_addr(temp2)/0x100),PLC_Addr++;
            break;//K3M0的V4-V7
            /************************************************K4M0寄存器"Z"*******************************************************************/
        case 0xA488:
            mov_d_addr=+DZ0_Z3(temp2),MOV_TO_K_H(Type_F,trade,PLC_D_C_T_addr(temp2)/0x100),PLC_Addr++;
            break;//K4M0的V0-V3
        case 0xA489:
            mov_d_addr=+DZ4_Z7(temp2),MOV_TO_K_H(Type_F,trade,PLC_D_C_T_addr(temp2)/0x100),PLC_Addr++;
            break;//K4M0的V4-V7
            /************************************************K5M0寄存器"Z"*******************************************************************/
        case 0xA48A:
            mov_d_addr=+DZ0_Z3(temp2),MOV_TO_K_H(Type_F,trade,PLC_D_C_T_addr(temp2)/0x100),PLC_Addr++;
            break;//K5M0的V0-V3
        case 0xA48B:
            mov_d_addr=+DZ4_Z7(temp2),MOV_TO_K_H(Type_F,trade,PLC_D_C_T_addr(temp2)/0x100),PLC_Addr++;
            break;//K5M0的V4-V7
            /************************************************K6M0寄存器"Z"*******************************************************************/
        case 0xA48C:
            mov_d_addr=+DZ0_Z3(temp2),MOV_TO_K_H(Type_F,trade,PLC_D_C_T_addr(temp2)/0x100),PLC_Addr++;
            break;//K6M0的V0-V3
        case 0xA48D:
            mov_d_addr=+DZ4_Z7(temp2),MOV_TO_K_H(Type_F,trade,PLC_D_C_T_addr(temp2)/0x100),PLC_Addr++;
            break;//K6M0的V4-V7
            /************************************************K7M0寄存器"Z"*******************************************************************/
        case 0xA48E:
            mov_d_addr=+DZ0_Z3(temp2),MOV_TO_K_H(Type_F,trade,PLC_D_C_T_addr(temp2)/0x100),PLC_Addr++;
            break;//K7M0的V0-V3
        case 0xA48F:
            mov_d_addr=+DZ4_Z7(temp2),MOV_TO_K_H(Type_F,trade,PLC_D_C_T_addr(temp2)/0x100),PLC_Addr++;
            break;//K7M0的V4-V7
            /************************************************K8M0寄存器"Z"*******************************************************************/
        case 0xA490:
            mov_d_addr=+DZ0_Z3(temp2),MOV_TO_K_H(Type_F,trade,PLC_D_C_T_addr(temp2)/0x100),PLC_Addr++;
            break;//k8M0的V0-V4
        case 0xA491:
            mov_d_addr=+DZ4_Z7(temp2),MOV_TO_K_H(Type_F,trade,PLC_D_C_T_addr(temp2)/0x100),PLC_Addr++;
            break;//K8M0的V4-V7
            /************************************************T寄存器"Z"*******************************************************************/
        case 0xA682:
            PLC_W_RAM_32BIT((RAM_T_ADDR+PLC_D_C_T_addr(temp2)+DZ0_Z3(temp2)*2),(uint32_t)trade),PLC_Addr++;
            break;//算出T的地址，的Z0-Z3
        case 0xA683:
            PLC_W_RAM_32BIT((RAM_T_ADDR+PLC_D_C_T_addr(temp2)+DZ4_Z7(temp2)*2),(uint32_t)trade),PLC_Addr++;
            break;//算出T的地址，的Z4-Z7
            /************************************************C寄存器"Z"*******************************************************************/
        case 0xA684:
            PLC_W_RAM_32BIT((RAM_C_ADDR+PLC_D_C_T_addr(temp2)+DZ0_Z3(temp2)*2),(uint32_t)trade);
            PLC_Addr++;
            break;//算出C的地址，的Z0-Z3
        case 0xA685:
            PLC_W_RAM_32BIT((RAM_C_ADDR+PLC_D_C_T_addr(temp2)+DZ4_Z7(temp2)*2),(uint32_t)trade),PLC_Addr++;
            break;//算出C的地址，的Z4-Z7
            /************************************************D寄存器"Z"*******************************************************************/
        case 0xA686:
            PLC_W_RAM_32BIT((RAM_D_ADDR+PLC_D_C_T_addr(temp2)+DZ0_Z3(temp2)*2),(uint32_t)trade),PLC_Addr++;
            break;//算出D的地址，的Z0-Z3
        case 0xA687:
            PLC_W_RAM_32BIT((RAM_D_ADDR+PLC_D_C_T_addr(temp2)+DZ4_Z7(temp2)*2),(uint32_t)trade),PLC_Addr++;
            break;//算出D的地址，的Z4-Z7
        case 0xA688:
            PLC_W_RAM_32BIT((RAM_D1000_ADDR+PLC_D_C_T_addr(temp2)+DZ0_Z3(temp2)*2),(uint32_t)trade),PLC_Addr++;
            break;//大于等于D1000的Z0-Z3
        case 0xA689:
            PLC_W_RAM_32BIT((RAM_D1000_ADDR+PLC_D_C_T_addr(temp2)+DZ4_Z7(temp2)*2),(uint32_t)trade),PLC_Addr++;
            break;//大于等于D1000的Z4-Z7
    }
    PLC_Addr+=2;
}

//=======================================================================================================
// 函数名称:  static void target(void)
// 功能描述： 加减法“与”“或”“异或”共用赋值函数	如MOV、ADD、SUB等指令的结果传递出去
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2015年6月23日
// 备  注:
//=======================================================================================================
static void target(void)
{
    uint16_t Type_F,temp2,Data1,Data2;
    Data1 =*PLC_Addr;
    PLC_Addr++;
    Data2 =*PLC_Addr;

    temp2=Type_F=0;
    Type_F  = (Data1 & 0xff00);
    Type_F  |= (Data2 >> 8);
    /************************************/
    temp2  = (Data2 << 8);
    temp2 |=mov_d_addr=(uint8_t)Data1;
    /************************************/
    switch(Type_F)
    {
        /* 进行如 K4M0 之类的传送 */
        case  0x8482:
        case  0x8484:
        case  0x8486:
        case  0x8488:
        {
            MOV_TO_K_H(Type_F,(uint16_t)trade,*PLC_Addr);
            PLC_Addr++;
            Transfer_bit=1;
            break;
        }
//      case  0x8482: MOV_TO_K_H(Type_F,(uint16_t)trade,*PLC_Addr),PLC_Addr++,Transfer_bit=1;break; //进行如 K4M0 之类的传送
//		case  0x8484: MOV_TO_K_H(Type_F,(uint16_t)trade,*PLC_Addr),PLC_Addr++,Transfer_bit=1;break; //进行如 K4M0 之类的传送
//		case  0x8486: MOV_TO_K_H(Type_F,(uint16_t)trade,*PLC_Addr),PLC_Addr++,Transfer_bit=1;break; //进行如 K4M0 之类的传送
//		case  0x8488: MOV_TO_K_H(Type_F,(uint16_t)trade,*PLC_Addr),PLC_Addr++,Transfer_bit=1;break; //进行如 K4M0 之类的传送

        case  0x8680:
            PLC_W_RAM_16BIT(RAM_D8000_ADDR+temp2+Transfer,(uint16_t)trade),PLC_Addr++;
            break; //算出D的地址 D8000
        case  0x8682:
            PLC_W_RAM_16BIT(RAM_T_ADDR+temp2+Transfer,(uint16_t)trade),PLC_Addr++;
            break; //算出T的地址
        case  0x8684:
            PLC_W_RAM_16BIT(RAM_C_ADDR +temp2+Transfer,(uint16_t)trade),PLC_Addr++;
            break; //算出C的地址
        case  0x8686: //算出D的地址
        {
            PLC_W_RAM_16BIT(RAM_D_ADDR +temp2+Transfer,(uint16_t)trade);
            PLC_Addr++;
            break;
        }
        case  0x8688:
            PLC_W_RAM_16BIT(RAM_D1000_ADDR +temp2+Transfer,(uint16_t)trade),PLC_Addr++;
            break; //大于等于D1000
            /************************************************K1M0寄存器"V"*******************************************************************/
        case  0x9482:
            mov_d_addr=+V0_V3(temp2),MOV_TO_K_H(Type_F,(uint16_t)trade,PLC_D_C_T_addr(temp2)/0x100),PLC_Addr++;
            break;//K1M0的V0-V3
        case  0x9483:
            mov_d_addr=+V4_V7(temp2),MOV_TO_K_H(Type_F,(uint16_t)trade,PLC_D_C_T_addr(temp2)/0x100),PLC_Addr++;
            break;//K1M0的V4-V7
            /************************************************K2M0寄存器"V"*******************************************************************/
        case  0x9484:
            mov_d_addr=+V0_V3(temp2),MOV_TO_K_H(Type_F,(uint16_t)trade,PLC_D_C_T_addr(temp2)/0x100),PLC_Addr++;
            break;//K2M0的V0-V3
        case  0x9485:
            mov_d_addr=+V4_V7(temp2),MOV_TO_K_H(Type_F,(uint16_t)trade,PLC_D_C_T_addr(temp2)/0x100),PLC_Addr++;
            break;//K2M0的V4-V7
            /************************************************K3M0寄存器"V"*******************************************************************/
        case  0x9486:
            mov_d_addr=+V0_V3(temp2),MOV_TO_K_H(Type_F,(uint16_t)trade,PLC_D_C_T_addr(temp2)/0x100),PLC_Addr++;
            break;//K3M0的V0-V3
        case  0x9487:
            mov_d_addr=+V4_V7(temp2),MOV_TO_K_H(Type_F,(uint16_t)trade,PLC_D_C_T_addr(temp2)/0x100),PLC_Addr++;
            break;//K3M0的V4-V7
            /************************************************K4M0寄存器"V"*******************************************************************/
        case  0x9488:
            mov_d_addr=+V0_V3(temp2),MOV_TO_K_H(Type_F,(uint16_t)trade,PLC_D_C_T_addr(temp2)/0x100),PLC_Addr++;
            break;//K4M0的V0-V3
        case  0x9489:
            mov_d_addr=+V4_V7(temp2),MOV_TO_K_H(Type_F,(uint16_t)trade,PLC_D_C_T_addr(temp2)/0x100),PLC_Addr++;
            break;//K4M0的V4-V7
            /************************************************T寄存器"V"*******************************************************************/
        case  0x9682:
            PLC_W_RAM_16BIT((RAM_T_ADDR+PLC_D_C_T_addr(temp2)+V0_V3(temp2)*2),(uint16_t)trade),PLC_Addr++;
            break;//算出T的地址，的V0-V3
        case  0x9683:
            PLC_W_RAM_16BIT((RAM_T_ADDR+PLC_D_C_T_addr(temp2)+V4_V7(temp2)*2),(uint16_t)trade),PLC_Addr++;
            break;//算出T的地址，的V4-V7
            /************************************************C寄存器"V"*******************************************************************/
        case  0x9684:
            PLC_W_RAM_16BIT((RAM_C_ADDR+PLC_D_C_T_addr(temp2)+V0_V3(temp2)*2),(uint16_t)trade),PLC_Addr++;
            break;//算出C的地址，的V0-V3
        case  0x9685:
            PLC_W_RAM_16BIT((RAM_C_ADDR+PLC_D_C_T_addr(temp2)+V4_V7(temp2)*2),(uint16_t)trade),PLC_Addr++;
            break;//算出C的地址，的V4-V7
            /************************************************D寄存器"V"*******************************************************************/
        case  0x9686:
            PLC_W_RAM_16BIT((RAM_D_ADDR+PLC_D_C_T_addr(temp2)+V0_V3(temp2)*2),(uint16_t)trade),PLC_Addr++;
            break;//算出D的地址，的V0-V3
        case  0x9687:
            PLC_W_RAM_16BIT((RAM_D_ADDR+PLC_D_C_T_addr(temp2)+V4_V7(temp2)*2),(uint16_t)trade),PLC_Addr++;
            break;//算出D的地址，的V4-V7
        case  0x9688:
            PLC_W_RAM_16BIT((RAM_D1000_ADDR+PLC_D_C_T_addr(temp2)+V0_V3(temp2)*2),(uint16_t)trade),PLC_Addr++;
            break;//大于等于D1000的V0-V3
        case  0x9689:
            PLC_W_RAM_16BIT((RAM_D1000_ADDR+PLC_D_C_T_addr(temp2)+V4_V7(temp2)*2),(uint16_t)trade),PLC_Addr++;
            break;//大于等于D1000的V4-V7

            /************************************************K1M0寄存器"Z"*******************************************************************/
        case  0xA482:
            mov_d_addr=+Z0_Z3(temp2),MOV_TO_K_H(Type_F,(uint16_t)trade,PLC_D_C_T_addr(temp2)/0x100),PLC_Addr++;
            break;//K1M0的V0-V3
        case  0xA483:
            mov_d_addr=+Z4_Z7(temp2),MOV_TO_K_H(Type_F,(uint16_t)trade,PLC_D_C_T_addr(temp2)/0x100),PLC_Addr++;
            break;//K1M0的V4-V7
            /************************************************K2M0寄存器"Z"*******************************************************************/
        case  0xA484:
            mov_d_addr=+Z0_Z3(temp2),MOV_TO_K_H(Type_F,(uint16_t)trade,PLC_D_C_T_addr(temp2)/0x100),PLC_Addr++;
            break;//K2M0的V0-V3
        case  0xA485:
            mov_d_addr=+Z4_Z7(temp2),MOV_TO_K_H(Type_F,(uint16_t)trade,PLC_D_C_T_addr(temp2)/0x100),PLC_Addr++;
            break;//K2M0的V4-V7
            /************************************************K3M0寄存器"Z"*******************************************************************/
        case  0xA486:
            mov_d_addr=+Z0_Z3(temp2),MOV_TO_K_H(Type_F,(uint16_t)trade,PLC_D_C_T_addr(temp2)/0x100),PLC_Addr++;
            break;//K3M0的V0-V3
        case  0xA487:
            mov_d_addr=+Z4_Z7(temp2),MOV_TO_K_H(Type_F,(uint16_t)trade,PLC_D_C_T_addr(temp2)/0x100),PLC_Addr++;
            break;//K3M0的V4-V7
            /************************************************K4M0寄存器"Z"*******************************************************************/
        case  0xA488:
            mov_d_addr=+Z0_Z3(temp2),MOV_TO_K_H(Type_F,(uint16_t)trade,PLC_D_C_T_addr(temp2)/0x100),PLC_Addr++;
            break;//K4M0的V0-V3
        case  0xA489:
            mov_d_addr=+Z4_Z7(temp2),MOV_TO_K_H(Type_F,(uint16_t)trade,PLC_D_C_T_addr(temp2)/0x100),PLC_Addr++;
            break;//K4M0的V4-V7
            /************************************************T寄存器"Z"*******************************************************************/
        case  0xA682:
            PLC_W_RAM_16BIT((RAM_T_ADDR+PLC_D_C_T_addr(temp2)+Z0_Z3(temp2)*2),(uint16_t)trade),PLC_Addr++;
            break;//算出C的地址，的Z0-Z3
        case  0xA683:
            PLC_W_RAM_16BIT((RAM_T_ADDR+PLC_D_C_T_addr(temp2)+Z4_Z7(temp2)*2),(uint16_t)trade),PLC_Addr++;
            break;//算出C的地址，的Z4-Z7
            /************************************************C寄存器"Z"*******************************************************************/
        case  0xA684:
            PLC_W_RAM_16BIT((RAM_C_ADDR+PLC_D_C_T_addr(temp2)+Z0_Z3(temp2)*2),(uint16_t)trade),PLC_Addr++;
            break;//算出C的地址，的Z0-Z3
        case  0xA685:
            PLC_W_RAM_16BIT((RAM_C_ADDR+PLC_D_C_T_addr(temp2)+Z4_Z7(temp2)*2),(uint16_t)trade),PLC_Addr++;
            break;//算出C的地址，的Z4-Z7
            /************************************************D寄存器"Z"*******************************************************************/
        case  0xA686:
            PLC_W_RAM_16BIT((RAM_D_ADDR+PLC_D_C_T_addr(temp2)+Z0_Z3(temp2)*2),(uint16_t)trade),PLC_Addr++;
            break;//算出D的地址，的Z0-Z3
        case  0xA687:
            PLC_W_RAM_16BIT((RAM_D_ADDR+PLC_D_C_T_addr(temp2)+Z4_Z7(temp2)*2),(uint16_t)trade),PLC_Addr++;
            break;//算出D的地址，的Z4-Z7
        case  0xA688:
            PLC_W_RAM_16BIT((RAM_D1000_ADDR+PLC_D_C_T_addr(temp2)+Z0_Z3(temp2)*2),(uint16_t)trade),PLC_Addr++;
            break;//大于等于D1000的Z0-Z3
        case  0xA689:
            PLC_W_RAM_16BIT((RAM_D1000_ADDR+PLC_D_C_T_addr(temp2)+Z4_Z7(temp2)*2),(uint16_t)trade),PLC_Addr++;
            break;//大于等于D1000的Z4-Z7
    }
}

static int16_t Special_cos_value(uint16_t num)
{
    static int16_t temp;
    uint16_t Type_F,temp2,Data1,Data2;

    Data1=*PLC_Addr;
    PLC_Addr++;
    Data2=*PLC_Addr;

    temp2=Type_F=0;
    Type_F = (Data1 & 0xff00)|(Data2 >> 8);
    /************************************/
    temp2  = (Data2 << 8);
    temp2 |=mov_d_addr=(uint8_t)Data1;
    /************************************/

    // Type_F = 0x8480时做如下处理
    if((Type_F == 0x8480) || (Type_F == 0x9480) || (Type_F == 0xA480))
        Type_F |= (num <<1);

    switch(Type_F)
    {
        case  0x8080: //算出K值
        {
            temp=temp2;
            PLC_Addr++;
            break;
        }
        case  0x8280: //算出H值
        {
            temp=temp2,
                    PLC_Addr++;
            break;
        }
        case  0x8482:
            temp=MOV_K(*PLC_Addr)&0X0000000F,Transfer_bit1=1,PLC_Addr++;
            break;  //进行如 K4M0 之类的传送
        case  0x8484:
            temp=MOV_K(*PLC_Addr)&0X000000FF,Transfer_bit1=1,PLC_Addr++;
            break;  //进行如 K4M0 之类的传送
        case  0x8486:
            temp=MOV_K(*PLC_Addr)&0X00000FFF,Transfer_bit1=1,PLC_Addr++;
            break;  //进行如 K4M0 之类的传送
        case  0x8488:
            temp=MOV_K(*PLC_Addr)&0X0000FFFF,Transfer_bit1=1,PLC_Addr++;
            break;  //进行如 K4M0 之类的传送
        case  0x8680: //算出D的地址 D8000
        {
            temp=PLC_R_RAM_16BIT(RAM_D8000_ADDR + temp2 + Transfer);
            PLC_Addr++;
            break;
        }
        case  0x8682: //算出T的地址
        {
            temp=PLC_R_RAM_16BIT(RAM_T_ADDR + temp2 + Transfer);
            PLC_Addr++;
            break;
        }
        case  0x8684: //算出C的地址
        {
            temp=PLC_R_RAM_16BIT(RAM_C_ADDR + temp2 + Transfer);
            PLC_Addr++;
            break;
        }
        case  0x8686: //算出D的地址
        {
            temp=PLC_R_RAM_16BIT(RAM_D_ADDR + temp2 + Transfer);
            PLC_Addr++;
            break;
        }
        case  0x8688: //大于等于D1000
        {
            temp=PLC_R_RAM_16BIT(RAM_D1000_ADDR + temp2 + Transfer);
            PLC_Addr++;
            break;
        }
            /************************************************K寄存器"V"*******************************************************************/
        case  0x9080:
            temp=temp2+D8029,PLC_Addr++;
            break;//算出K的地址，的V0
        case  0x9081:
            temp=temp2+D8183,PLC_Addr++;
            break;//算出K的地址，的V1
        case  0x9082:
            temp=temp2+D8185,PLC_Addr++;
            break;//算出K的地址，的V2
        case  0x9083:
            temp=temp2+D8187,PLC_Addr++;
            break;//算出K的地址，的V3
        case  0x9084:
            temp=temp2+D8189,PLC_Addr++;
            break;//算出K的地址，的V4
        case  0x9085:
            temp=temp2+D8191,PLC_Addr++;
            break;//算出K的地址，的V5
        case  0x9086:
            temp=temp2+D8193,PLC_Addr++;
            break;//算出K的地址，的V6
        case  0x9087:
            temp=temp2+D8195,PLC_Addr++;
            break;//算出K的地址，的V7
        case  0x9280:
            temp=temp2+D8029,PLC_Addr++;
            break;//算出H的地址，的V0
        case  0x9281:
            temp=temp2+D8183,PLC_Addr++;
            break;//算出H的地址，的V1
        case  0x9282:
            temp=temp2+D8185,PLC_Addr++;
            break;//算出H的地址，的V2
        case  0x9283:
            temp=temp2+D8187,PLC_Addr++;
            break;//算出H的地址，的V3
        case  0x9284:
            temp=temp2+D8189,PLC_Addr++;
            break;//算出H的地址，的V4
        case  0x9285:
            temp=temp2+D8191,PLC_Addr++;
            break;//算出H的地址，的V5
        case  0x9286:
            temp=temp2+D8193,PLC_Addr++;
            break;//算出H的地址，的V6
        case  0x9287:
            temp=temp2+D8195,PLC_Addr++;
            break;//算出H的地址，的V7

            /************************************************K1M0寄存器"V"*******************************************************************/
        case  0x9482:
            mov_d_addr=+V0_V3(temp2),temp=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X0000000F,PLC_Addr++;
            break;//K1M0的V0-V3
        case  0x9483:
            mov_d_addr=+V4_V7(temp2),temp=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X0000000F,PLC_Addr++;
            break;//K1M0的V4-V7
            /************************************************K2M0寄存器"V"*******************************************************************/
        case  0x9484:
            mov_d_addr=+V0_V3(temp2),temp=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X000000FF,PLC_Addr++;
            break;//K2M0的V0-V3
        case  0x9485:
            mov_d_addr=+V4_V7(temp2),temp=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X000000FF,PLC_Addr++;
            break;//K2M0的V4-V7
            /************************************************K3M0寄存器"V"*******************************************************************/
        case  0x9486:
            mov_d_addr=+V0_V3(temp2),temp=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X00000FFF,PLC_Addr++;
            break;//K3M0的V0-V3
        case  0x9487:
            mov_d_addr=+V4_V7(temp2),temp=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X00000FFF,PLC_Addr++;
            break;//K3M0的V4-V7
            /************************************************K4M0寄存器"V"*******************************************************************/
        case  0x9488:
            mov_d_addr=+V0_V3(temp2),temp=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X0000FFFF,PLC_Addr++;
            break;//K4M0的V0-V3
        case  0x9489:
            mov_d_addr=+V4_V7(temp2),temp=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X0000FFFF,PLC_Addr++;
            break;//K4M0的V4-V7
            /************************************************T寄存器"V"*******************************************************************/
        case  0x9682:
            temp=PLC_R_RAM_16BIT(RAM_T_ADDR+PLC_D_C_T_addr(temp2)+V0_V3(temp2)*2),PLC_Addr++;
            break;//算出T的地址，的V0-V3
        case  0x9683:
            temp=PLC_R_RAM_16BIT(RAM_T_ADDR+PLC_D_C_T_addr(temp2)+V4_V7(temp2)*2),PLC_Addr++;
            break;//算出T的地址，的V4-V7
            /************************************************C寄存器"V"*******************************************************************/
        case  0x9684:
            temp=PLC_R_RAM_16BIT(RAM_C_ADDR+PLC_D_C_T_addr(temp2)+V0_V3(temp2)*2),PLC_Addr++;
            break;//算出C的地址，的V0-V3
        case  0x9685:
            temp=PLC_R_RAM_16BIT(RAM_C_ADDR+PLC_D_C_T_addr(temp2)+V4_V7(temp2)*2),PLC_Addr++;
            break;//算出C的地址，的V4-V7
            /************************************************D寄存器"V"*******************************************************************/
        case  0x9686:
            temp=PLC_R_RAM_16BIT(RAM_D_ADDR+PLC_D_C_T_addr(temp2)+V0_V3(temp2)*2),PLC_Addr++;
            break;//算出D的地址，的V0-V3
        case  0x9687:
            temp=PLC_R_RAM_16BIT(RAM_D_ADDR+PLC_D_C_T_addr(temp2)+V4_V7(temp2)*2),PLC_Addr++;
            break;//算出D的地址，的V4-V7
        case  0x9688:
            temp=PLC_R_RAM_16BIT(RAM_D1000_ADDR+PLC_D_C_T_addr(temp2)+V0_V3(temp2)*2),PLC_Addr++;
            break;//大于等于D1000的V0-V3
        case  0x9689:
            temp=PLC_R_RAM_16BIT(RAM_D1000_ADDR+PLC_D_C_T_addr(temp2)+V4_V7(temp2)*2),PLC_Addr++;
            break;//大于等于D1000的V4-V7
        case  0xA080:
            temp=temp2+D8028,PLC_Addr++;
            break;//算出K的地址，的Z0
        case  0xA081:
            temp=temp2+D8182,PLC_Addr++;
            break;//算出K的地址，的Z1
        case  0xA082:
            temp=temp2+D8184,PLC_Addr++;
            break;//算出K的地址，的Z2
        case  0xA083:
            temp=temp2+D8186,PLC_Addr++;
            break;//算出K的地址，的Z3
        case  0xA084:
            temp=temp2+D8188,PLC_Addr++;
            break;//算出K的地址，的Z4
        case  0xA085:
            temp=temp2+D8190,PLC_Addr++;
            break;//算出K的地址，的Z5
        case  0xA086:
            temp=temp2+D8192,PLC_Addr++;
            break;//算出K的地址，的Z6
        case  0xA087:
            temp=temp2+D8194,PLC_Addr++;
            break;//算出K的地址，的Z7
        case  0xA280:
            temp=temp2+D8028,PLC_Addr++;
            break;//算出H的地址，的Z0
        case  0xA281:
            temp=temp2+D8182,PLC_Addr++;
            break;//算出H的地址，的Z1
        case  0xA282:
            temp=temp2+D8184,PLC_Addr++;
            break;//算出H的地址，的Z2
        case  0xA283:
            temp=temp2+D8186,PLC_Addr++;
            break;//算出H的地址，的Z3
        case  0xA284:
            temp=temp2+D8188,PLC_Addr++;
            break;//算出H的地址，的Z4
        case  0xA285:
            temp=temp2+D8190,PLC_Addr++;
            break;//算出H的地址，的Z5
        case  0xA286:
            temp=temp2+D8192,PLC_Addr++;
            break;//算出H的地址，的Z6
        case  0xA287:
            temp=temp2+D8194,PLC_Addr++;
            break;//算出H的地址，的Z7
            /************************************************K1M0寄存器"Z"*******************************************************************/
        case  0xA482:
            mov_d_addr=+Z0_Z3(temp2),temp=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X0000000F,PLC_Addr++;
            break;//K1M0的V0-V3
        case  0xA483:
            mov_d_addr=+Z4_Z7(temp2),temp=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X0000000F,PLC_Addr++;
            break;//K1M0的V4-V7
            /************************************************K2M0寄存器"Z"*******************************************************************/
        case  0xA484:
            mov_d_addr=+Z0_Z3(temp2),temp=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X000000FF,PLC_Addr++;
            break;//K2M0的V0-V3
        case  0xA485:
            mov_d_addr=+Z4_Z7(temp2),temp=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X000000FF,PLC_Addr++;
            break;//K2M0的V4-V7
            /************************************************K3M0寄存器"Z"*******************************************************************/
        case  0xA486:
            mov_d_addr=+Z0_Z3(temp2),temp=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X00000FFF,PLC_Addr++;
            break;//K3M0的V0-V3
        case  0xA487:
            mov_d_addr=+Z4_Z7(temp2),temp=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X00000FFF,PLC_Addr++;
            break;//K3M0的V4-V7
            /************************************************K4M0寄存器"Z"*******************************************************************/
        case  0xA488:
            mov_d_addr=+Z0_Z3(temp2),temp=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X0000FFFF,PLC_Addr++;
            break;//K4M0的V0-V3
        case  0xA489:
            mov_d_addr=+Z4_Z7(temp2),temp=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X0000FFFF,PLC_Addr++;
            break;//K4M0的V4-V7
        case  0xA682:
            temp=PLC_R_RAM_16BIT(RAM_T_ADDR+PLC_D_C_T_addr(temp2)+Z0_Z3(temp2)*2),PLC_Addr++;
            break;//算出T的地址，的Z0-Z3
        case  0xA683:
            temp=PLC_R_RAM_16BIT(RAM_T_ADDR+PLC_D_C_T_addr(temp2)+Z4_Z7(temp2)*2),PLC_Addr++;
            break;//算出T的地址，的Z4-Z7
            /************************************************C寄存器"Z"*******************************************************************/
        case  0xA684:
            temp=PLC_R_RAM_16BIT(RAM_C_ADDR+PLC_D_C_T_addr(temp2)+Z0_Z3(temp2)*2),PLC_Addr++;
            break;//算出C的地址，的Z0-Z3
        case  0xA685:
            temp=PLC_R_RAM_16BIT(RAM_C_ADDR+PLC_D_C_T_addr(temp2)+Z4_Z7(temp2)*2),PLC_Addr++;
            break;//算出C的地址，的Z4-Z7
            /************************************************D寄存器"Z"*******************************************************************/
        case  0xA686:
            temp=PLC_R_RAM_16BIT(RAM_D_ADDR+PLC_D_C_T_addr(temp2)+Z0_Z3(temp2)*2),PLC_Addr++;
            break;//算出D的地址，的Z0-Z3
        case  0xA687:
            temp=PLC_R_RAM_16BIT(RAM_D_ADDR+PLC_D_C_T_addr(temp2)+Z4_Z7(temp2)*2),PLC_Addr++;
            break;//算出D的地址，的Z4-Z7
        case  0xA688:
            temp=PLC_R_RAM_16BIT(RAM_D1000_ADDR+PLC_D_C_T_addr(temp2)+Z0_Z3(temp2)*2),PLC_Addr++;
            break;//大于等于D1000的Z0-Z3
        case  0xA689:
            temp=PLC_R_RAM_16BIT(RAM_D1000_ADDR+PLC_D_C_T_addr(temp2)+Z4_Z7(temp2)*2),PLC_Addr++;
            break;//大于等于D1000的Z4-Z7
    }
    return temp;
}

// kevin，20180517新增
static void SpecialTarget(void)
{
    uint16_t Type_F,temp2,Data1,Data2;
    uint16_t tmp =0;

    Data1 =*PLC_Addr;
    PLC_Addr++;
    Data2 =*PLC_Addr;

    temp2=Type_F=0;
    Type_F  = (Data1 & 0xff00);
    Type_F  |= (Data2 >> 8);
    /************************************/
    temp2  = (Data2 << 8);
    temp2 |=mov_d_addr=(uint8_t)Data1;
    /************************************/
    // Type_F = 0x8480时做如下处理
    if((Type_F == 0x8480) || (Type_F == 0x9480) || (Type_F == 0xA480))
    {
        if(trade <= 0x000F) // 4
            tmp =0x0002;
        else if(trade <= 0x00FF) // 8
            tmp =0x0004;
        else if(trade <= 0x0FFF) // 12
            tmp =0x0006;
        else if(trade <= 0xFFFF) // 16
            tmp =0x0008;
        else if(trade <= 0xFFFFF) // 20
            tmp =0x000A;
        else if(trade <= 0xFFFFFF) // 24
            tmp =0x000C;
        else if(trade <= 0xFFFFFFF) // 28
            tmp =0x000E;
        else if(trade <= 0xFFFFFFFF) // 32
            tmp =0x0010;
        Type_F |=tmp;
    }

    switch(Type_F)
    {
        /* 进行如 K4M0 之类的传送 */
        case  0x8482: // 4
        case  0x8484: // 8
        case  0x8486: // 12
        case  0x8488: // 16
        case  0x848A: // 20
        case  0x848C: // 24
        case  0x848E: // 28
        case  0x8490: // 32
        {
            MOV_TO_K_H(Type_F,trade,*PLC_Addr);
            PLC_Addr++;
            Transfer_bit=1;
            break;
        }
        case  0x8680:
            PLC_W_RAM_16BIT((RAM_D8000_ADDR+temp2+Transfer),(uint16_t)trade),PLC_Addr++;
            break; //算出D的地址 D8000
        case  0x8682:
            PLC_W_RAM_16BIT((RAM_T_ADDR+temp2+Transfer),(uint16_t)trade),PLC_Addr++;
            break; //算出T的地址
        case  0x8684:
            PLC_W_RAM_16BIT((RAM_C_ADDR+temp2+Transfer),(uint16_t)trade),PLC_Addr++;
            break; //算出C的地址
        case  0x8686:
            PLC_W_RAM_16BIT((RAM_D_ADDR+temp2+Transfer),(uint16_t)trade),PLC_Addr++;
            break; //算出D的地址
        case  0x8688:
            PLC_W_RAM_16BIT((RAM_D1000_ADDR+temp2+Transfer),(uint16_t)trade),PLC_Addr++;
            break; //大于等于D1000

            /************************************************KxM0寄存器"V"*******************************************************************/
        case  0x9482: // K1M0的V0-V3
        case  0x9484: // K2M0的V0-V3
        case  0x9486: // K3M0的V0-V3
        case  0x9488: // K4M0的V0-V3
        {
            mov_d_addr=+V0_V3(temp2);
            MOV_TO_K_H(Type_F,(uint16_t)trade,PLC_D_C_T_addr(temp2)/0x100);
            PLC_Addr++;
            break;
        }
        case  0x9483: // K1M0的V4-V7
        case  0x9485: // K2M0的V4-V7
        case  0x9487: // K3M0的V4-V7
        case  0x9489: // K4M0的V4-V7
        {
            mov_d_addr=+V4_V7(temp2);
            MOV_TO_K_H(Type_F,(uint16_t)trade,PLC_D_C_T_addr(temp2)/0x100);
            PLC_Addr++;
            break;
        }
            /************************************************T寄存器"V"*******************************************************************/
        case  0x9682:
            PLC_W_RAM_16BIT((RAM_T_ADDR+PLC_D_C_T_addr(temp2)+V0_V3(temp2)*2),(uint16_t)trade),PLC_Addr++;
            break;//算出T的地址，的V0-V3
        case  0x9683:
            PLC_W_RAM_16BIT((RAM_T_ADDR+PLC_D_C_T_addr(temp2)+V4_V7(temp2)*2),(uint16_t)trade),PLC_Addr++;
            break;//算出T的地址，的V4-V7
            /************************************************C寄存器"V"*******************************************************************/
        case  0x9684:
            PLC_W_RAM_16BIT((RAM_C_ADDR+PLC_D_C_T_addr(temp2)+V0_V3(temp2)*2),(uint16_t)trade),PLC_Addr++;
            break;//算出C的地址，的V0-V3
        case  0x9685:
            PLC_W_RAM_16BIT((RAM_C_ADDR+PLC_D_C_T_addr(temp2)+V4_V7(temp2)*2),(uint16_t)trade),PLC_Addr++;
            break;//算出C的地址，的V4-V7
            /************************************************D寄存器"V"*******************************************************************/
        case  0x9686:
            PLC_W_RAM_16BIT((RAM_D_ADDR+PLC_D_C_T_addr(temp2)+V0_V3(temp2)*2),(uint16_t)trade),PLC_Addr++;
            break;//算出D的地址，的V0-V3
        case  0x9687:
            PLC_W_RAM_16BIT((RAM_D_ADDR+PLC_D_C_T_addr(temp2)+V4_V7(temp2)*2),(uint16_t)trade),PLC_Addr++;
            break;//算出D的地址，的V4-V7
        case  0x9688:
            PLC_W_RAM_16BIT((RAM_D1000_ADDR+PLC_D_C_T_addr(temp2)+V0_V3(temp2)*2),(uint16_t)trade),PLC_Addr++;
            break;//大于等于D1000的V0-V3
        case  0x9689:
            PLC_W_RAM_16BIT((RAM_D1000_ADDR+PLC_D_C_T_addr(temp2)+V4_V7(temp2)*2),(uint16_t)trade),PLC_Addr++;
            break;//大于等于D1000的V4-V7

            /************************************************KxM0寄存器"Z"*******************************************************************/
        case  0xA482: //K1M0的V0-V3
        case  0xA484: //K2M0的V0-V3
        case  0xA486: //K3M0的V0-V3
        case  0xA488: //K4M0的V0-V3
        {
            mov_d_addr=+Z0_Z3(temp2);
            MOV_TO_K_H(Type_F,(uint16_t)trade,PLC_D_C_T_addr(temp2)/0x100);
            PLC_Addr++;
            break;
        }
        case  0xA483: //K1M0的V4-V7
        case  0xA485: //K2M0的V4-V7
        case  0xA487: //K3M0的V4-V7
        case  0xA489: //K4M0的V4-V7
        {
            mov_d_addr=+Z4_Z7(temp2);
            MOV_TO_K_H(Type_F,(uint16_t)trade,PLC_D_C_T_addr(temp2)/0x100);
            PLC_Addr++;
            break;
        }
            /************************************************T寄存器"Z"*******************************************************************/
        case  0xA682:
            PLC_W_RAM_16BIT((RAM_T_ADDR+PLC_D_C_T_addr(temp2)+Z0_Z3(temp2)*2),(uint16_t)trade),PLC_Addr++;
            break;//算出T的地址，的Z0-Z3
        case  0xA683:
            PLC_W_RAM_16BIT((RAM_T_ADDR+PLC_D_C_T_addr(temp2)+Z4_Z7(temp2)*2),(uint16_t)trade),PLC_Addr++;
            break;//算出T的地址，的Z4-Z7
            /************************************************C寄存器"Z"*******************************************************************/
        case  0xA684:
            PLC_W_RAM_16BIT((RAM_C_ADDR+PLC_D_C_T_addr(temp2)+Z0_Z3(temp2)*2),(uint16_t)trade),PLC_Addr++;
            break;//算出C的地址，的Z0-Z3
        case  0xA685:
            PLC_W_RAM_16BIT((RAM_C_ADDR+PLC_D_C_T_addr(temp2)+Z4_Z7(temp2)*2),(uint16_t)trade),PLC_Addr++;
            break;//算出C的地址，的Z4-Z7
            /************************************************D寄存器"Z"*******************************************************************/
        case  0xA686:
            PLC_W_RAM_16BIT((RAM_D_ADDR+PLC_D_C_T_addr(temp2)+Z0_Z3(temp2)*2),(uint16_t)trade),PLC_Addr++;
            break;//算出D的地址，的Z0-Z3
        case  0xA687:
            PLC_W_RAM_16BIT((RAM_D_ADDR+PLC_D_C_T_addr(temp2)+Z4_Z7(temp2)*2),(uint16_t)trade),PLC_Addr++;
            break;//算出D的地址，的Z4-Z7
        case  0xA688:
            PLC_W_RAM_16BIT((RAM_D1000_ADDR+PLC_D_C_T_addr(temp2)+Z0_Z3(temp2)*2),(uint16_t)trade),PLC_Addr++;
            break;//大于等于D1000的Z0-Z3
        case  0xA689:
            PLC_W_RAM_16BIT((RAM_D1000_ADDR+PLC_D_C_T_addr(temp2)+Z4_Z7(temp2)*2),(uint16_t)trade),PLC_Addr++;
            break;//大于等于D1000的Z4-Z7
    }
}


//=======================================================================================================
// 函数名称:  static uint16_t  cos_value(void)
// 功能描述：
// 输　入:  void
// 输　出:  输出16位数据
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2015年6月19日
// 备  注:
//-------------------------------------------------------------------------------------------------------
// 修改人:
// 日　期:
// 备  注:
//-------------------------------------------------------------------------------------------------------
//=======================================================================================================
static int16_t cos_value()
{
    static int16_t temp;
    uint16_t Type_F,temp2,Data1,Data2;

    Data1=*PLC_Addr;
    PLC_Addr++;
    Data2=*PLC_Addr;

    temp2=Type_F=0;
    Type_F = (Data1 & 0xff00)|(Data2 >> 8);
    /************************************/
    temp2  = (Data2 << 8);
    temp2 |=mov_d_addr=(uint8_t)Data1;
    /************************************/
    switch(Type_F)
    {
        case  0x8080: //算出K值
        {
            temp=temp2;
            PLC_Addr++;
            break;
        }
        case  0x8280: //算出H值
        {
            temp=temp2,
                    PLC_Addr++;
            break;
        }
        case  0x8482:
            temp=MOV_K(*PLC_Addr)&0X0000000F,Transfer_bit1=1,PLC_Addr++;
            break;  //进行如 K4M0 之类的传送
        case  0x8484:
            temp=MOV_K(*PLC_Addr)&0X000000FF,Transfer_bit1=1,PLC_Addr++;
            break;  //进行如 K4M0 之类的传送
        case  0x8486:
            temp=MOV_K(*PLC_Addr)&0X00000FFF,Transfer_bit1=1,PLC_Addr++;
            break;  //进行如 K4M0 之类的传送
        case  0x8488:
            temp=MOV_K(*PLC_Addr)&0X0000FFFF,Transfer_bit1=1,PLC_Addr++;
            break;  //进行如 K4M0 之类的传送
        case  0x8680: //算出D的地址 D8000
        {
            temp=PLC_R_RAM_16BIT(RAM_D8000_ADDR + temp2 + Transfer);
            Transfer_bit1=0;
            PLC_Addr++;
            break;
        }
        case  0x8682: //算出T的地址
        {
            temp=PLC_R_RAM_16BIT(RAM_T_ADDR + temp2 + Transfer);
            Transfer_bit1=0;
            PLC_Addr++;
            break;
        }
        case  0x8684: //算出C的地址
        {
            temp=PLC_R_RAM_16BIT(RAM_C_ADDR + temp2 + Transfer);
            Transfer_bit1=0;
            PLC_Addr++;
            break;
        }
        case  0x8686: //算出D的地址
        {
            temp=PLC_R_RAM_16BIT(RAM_D_ADDR + temp2 + Transfer);
            Transfer_bit1=0;
            PLC_Addr++;
            break;
        }
        case  0x8688: //大于等于D1000
        {
            temp=PLC_R_RAM_16BIT(RAM_D1000_ADDR + temp2 + Transfer);
            Transfer_bit1=0;
            PLC_Addr++;
            break;
        }
            /************************************************K寄存器"V"*******************************************************************/
        case  0x9080:
            temp=temp2+D8029,PLC_Addr++;
            break;//算出K的地址，的V0
        case  0x9081:
            temp=temp2+D8183,PLC_Addr++;
            break;//算出K的地址，的V1
        case  0x9082:
            temp=temp2+D8185,PLC_Addr++;
            break;//算出K的地址，的V2
        case  0x9083:
            temp=temp2+D8187,PLC_Addr++;
            break;//算出K的地址，的V3
        case  0x9084:
            temp=temp2+D8189,PLC_Addr++;
            break;//算出K的地址，的V4
        case  0x9085:
            temp=temp2+D8191,PLC_Addr++;
            break;//算出K的地址，的V5
        case  0x9086:
            temp=temp2+D8193,PLC_Addr++;
            break;//算出K的地址，的V6
        case  0x9087:
            temp=temp2+D8195,PLC_Addr++;
            break;//算出K的地址，的V7
        case  0x9280:
            temp=temp2+D8029,PLC_Addr++;
            break;//算出H的地址，的V0
        case  0x9281:
            temp=temp2+D8183,PLC_Addr++;
            break;//算出H的地址，的V1
        case  0x9282:
            temp=temp2+D8185,PLC_Addr++;
            break;//算出H的地址，的V2
        case  0x9283:
            temp=temp2+D8187,PLC_Addr++;
            break;//算出H的地址，的V3
        case  0x9284:
            temp=temp2+D8189,PLC_Addr++;
            break;//算出H的地址，的V4
        case  0x9285:
            temp=temp2+D8191,PLC_Addr++;
            break;//算出H的地址，的V5
        case  0x9286:
            temp=temp2+D8193,PLC_Addr++;
            break;//算出H的地址，的V6
        case  0x9287:
            temp=temp2+D8195,PLC_Addr++;
            break;//算出H的地址，的V7
            /************************************************K1M0寄存器"V"*******************************************************************/
        case  0x9482:
            mov_d_addr=+V0_V3(temp2),temp=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X0000000F,PLC_Addr++;
            break;//K1M0的V0-V3
        case  0x9483:
            mov_d_addr=+V4_V7(temp2),temp=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X0000000F,PLC_Addr++;
            break;//K1M0的V4-V7
            /************************************************K2M0寄存器"V"*******************************************************************/
        case  0x9484:
            mov_d_addr=+V0_V3(temp2),temp=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X000000FF,PLC_Addr++;
            break;//K2M0的V0-V3
        case  0x9485:
            mov_d_addr=+V4_V7(temp2),temp=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X000000FF,PLC_Addr++;
            break;//K2M0的V4-V7
            /************************************************K3M0寄存器"V"*******************************************************************/
        case  0x9486:
            mov_d_addr=+V0_V3(temp2),temp=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X00000FFF,PLC_Addr++;
            break;//K3M0的V0-V3
        case  0x9487:
            mov_d_addr=+V4_V7(temp2),temp=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X00000FFF,PLC_Addr++;
            break;//K3M0的V4-V7
            /************************************************K4M0寄存器"V"*******************************************************************/
        case  0x9488:
            mov_d_addr=+V0_V3(temp2),temp=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X0000FFFF,PLC_Addr++;
            break;//K4M0的V0-V3
        case  0x9489:
            mov_d_addr=+V4_V7(temp2),temp=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X0000FFFF,PLC_Addr++;
            break;//K4M0的V4-V7
            /************************************************T寄存器"V"*******************************************************************/
        case  0x9682:
            temp=PLC_R_RAM_16BIT(RAM_T_ADDR+PLC_D_C_T_addr(temp2)+V0_V3(temp2)*2),PLC_Addr++;
            break;//算出T的地址，的V0-V3
        case  0x9683:
            temp=PLC_R_RAM_16BIT(RAM_T_ADDR+PLC_D_C_T_addr(temp2)+V4_V7(temp2)*2),PLC_Addr++;
            break;//算出T的地址，的V4-V7
            /************************************************C寄存器"V"*******************************************************************/
        case  0x9684:
            temp=PLC_R_RAM_16BIT(RAM_C_ADDR+PLC_D_C_T_addr(temp2)+V0_V3(temp2)*2),PLC_Addr++;
            break;//算出C的地址，的V0-V3
        case  0x9685:
            temp=PLC_R_RAM_16BIT(RAM_C_ADDR+PLC_D_C_T_addr(temp2)+V4_V7(temp2)*2),PLC_Addr++;
            break;//算出C的地址，的V4-V7
            /************************************************D寄存器"V"*******************************************************************/
        case  0x9686:
            temp=PLC_R_RAM_16BIT(RAM_D_ADDR+PLC_D_C_T_addr(temp2)+V0_V3(temp2)*2),PLC_Addr++;
            break;//算出D的地址，的V0-V3
        case  0x9687:
            temp=PLC_R_RAM_16BIT(RAM_D_ADDR+PLC_D_C_T_addr(temp2)+V4_V7(temp2)*2),PLC_Addr++;
            break;//算出D的地址，的V4-V7
        case  0x9688:
            temp=PLC_R_RAM_16BIT(RAM_D1000_ADDR+PLC_D_C_T_addr(temp2)+V0_V3(temp2)*2),PLC_Addr++;
            break;//大于等于D1000的V0-V3
        case  0x9689:
            temp=PLC_R_RAM_16BIT(RAM_D1000_ADDR+PLC_D_C_T_addr(temp2)+V4_V7(temp2)*2),PLC_Addr++;
            break;//大于等于D1000的V4-V7
        case  0xA080:
            temp=temp2+D8028,PLC_Addr++;
            break;//算出K的地址，的Z0
        case  0xA081:
            temp=temp2+D8182,PLC_Addr++;
            break;//算出K的地址，的Z1
        case  0xA082:
            temp=temp2+D8184,PLC_Addr++;
            break;//算出K的地址，的Z2
        case  0xA083:
            temp=temp2+D8186,PLC_Addr++;
            break;//算出K的地址，的Z3
        case  0xA084:
            temp=temp2+D8188,PLC_Addr++;
            break;//算出K的地址，的Z4
        case  0xA085:
            temp=temp2+D8190,PLC_Addr++;
            break;//算出K的地址，的Z5
        case  0xA086:
            temp=temp2+D8192,PLC_Addr++;
            break;//算出K的地址，的Z6
        case  0xA087:
            temp=temp2+D8194,PLC_Addr++;
            break;//算出K的地址，的Z7
        case  0xA280:
            temp=temp2+D8028,PLC_Addr++;
            break;//算出H的地址，的Z0
        case  0xA281:
            temp=temp2+D8182,PLC_Addr++;
            break;//算出H的地址，的Z1
        case  0xA282:
            temp=temp2+D8184,PLC_Addr++;
            break;//算出H的地址，的Z2
        case  0xA283:
            temp=temp2+D8186,PLC_Addr++;
            break;//算出H的地址，的Z3
        case  0xA284:
            temp=temp2+D8188,PLC_Addr++;
            break;//算出H的地址，的Z4
        case  0xA285:
            temp=temp2+D8190,PLC_Addr++;
            break;//算出H的地址，的Z5
        case  0xA286:
            temp=temp2+D8192,PLC_Addr++;
            break;//算出H的地址，的Z6
        case  0xA287:
            temp=temp2+D8194,PLC_Addr++;
            break;//算出H的地址，的Z7
            /************************************************K1M0寄存器"Z"*******************************************************************/
        case  0xA482:
            mov_d_addr=+Z0_Z3(temp2),temp=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X0000000F,PLC_Addr++;
            break;//K1M0的V0-V3
        case  0xA483:
            mov_d_addr=+Z4_Z7(temp2),temp=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X0000000F,PLC_Addr++;
            break;//K1M0的V4-V7
            /************************************************K2M0寄存器"Z"*******************************************************************/
        case  0xA484:
            mov_d_addr=+Z0_Z3(temp2),temp=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X000000FF,PLC_Addr++;
            break;//K2M0的V0-V3
        case  0xA485:
            mov_d_addr=+Z4_Z7(temp2),temp=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X000000FF,PLC_Addr++;
            break;//K2M0的V4-V7
            /************************************************K3M0寄存器"Z"*******************************************************************/
        case  0xA486:
            mov_d_addr=+Z0_Z3(temp2),temp=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X00000FFF,PLC_Addr++;
            break;//K3M0的V0-V3
        case  0xA487:
            mov_d_addr=+Z4_Z7(temp2),temp=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X00000FFF,PLC_Addr++;
            break;//K3M0的V4-V7
            /************************************************K4M0寄存器"Z"*******************************************************************/
        case  0xA488:
            mov_d_addr=+Z0_Z3(temp2),temp=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X0000FFFF,PLC_Addr++;
            break;//K4M0的V0-V3
        case  0xA489:
            mov_d_addr=+Z4_Z7(temp2),temp=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X0000FFFF,PLC_Addr++;
            break;//K4M0的V4-V7
        case  0xA682:
            temp=PLC_R_RAM_16BIT(RAM_T_ADDR+PLC_D_C_T_addr(temp2)+Z0_Z3(temp2)*2),PLC_Addr++;
            break;//算出T的地址，的Z0-Z3
        case  0xA683:
            temp=PLC_R_RAM_16BIT(RAM_T_ADDR+PLC_D_C_T_addr(temp2)+Z4_Z7(temp2)*2),PLC_Addr++;
            break;//算出T的地址，的Z4-Z7
            /************************************************C寄存器"Z"*******************************************************************/
        case  0xA684:
            temp=PLC_R_RAM_16BIT(RAM_C_ADDR+PLC_D_C_T_addr(temp2)+Z0_Z3(temp2)*2),PLC_Addr++;
            break;//算出C的地址，的Z0-Z3
        case  0xA685:
            temp=PLC_R_RAM_16BIT(RAM_C_ADDR+PLC_D_C_T_addr(temp2)+Z4_Z7(temp2)*2),PLC_Addr++;
            break;//算出C的地址，的Z4-Z7
            /************************************************D寄存器"Z"*******************************************************************/
        case  0xA686:
            temp=PLC_R_RAM_16BIT(RAM_D_ADDR+PLC_D_C_T_addr(temp2)+Z0_Z3(temp2)*2),PLC_Addr++;
            break;//算出D的地址，的Z0-Z3
        case  0xA687:
            temp=PLC_R_RAM_16BIT(RAM_D_ADDR+PLC_D_C_T_addr(temp2)+Z4_Z7(temp2)*2),PLC_Addr++;
            break;//算出D的地址，的Z4-Z7
        case  0xA688:
            temp=PLC_R_RAM_16BIT(RAM_D1000_ADDR+PLC_D_C_T_addr(temp2)+Z0_Z3(temp2)*2),PLC_Addr++;
            break;//大于等于D1000的Z0-Z3
        case  0xA689:
            temp=PLC_R_RAM_16BIT(RAM_D1000_ADDR+PLC_D_C_T_addr(temp2)+Z4_Z7(temp2)*2),PLC_Addr++;
            break;//大于等于D1000的Z4-Z7
    }
    return temp;
}

//=======================================================================================================
// 函数名称:  static uint32_t cos_u32_value(void)
// 功能描述：
// 输　入:  void
// 输　出:  输出32位数据
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2015年6月19日
// 备  注:
//-------------------------------------------------------------------------------------------------------
// 修改人:
// 日　期:
// 备  注:
//-------------------------------------------------------------------------------------------------------
//=======================================================================================================
#define  D_data  u32data.data
static int32_t cos_u32_value(void)
{
    uint16_t Type_F,temp2,Data1,Data2;
    unsigned short temp;
    Data1=*PLC_Addr;
    PLC_Addr++;
    Data2=*PLC_Addr;
    temp2=Type_F=0;
    Type_F   = (Data1 & 0xff00);
    Type_F  |= (Data2 >> 8);
    /************************************/
    temp2  = (Data2 << 8);
    temp2 |=mov_d_addr=(uint8_t)Data1;
    /************************************/
    switch(Type_F)
    {
        case  0x8080:
            u32data.data1[0]=temp2,PLC_Addr++,u32data.data1[1]=cos_value(),PLC_Addr-=2;
            break;//算出K值
        case  0x8280:
            u32data.data1[0]=temp2,PLC_Addr++,u32data.data1[1]=cos_value(),PLC_Addr-=2;
            break;//算出H值
        case  0x8482:
            D_data=MOV_K(*PLC_Addr)&0X0000000F,Transfer_bit1=1,PLC_Addr++;
            break;//进行如 K4M0 之类的传送
        case  0x8484:
            D_data=MOV_K(*PLC_Addr)&0X000000FF,Transfer_bit1=1,PLC_Addr++;
            break;//进行如 K4M0 之类的传送
        case  0x8486:
            D_data=MOV_K(*PLC_Addr)&0X00000FFF,Transfer_bit1=1,PLC_Addr++;
            break;//进行如 K4M0 之类的传送
        case  0x8488:
            D_data=MOV_K(*PLC_Addr)&0X0000FFFF,Transfer_bit1=1,PLC_Addr++;
            break;//进行如 K4M0 之类的传送
        case  0x848A:
            D_data=MOV_K(*PLC_Addr)&0X000FFFFF,Transfer_bit1=1,PLC_Addr++;
            break;//进行如 K4M0 之类的传送
        case  0x848C:
            D_data=MOV_K(*PLC_Addr)&0X00FFFFFF,Transfer_bit1=1,PLC_Addr++;
            break;//进行如 K4M0 之类的传送
        case  0x848E:
            D_data=MOV_K(*PLC_Addr)&0X0FFFFFFF,Transfer_bit1=1,PLC_Addr++;
            break;//进行如 K4M0 之类的传送
        case  0x8490:
            D_data=MOV_K(*PLC_Addr),           Transfer_bit1=1,PLC_Addr++;
            break;//进行如 K4M0 之类的传送
        case  0x8680:
            D_data=PLC_R_RAM_32BIT(RAM_D8000_ADDR+temp2),PLC_Addr++;
            break;             //算出D的地址 D8000
        case  0x8682:
            D_data=PLC_R_RAM_32BIT(RAM_T_ADDR+temp2),PLC_Addr++;
            break;                 //算出T的地址
        case  0x8684:
            D_data=PLC_R_RAM_32BIT(RAM_C_ADDR+temp2),PLC_Addr++;
            break;                 //算出C的地址
        case  0x8686:
            D_data=PLC_R_RAM_32BIT(RAM_D_ADDR+temp2),PLC_Addr++;
            break;                 //算出D的地址
        case  0x8688:
            D_data=PLC_R_RAM_32BIT(RAM_D1000_ADDR+temp2),PLC_Addr++;
            break;                 //大于等于D1000
        case  0xA080:
            u32data.data1[0]=temp2+D8028,PLC_Addr++,u32data.data1[1]=cos_value()+D8029,PLC_Addr-=2;
            break;//算出K的地址，的Z0
        case  0xA081:
            u32data.data1[0]=temp2+D8182,PLC_Addr++,u32data.data1[1]=cos_value()+D8183,PLC_Addr-=2;
            break;//算出K的地址，的Z1
        case  0xA082:
            u32data.data1[0]=temp2+D8184,PLC_Addr++,u32data.data1[1]=cos_value()+D8185,PLC_Addr-=2;
            break;//算出K的地址，的Z2
        case  0xA083:
            u32data.data1[0]=temp2+D8186,PLC_Addr++,u32data.data1[1]=cos_value()+D8187,PLC_Addr-=2;
            break;//算出K的地址，的Z3
        case  0xA084:
            u32data.data1[0]=temp2+D8188,PLC_Addr++,u32data.data1[1]=cos_value()+D8189,PLC_Addr-=2;
            break;//算出K的地址，的Z4
        case  0xA085:
            u32data.data1[0]=temp2+D8190,PLC_Addr++,u32data.data1[1]=cos_value()+D8191,PLC_Addr-=2;
            break;//算出K的地址，的Z5
        case  0xA086:
            u32data.data1[0]=temp2+D8192,PLC_Addr++,u32data.data1[1]=cos_value()+D8193,PLC_Addr-=2;
            break;//算出K的地址，的Z6
        case  0xA087:
            u32data.data1[0]=temp2+D8194,PLC_Addr++,u32data.data1[1]=cos_value()+D8195,PLC_Addr-=2;
            break;//算出K的地址，的Z7
        case  0xA280:
            u32data.data1[0]=temp2+D8028,PLC_Addr++,u32data.data1[1]=cos_value()+D8029,PLC_Addr-=2;
            break;//算出H的地址，的Z0
        case  0xA281:
            u32data.data1[0]=temp2+D8182,PLC_Addr++,u32data.data1[1]=cos_value()+D8183,PLC_Addr-=2;
            break;//算出H的地址，的Z1
        case  0xA282:
            u32data.data1[0]=temp2+D8184,PLC_Addr++,u32data.data1[1]=cos_value()+D8185,PLC_Addr-=2;
            break;//算出H的地址，的Z2
        case  0xA283:
            u32data.data1[0]=temp2+D8186,PLC_Addr++,u32data.data1[1]=cos_value()+D8187,PLC_Addr-=2;
            break;//算出H的地址，的Z3
        case  0xA284:
            u32data.data1[0]=temp2+D8188,PLC_Addr++,u32data.data1[1]=cos_value()+D8189,PLC_Addr-=2;
            break;//算出H的地址，的Z4
        case  0xA285:
            u32data.data1[0]=temp2+D8190,PLC_Addr++,u32data.data1[1]=cos_value()+D8191,PLC_Addr-=2;
            break;//算出H的地址，的Z5
        case  0xA286:
            u32data.data1[0]=temp2+D8192,PLC_Addr++,u32data.data1[1]=cos_value()+D8193,PLC_Addr-=2;
            break;//算出H的地址，的Z6
        case  0xA287:
            u32data.data1[0]=temp2+D8194,PLC_Addr++,u32data.data1[1]=cos_value()+D8195,PLC_Addr-=2;
            break;//算出H的地址，的Z7
            /************************************************K1M0寄存器"Z"*******************************************************************/
        case  0xA482:
            mov_d_addr=+DZ0_Z3(temp2),D_data=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X0000000F,PLC_Addr++;
            break;//K1M0的V0-V3
        case  0xA483:
            mov_d_addr=+DZ4_Z7(temp2),D_data=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X0000000F,PLC_Addr++;
            break;//K1M0的V4-V7
            /************************************************K2M0寄存器"Z"*******************************************************************/
        case  0xA484:
            mov_d_addr=+DZ0_Z3(temp2),D_data=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X000000FF,PLC_Addr++;
            break;//K2M0的V0-V3
        case  0xA485:
            mov_d_addr=+DZ4_Z7(temp2),D_data=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X000000FF,PLC_Addr++;
            break;//K2M0的V4-V7
            /************************************************K3M0寄存器"Z"*******************************************************************/
        case  0xA486:
            mov_d_addr=+DZ0_Z3(temp2),D_data=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X00000FFF,PLC_Addr++;
            break;//K3M0的V0-V3
        case  0xA487:
            mov_d_addr=+DZ4_Z7(temp2),D_data=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X00000FFF,PLC_Addr++;
            break;//K3M0的V4-V7
            /************************************************K4M0寄存器"Z"*******************************************************************/
        case  0xA488:
            mov_d_addr=+DZ0_Z3(temp2),D_data=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X0000FFFF,PLC_Addr++;
            break;//K4M0的V0-V3
        case  0xA489:
            mov_d_addr=+DZ4_Z7(temp2),D_data=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X0000FFFF,PLC_Addr++;
            break;//K4M0的V4-V7
            /************************************************K5M0寄存器"Z"*******************************************************************/
        case  0xA48A:
            mov_d_addr=+DZ0_Z3(temp2),D_data=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X000FFFFF,PLC_Addr++;
            break;//K1M0的V0-V3
        case  0xA48B:
            mov_d_addr=+DZ4_Z7(temp2),D_data=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X000FFFFF,PLC_Addr++;
            break;//K1M0的V4-V7
            /************************************************K6M0寄存器"Z"*******************************************************************/
        case  0xA48C:
            mov_d_addr=+DZ0_Z3(temp2),D_data=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X00FFFFFF,PLC_Addr++;
            break;//K2M0的V0-V3
        case  0xA48D:
            mov_d_addr=+DZ4_Z7(temp2),D_data=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X00FFFFFF,PLC_Addr++;
            break;//K2M0的V4-V7
            /************************************************K7M0寄存器"Z"*******************************************************************/
        case  0xA48E:
            mov_d_addr=+DZ0_Z3(temp2),D_data=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X0FFFFFFF,PLC_Addr++;
            break;//K3M0的V0-V3
        case  0xA48F:
            mov_d_addr=+DZ4_Z7(temp2),D_data=MOV_K(PLC_D_C_T_addr(temp2)/0x100)&0X0FFFFFFF,PLC_Addr++;
            break;//K3M0的V4-V7
            /************************************************K8M0寄存器"Z"*******************************************************************/
        case  0xA490:
            mov_d_addr=+DZ0_Z3(temp2),D_data=MOV_K(PLC_D_C_T_addr(temp2)/0x100),PLC_Addr++;
            break;//K4M0的V0-V3
        case  0xA491:
            mov_d_addr=+DZ4_Z7(temp2),D_data=MOV_K(PLC_D_C_T_addr(temp2)/0x100),PLC_Addr++;
            break;//K4M0的V4-V7
            /************************************************T寄存器"Z"*******************************************************************/
        case  0xA682: {
            temp=(PLC_D_C_T_addr(temp2)+DZ0_Z3(temp2)*2);
            if(temp>=510) PLC_PROG_ERROR(M8067,6706),D_data=0;
            else D_data=PLC_R_RAM_32BIT(RAM_T_ADDR+temp),PLC_Addr++;
        }
            break;//算出T的地址，的Z0-Z3
        case  0xA683: {
            temp=(PLC_D_C_T_addr(temp2)+DZ4_Z7(temp2)*2);
            if(temp>=510) PLC_PROG_ERROR(M8067,6706),D_data=0;
            else D_data=PLC_R_RAM_32BIT(RAM_T_ADDR+temp),PLC_Addr++;
        }
            break;//算出T的地址，的Z4-Z7
            /************************************************C寄存器"Z"*******************************************************************/
        case  0xA684: {
            temp=(PLC_D_C_T_addr(temp2)+DZ0_Z3(temp2)*2);
            if(temp>=510) PLC_PROG_ERROR(M8067,6706),D_data=0;
            else D_data=PLC_R_RAM_32BIT(RAM_C_ADDR+temp),PLC_Addr++;
        }
            break;//算出C的地址，的Z0-Z3
        case  0xA685: {
            temp=(PLC_D_C_T_addr(temp2)+DZ4_Z7(temp2)*2);
            if(temp>=510) PLC_PROG_ERROR(M8067,6706),D_data=0;
            else D_data=PLC_R_RAM_32BIT(RAM_C_ADDR+temp),PLC_Addr++;
        }
            break;//算出C的地址，的Z4-Z7
            /************************************************D寄存器"Z"*******************************************************************/
        case  0xA686: {
            temp=(PLC_D_C_T_addr(temp2)+DZ0_Z3(temp2)*2);
            if(temp>=15998) PLC_PROG_ERROR(M8067,6706),D_data=0;
            else D_data=PLC_R_RAM_32BIT(RAM_D_ADDR+temp);
            PLC_Addr++;
        }
            break;//算出D的地址，的Z0-Z3
        case  0xA687: {
            temp=(PLC_D_C_T_addr(temp2)+DZ4_Z7(temp2)*2);
            if(temp>=15998) PLC_PROG_ERROR(M8067,6706),D_data=0;
            else D_data=PLC_R_RAM_32BIT(RAM_D_ADDR+temp);
            PLC_Addr++;
        }
            break;//算出D的地址，的Z4-Z7
        case  0xA688: {
            temp=(PLC_D_C_T_addr(temp2)+DZ0_Z3(temp2)*2);
            if(temp>=13998) PLC_PROG_ERROR(M8067,6706),D_data=0;
            else D_data=PLC_R_RAM_32BIT(RAM_D1000_ADDR+temp),PLC_Addr++;
        }
            break;//大于等于D1000的Z0-Z3
        case  0xA689: {
            temp=(PLC_D_C_T_addr(temp2)+DZ4_Z7(temp2)*2);
            if(temp>=13998) PLC_PROG_ERROR(M8067,6706),D_data=0;
            else D_data=PLC_R_RAM_32BIT(RAM_D1000_ADDR+temp),PLC_Addr++;
        }
            break;//大于等于D1000的Z4-Z7
    }
    PLC_Addr+=2;
    return D_data;
}
//=======================================================================================================
// 函数名称: static float float_value(void)
// 功能描述：
// 输　入:  void
// 输　出:  输出float数据
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2015年6月19日
// 备  注:
//-------------------------------------------------------------------------------------------------------
// 修改人:
// 日　期:
// 备  注:
//-------------------------------------------------------------------------------------------------------
//=======================================================================================================
static float float_value(void)
{
    uint16_t Type_F,temp1,temp2,Data1,Data2,Data3,Data4;
    Data1=*PLC_Addr;
    PLC_Addr++;
    Data2=*PLC_Addr;
    PLC_Addr++;
    Data3=*PLC_Addr;
    PLC_Addr++;
    Data4=*PLC_Addr;
    Type_F   = (Data1 & 0xff00);
    Type_F  |= (Data2 >> 8);
    /************************************/
    temp1  = (Data2 << 8);
    temp1 |=(uint8_t)Data1;
    /************************************/
    temp2  = (Data4 << 8);
    temp2 |=(uint8_t)Data3;
    /************************************/
    if(Type_F == 0x8080)      u32data.data1[0]=temp1,u32data.data1[1]=temp2, FLOAT.DATA=(float)u32data.data, PLC_Addr++;//算出K值
    else if(Type_F == 0x8280) u32data.data1[0]=temp1,u32data.data1[1]=temp2, FLOAT.DATA=(float)u32data.data, PLC_Addr++;//算出H值
    else if(Type_F == 0x8090) u32data.data1[0]=temp1,u32data.data1[1]=temp2, FLOAT.DATA=u32data.dataF, PLC_Addr++;//算出E值
    else if(Type_F == 0x8680) FLOAT.DATA=PLC_R_RAM_FLOAT(RAM_D8000_ADDR+temp1),PLC_Addr++;             //算出D的地址 D8000
    else if(Type_F == 0x8682) FLOAT.DATA=PLC_R_RAM_FLOAT(RAM_T_ADDR+temp1),PLC_Addr++;                 //算出T的地址
    else if(Type_F == 0x8684) FLOAT.DATA=PLC_R_RAM_FLOAT(RAM_C_ADDR+temp1),PLC_Addr++;                 //算出C的地址
    else if(Type_F == 0x8686) FLOAT.bata=PLC_R_RAM_FLOAT(RAM_D_ADDR+temp1),PLC_Addr++;                 //算出D的地址
    else if(Type_F == 0x8688) FLOAT.bata =PLC_R_RAM_FLOAT(RAM_D1000_ADDR+temp1),PLC_Addr++;             //大于等于D1000
    return FLOAT.DATA;
}

static void RST_D(void)
{
    uint8_t temp,addr,l_value;
    if(PLC_ACC_BIT&0x01)
    {
        l_value=*PLC_Addr;
        PLC_Addr++;
        addr=*PLC_Addr/0x100;

        if(addr==0x86)
            temp=l_value+((*PLC_Addr%0x100)*0x100), temp= HELON_REG_D8000_START_ADDR + temp / 2, plc_16BitBuf[temp]=0;
        else if(addr==0x88)
            temp=l_value+((*PLC_Addr%0x100)*0x100), temp= HELON_REG_D_START_ADDR + temp / 2, plc_16BitBuf[temp]=0;
        else if(addr==0x80)
            temp=l_value+((*PLC_Addr%0x100)*0x100), temp= HELON_REG_D_START_ADDR + temp / 2 + 1000, plc_16BitBuf[temp]=0;
        else
            PLC_PROG_ERROR(M8065,6501);
//		PLC_Addr++;
    }
    else PLC_Addr+=2;
}

//=======================================================================================================
// 函数名称:  static void target(void)
// 功能描述： 加减法“与”“或”“异或”共用赋值函数	如DEMOV、DEADD、DESUB等指令的结果传递出去
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2015年6月23日
// 备  注:
//=======================================================================================================
static void float_target(void)
{
    uint16_t temp;
    temp=addr_value() ;
    plc_16BitBuf[temp]=FLOAT.DATA1[0];
    plc_16BitBuf[temp+1]=FLOAT.DATA1[1];
    PLC_Addr+=2;
}

static void PID(void)
{
    int16_t PVn;  // 测量值
    int16_t SV;   // 设定目标Desired value
    int16_t Ts;   // 取样时间
    int32_t Su;
    int16_t KP; // P
    int16_t Ti; // I
    int16_t KD; // D
    int16_t TD; // 微分增益
    uint32_t Addr,Addr1; // 地址记录
    uint32_t csp;        // PID内部计算地址
    if((PLC_ACC_BIT&0X01)==0X01)        //母线成立不
    {
        SV=PLC_R_RAM_16BIT(addr_value_prog());   // 设定值
        PVn=PLC_R_RAM_16BIT(addr_value_prog());  // 测量数据
        Addr=addr_value_prog();            // 读取参数起始地址
        Addr1= addr_value_prog();
        Ts=PLC_R_RAM_16BIT(Addr);                // 取样时间
        KP=PLC_R_RAM_16BIT(Addr+6);  // P
        Ti=PLC_R_RAM_16BIT(Addr+8);  // I
        KD=PLC_R_RAM_16BIT(Addr+10); // D
        TD=PLC_R_RAM_16BIT(Addr+12); // 微分增益
        csp=Addr+14;           // 内部计算起始地址

        PLC_W_RAM_16BIT((csp+14),KP*((PLC_R_RAM_16BIT(csp)-PLC_R_RAM_16BIT(csp+2))+((Ts/Ti)*PLC_R_RAM_16BIT(csp))+PLC_R_RAM_16BIT(csp+10)));

        PLC_W_RAM_16BIT(csp,PLC_R_RAM_16BIT(csp+4) - SV);         // 计算本次偏差值

        PLC_W_RAM_16BIT((csp+4), PLC_R_RAM_16BIT(Addr+4)*PLC_R_RAM_16BIT(csp+6)+(1-PLC_R_RAM_16BIT(Addr+4))*PVn);

        PLC_W_RAM_16BIT((csp+10),(TD/(Ts+KD*TD))*(-2*PLC_R_RAM_16BIT(csp+6)+PLC_R_RAM_16BIT(csp+4)+PLC_R_RAM_16BIT(csp+8))+((KD*TD)/(Ts+KD*TD))*PLC_R_RAM_16BIT(csp+12));
        //PID运算公式：u(k) = Kp * e(k) + Ki * T * ∑e(i) + ( Kd / T ) * [Pv(k) – Pv(k-1) ]
        Su=PLC_R_RAM_16BIT(Addr1)+PLC_R_RAM_16BIT(csp+14);
        if(Su>32766)       PLC_W_RAM_16BIT(Addr1,32767);
        else if(Su<-32767) PLC_W_RAM_16BIT(Addr1,-32768);
        else PLC_W_RAM_16BIT(Addr1, Su);
        PLC_W_RAM_16BIT((csp+12),PLC_R_RAM_16BIT(csp+10));
        PLC_W_RAM_16BIT((csp+8),PLC_R_RAM_16BIT(csp+6));
        PLC_W_RAM_16BIT((csp+6),PLC_R_RAM_16BIT(csp+4));
        PLC_W_RAM_16BIT((csp+2),PLC_R_RAM_16BIT(csp));
    }
    else
    {
        PLC_Addr+=8;
    }
}

/* kevin，20171222 Read */
static void MOV(void)	          //MOV
{
    if(PLC_ACC_BIT&0X01)        // 判断条件满足否
    {
        trade=cos_value();
        target();
    }
    else
    {
        PLC_Addr+=4;		           // 条件不满足执行跳过程序，减小CPU开销
    }
}

// 2019年11月优化、新增
static void MOVP(void)	          //MOV
{
    if(PLC_LDP_TEST())         // 判断条件满足否
    {
        trade=cos_value();
        target();
    }
    else
    {
        PLC_Addr+=4;		           // 条件不满足执行跳过程序，减小CPU开销
    }
}

// 2019年11月优化、新增
static void DMOV(void)	        //单精度数据传递
{
    if(PLC_ACC_BIT&0X01)
    {
        trade=cos_u32_value();
        D_target();
    }
    else
        PLC_Addr+=8;		              //条件不满足执行跳过程序，减小CPU开销
}

// 2019年11月优化、新增
static void DMOVP(void)	        //单精度数据传递
{
    if(PLC_LDP_TEST())
    {
        trade=cos_u32_value();
        D_target();
    }
    else
        PLC_Addr+=8;		              //条件不满足执行跳过程序，减小CPU开销
}

static void DEMOV(void)	        //单精度数据传递
{
    if(PLC_ACC_BIT&0X01)
        trade=float_value(),float_target();
    else
        PLC_Addr+=8;		              //条件不满足执行跳过程序，减小CPU开销
}

// 2019年11月优化、新增
//======================================================================================================
// 函数名称: static void ZRST(void)
// 功能描述： ZRST指令函数
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2015年6月27日
// 备  注:
//=======================================================================================================
static void _ZRST(void)
{
    uint16_t temp,temp1;
    temp=addr_value();
    temp1=addr_value();
    if(Flag_bit==0x00) //复位 位变量
    {
        for(; temp<=temp1; temp++)
            PLC_BIT_OFF(temp);
    }
    else
    {
        for(; temp<=temp1; temp++)
            plc_16BitBuf[temp]=0;
    }
}

// 2019年11月优化、新增
static void ZRST(void)
{
    if((PLC_ACC_BIT&0X01)==0X01)_ZRST();
    else PLC_Addr+=4;
}

static void ZRSTP(void)
{
    if(PLC_LDP_TEST())_ZRST();
    else PLC_Addr+=4;
}

static void MTR(void)
{
    uint16_t X,Y,M_Y,K_H,temp=0;
    uint8_t i,t;
    if(PLC_ACC_BIT&0X01)
    {
        X=addr_value();
        Y=addr_value();
        M_Y=addr_value();
        K_H=cos_value();
        for(i=0; i<K_H; i++)
        {
            temp=i*7;
            PLC_BIT_ON(Y+i);
            for(t=0; t<=7; t++)
                (PLC_BIT_TEST(X+t)) ? PLC_BIT_ON(M_Y+temp+t) : PLC_BIT_OFF(M_Y+temp+t);
        }
    }
    else PLC_Addr+=8;
}

// 2019年11月优化、新增
static void REFF(void)
{
//    if(PLC_ACC_BIT&0X01)
//        X_DIY=cos_value();
//    else
//        X_DIY=10,PLC_Addr+=2;
}

static void HSCS(void)		//高速计数置位
{
    int32_t temp1,temp2;
    temp1=cos_u32_value();
    temp2=cos_u32_value();
    if((temp2==temp1)&&((PLC_ACC_BIT&0X01)==0X01))
        PLC_ACC_BIT|=1;
    else
        PLC_ACC_BIT&=~1;
}

//======================================================================================================
// 函数名称:  static void SQR(void)
// 功能描述： 16位右移位 RCR指令
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2015年6月27日
// 备  注:
//=======================================================================================================
static void SQR(void)
{
    if((PLC_ACC_BIT&0X01)==0X01)
    {
        trade=(uint16_t)sqrt((double)cos_value());
        target();
    }
}

// 2019年11月优化、新增
static void SQRP(void)
{
    if(PLC_LDP_TEST())
    {
        trade=(uint16_t)sqrt((double)cos_value());
        target();
    }
}

//======================================================================================================
// 函数名称:  static void DSQR(void)
// 功能描述： 32位开方计算 DSQR
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2015年6月27日
// 备  注:
//=======================================================================================================
static void DSQR(void)
{
    if((PLC_ACC_BIT&0X01)==0X01)
    {
        trade=(uint32_t)sqrt((double)cos_u32_value());
        D_target();	//??
    }
}

static void DSQRP(void)
{
    if(PLC_LDP_TEST())
    {
        trade=(uint32_t)sqrt((double)cos_u32_value());
        D_target();	//??
    }
}

// 2019年11月优化、新增
//======================================================================================================
// 函数名称:  static void DRCR(void)
// 功能描述： 32位右移位 RCR指令
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2015年6月27日
// 备  注:
//=======================================================================================================
static void DRCR(void)
{
    uint32_t temp1,temp2;
    if((PLC_ACC_BIT&0X01)==0X01)
    {
        temp1=cos_u32_value();
        temp2=cos_u32_value();
        trade=temp1>>temp2;
        PLC_Addr-=8;
        D_target();
        PLC_Addr+=4;
    }
}

static void DRCRP(void)
{
    uint32_t temp1,temp2;
    if(PLC_LDP_TEST())
    {
        temp1=cos_u32_value();
        temp2=cos_u32_value();
        trade=temp1>>temp2;
        PLC_Addr-=8;
        D_target();
        PLC_Addr+=4;
    }
}
//======================================================================================================
// 函数名称:  static void RCR(void)
// 功能描述： 16位右移位 RCR指令
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2015年6月27日
// 备  注:
//=======================================================================================================
static void RCR(void)
{
    uint16_t temp1,temp2;
    if((PLC_ACC_BIT&0X01)==0X01)
    {
        temp1=cos_value();
        temp2=cos_value();
        trade=temp1>>temp2;
        PLC_Addr-=4;
        target();
        PLC_Addr+=2;
    }
    else PLC_Addr+=4;                       //没有动作跳过4步程序
}

// 2019年11月优化、新增
static void RCRP(void)
{
    uint16_t temp1,temp2;
    if(PLC_LDP_TEST())
    {
        temp1=cos_value();
        temp2=cos_value();
        trade=temp1>>temp2;
        PLC_Addr-=4;
        target();
        PLC_Addr+=2;
    }
    else PLC_Addr+=4;                       //没有动作跳过4步程序
}

// 2019年11月优化、新增
//======================================================================================================
// 函数名称:  static void DROL(void)
// 功能描述： 32位左移位 RCL指令
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2015年6月27日
// 备  注:
//=======================================================================================================
static void DRCL(void)
{
    uint32_t temp1,temp2;
    if((PLC_ACC_BIT&0X01)==0X01)
    {
        temp1=cos_u32_value();
        temp2=cos_u32_value();
        trade=temp1<<temp2;
        PLC_Addr-=8;
        D_target();
        PLC_Addr+=4;
    }
}

static void DRCLP(void)
{
    uint32_t temp1,temp2;
    if(PLC_LDP_TEST())
    {
        temp1=cos_u32_value();
        temp2=cos_u32_value();
        trade=temp1<<temp2;
        PLC_Addr-=8;
        D_target();
        PLC_Addr+=4;
    }
}
//======================================================================================================
// 函数名称:  static void ROL(void)
// 功能描述： 16位左移位 RCL指令
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2015年6月27日
// 备  注:
//=======================================================================================================
static void RCL(void)
{
    uint16_t temp1,temp2;
    if((PLC_ACC_BIT&0X01)==0X01)
    {
        temp1=cos_value();
        temp2=cos_value();
        trade=temp1<<temp2;
        PLC_Addr-=4;
        target();
        PLC_Addr+=2;
    }
    else PLC_Addr+=4;                      //没有动作跳过4步程序
}

// 2019年11月优化、新增
static void RCLP(void)
{
    uint16_t temp1,temp2;
    if(PLC_LDP_TEST())
    {
        temp1=cos_value();
        temp2=cos_value();
        trade=temp1<<temp2;
        PLC_Addr-=4;
        target();
        PLC_Addr+=2;
    }
    else PLC_Addr+=4;                      //没有动作跳过4步程序
}

//======================================================================================================
// 函数名称:  static void DROR(void)
// 功能描述： 32位循环右位 ROR指令
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2015年6月27日
// 备  注:
//=======================================================================================================
static void DROR(void)
{
    uint32_t temp1,temp2;
    if((PLC_ACC_BIT&0X01)==0X01)
    {
        temp1=cos_u32_value();
        temp2=cos_u32_value();
        trade=ROTATE_RIGHT(temp1,32,temp2);
        PLC_Addr-=8;
        D_target();
        PLC_Addr+=4;
    }
}

// 2019年11月优化、新增
static void DRORP(void)
{
    uint32_t temp1,temp2;
    if(PLC_LDP_TEST())
    {
        temp1=cos_u32_value();
        temp2=cos_u32_value();
        trade=ROTATE_RIGHT(temp1,32,temp2);
        PLC_Addr-=8;
        D_target();
        PLC_Addr+=4;
    }
}

// 2019年11月优化、新增
//======================================================================================================
// 函数名称:  static void ROR(void)
// 功能描述： 16位循环右位 ROR指令
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2015年6月27日
// 备  注:
//=======================================================================================================
static void ROR(void)
{
    uint16_t temp1,temp2;
    if((PLC_ACC_BIT&0X01)==0X01)
    {
        temp1=cos_value();
        temp2=cos_value();
        trade=ROTATE_RIGHT(temp1,16,temp2);
        PLC_Addr-=4;
        target();
        PLC_Addr+=2;
    }
    else PLC_Addr+=4;                      //没有动作跳过4步程序
}

static void RORP(void)
{
    uint16_t temp1,temp2;
    if(PLC_LDP_TEST())
    {
        temp1=cos_value();
        temp2=cos_value();
        trade=ROTATE_RIGHT(temp1,16,temp2);
        PLC_Addr-=4;
        target();
        PLC_Addr+=2;
    }
    else PLC_Addr+=4;                      //没有动作跳过4步程序
}

//======================================================================================================
// 函数名称:  static void DROL(void)
// 功能描述： 32位循环左移位 ROL指令
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2015年6月27日
// 备  注:
//=======================================================================================================
static void DROL(void)
{
    uint32_t temp1,temp2;
    if((PLC_ACC_BIT&0X01)==0X01)
    {
        temp1=cos_u32_value();
        temp2=cos_u32_value();
        trade=ROTATE_LEFT(temp1,32,temp2);
        PLC_Addr-=8;
        D_target();
        PLC_Addr+=4;
    }
}

// 2019年11月优化、新增
static void DROLP(void)
{
    uint32_t temp1,temp2;
    if(PLC_LDP_TEST())
    {
        temp1=cos_u32_value();
        temp2=cos_u32_value();
        trade=ROTATE_LEFT(temp1,32,temp2);
        PLC_Addr-=8;
        D_target();
        PLC_Addr+=4;
    }
}

//======================================================================================================
// 函数名称:  static void ROL(void)
// 功能描述： 16位循环左移位 ROL指令
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2015年6月27日
// 备  注:
//=======================================================================================================
static void ROL(void)
{
    uint16_t temp1,temp2;
    if((PLC_ACC_BIT&0X01)==0X01)
    {
        temp1=cos_value();
        temp2=cos_value();
        trade=ROTATE_LEFT(temp1,16,temp2);
        PLC_Addr-=4;
        target();
        PLC_Addr+=2;
    }
    else PLC_Addr+=4;                      //没有动作跳过4步程序
}

// 2019年11月优化、新增
static void ROLP(void)
{
    uint16_t temp1,temp2;
    if(PLC_LDP_TEST())
    {
        temp1=cos_value();
        temp2=cos_value();
        trade=ROTATE_LEFT(temp1,16,temp2);
        PLC_Addr-=4;
        target();
        PLC_Addr+=2;
    }
    else PLC_Addr+=4;                      //没有动作跳过4步程序
}

// kevin，2019年11月优化、新增
//=======================================================================================================
// 函数名称:  static void DGBIN(void)
// 功能描述： 16位上下交换 DGBIN指令
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2015年8月4日
// 备  注:
//=======================================================================================================
static void _DGBIN(void)
{
    signed int temp;
    temp=cos_value();
    trade=GtoB(temp);
    D_target();
}

static void DGBIN(void)
{
    if((PLC_ACC_BIT&0X01)==0X01)_DGBIN();
    else PLC_Addr+=8;                      //没有动作跳过8步程序
}

static void DGBINP(void)
{
    if(PLC_LDP_TEST())_DGBIN();
    else PLC_Addr+=8;                      //没有动作跳过8步程序
}

// kevin，2019年11月优化、新增
//=======================================================================================================
// 函数名称:  static void GBIN(void)
// 功能描述： 16位上下交换 GBIN指令
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2015年6月27日
// 备  注:
//=======================================================================================================
static void _GBIN(void)
{
    signed short int temp;
    temp=cos_value();
    trade=(uint16_t)GtoB((unsigned int)temp);
    target();
}

static void GBIN(void)
{
    if((PLC_ACC_BIT&0X01)==0X01)_GBIN();
    else PLC_Addr+=4;                      //没有动作跳过4步程序
}

static void GBINP(void)
{
    if(PLC_LDP_TEST())_GBIN();
    else PLC_Addr+=4;                      //没有动作跳过4步程序
}

// kevin，2019年11月优化、新增
//=======================================================================================================
// 函数名称:  static void DGRY(void)
// 功能描述： 32位上下交换 DGRY指令
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2015年8月4日
// 备  注:
//=======================================================================================================
static void _DGRY(void)
{
    signed int temp;
    temp=cos_value();
    trade=BtoG(temp);
    D_target();
}

static void DGRY(void)
{
    if((PLC_ACC_BIT&0X01)==0X01)_DGRY();
    else PLC_Addr+=8;                      //没有动作跳过8步程序
}

static void DGRYP(void)
{
    if(PLC_LDP_TEST())_DGRY();
    else PLC_Addr+=8;                      //没有动作跳过8步程序
}

// kevin，2019年11月优化、新增
//=======================================================================================================
// 函数名称:  static void GRY(void)
// 功能描述： 16位上下交换 GRY指令
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2015年6月27日
// 备  注:
//=======================================================================================================
static void _GRY(void)
{
    signed short int temp;
    temp=cos_value();
    trade=(uint16_t)BtoG((unsigned int)temp);
    target();
}

static void GRY(void)
{
    if((PLC_ACC_BIT&0X01)==0X01)_GRY();
    else PLC_Addr+=4;                      //没有动作跳过4步程序
}

static void GRYP(void)
{
    if(PLC_LDP_TEST())_GRY();
    else PLC_Addr+=4;                      //没有动作跳过4步程序
}

//=======================================================================================================
// 函数名称:  static void SWAP(void)
// 功能描述： 16位上下交换 SWAP指令
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2015年6月27日
// 备  注:
//=======================================================================================================
static void SWAP(void)
{
    signed short int temp;
    if((PLC_ACC_BIT&0X01)==0X01)
    {
        temp=cos_value();
        trade=swap_u16(temp);
        PLC_Addr-=2;
        target();
    }
    else PLC_Addr+=2;                      //没有动作跳过4步程序
}

// kevin，2019年11月优化、新增
static void SWAPP(void)
{
    signed short int temp;
    if(PLC_LDP_TEST())
    {
        temp=cos_value();
        trade=swap_u16(temp);
        PLC_Addr-=2;
        target();
    }
    else PLC_Addr+=2;                      //没有动作跳过4步程序
}

// kevin，2019年11月优化、新增
//======================================================================================================
// 函数名称:  static void DSWAP(void)
// 功能描述： 32位上下交换 DSWAP指令
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2015年6月27日
// 备  注:
//=======================================================================================================
static void DSWAP(void)
{
    uint32_t temp;
    if((PLC_ACC_BIT&0X01)==0X01)
    {
        temp=cos_u32_value();
        trade=swap_u32(temp);
        PLC_Addr-=4;
        D_target();
    }
    else PLC_Addr+=8;                    //??
}

static void DSWAPP(void)
{
    uint32_t temp;
    if(PLC_LDP_TEST())
    {
        temp=cos_u32_value();
        trade=swap_u32(temp);
        PLC_Addr-=4;
        D_target();
    }
    else PLC_Addr+=8;                    //??
}

// kevin，2019年11月优化、新增
//=======================================================================================================
// 函数名称:  static void SFTR(void)
// 功能描述： SFTR指令
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2019年4月8日
// 备  注:
//=======================================================================================================
static void _SFTR(void)	           //2019年4月8日 位右移
{
    uint16_t s1,s2,n1,n2,temp;
    uint16_t i;
    s1=addr_value(); //S源地址
    s2=addr_value(); //D目标地址
    n1=cos_value(); //n1移动寄存器长度
    n2=cos_value(); //n2移动数量
    if(n2<=n1) temp = n1-n2;
    else return;
    for(i=0; i<temp; i++)
    {
        (PLC_BIT_TEST(s2+n2+i)) ? PLC_BIT_ON(s2+i) : PLC_BIT_OFF(s2+i);
    }
    for(i=0; i<n2; i++)
    {
        (PLC_BIT_TEST(s1+i)) ? PLC_BIT_ON(s2+i+temp) : PLC_BIT_OFF(s2+i+temp);
    }
}

static void SFTR(void)
{
    if(PLC_ACC_BIT&0X01)_SFTR();
    else PLC_Addr+=8;                      //没有动作跳过4步程序
}

static void SFTRP(void)
{
    if(PLC_LDP_TEST())_SFTR();
    else PLC_Addr+=8;                      //没有动作跳过4步程序
}

static void _SFTL(void)	             //位左移
{
    uint16_t s1,s2,n1,n2,temp;
    int16_t i;
    s1=addr_value(); //S源地址
    s2=addr_value(); //D目标地址
    n1=cos_value(); //n1	移动寄存器长度
    n2=cos_value(); //n2移动数量
    if(n2<=n1) temp = n1-n2;
    else return;
    for(i=temp-1; i>=0; i--)
    {
        (PLC_BIT_TEST(s2+i)) ? PLC_BIT_ON(s2+n2+i) : PLC_BIT_OFF(s2+n2+i);
    }
    for(i=0; i<n2; i++)
    {
        (PLC_BIT_TEST(s1+i)) ? PLC_BIT_ON(s2+i) : PLC_BIT_OFF(s2+i);
    }
}

static void SFTL(void)
{
    if(PLC_ACC_BIT&0X01)_SFTL();
    else PLC_Addr+=8;                      //没有动作跳过4步程序
}
static void SFTLP(void)
{
    if(PLC_LDP_TEST())_SFTL();
    else PLC_Addr+=8;                      //没有动作跳过4步程序
}

static void _WSFR(void)	           //2019年4月8日 字右移
{
    uint16_t s1,s2,n1,n2,temp;
    uint16_t i;
    s1=addr_value(); //S源地址
    s2=addr_value(); //D目标地址
    Flag_bit = 0xFF;
    n1=cos_value(); //n1	移动寄存器长度
    n2=cos_value(); //n2移动数量
    if(n2<=n1) temp = n1-n2;
    else return;
    for(i=0; i<temp; i++) //先移
    {
        plc_16BitBuf[s2+i] = plc_16BitBuf[s2+n2+i];
    }
    for(i=0; i<n2; i++) //赋新值
    {
        plc_16BitBuf[s2+i+temp]=plc_16BitBuf[s1+i];
    }
}

static void WSFR(void)
{
    if(PLC_ACC_BIT&0X01)_WSFR();
    else PLC_Addr+=8;
}

static void WSFRP(void)
{
    if(PLC_LDP_TEST())_WSFR();
    else PLC_Addr+=8;
}

static void _WSFL(void)	             //字左移
{
    uint16_t s1,s2,n1,n2,temp;
    int16_t i;
    s1=addr_value(); //S源地址
    s2=addr_value(); //D目标地址
    Flag_bit = 0xFF;
    n1=cos_value(); //n1	移动寄存器长度
    n2=cos_value(); //n2移动数量
    if(n2<=n1) temp = n1-n2;
    else return;
    for(i=temp-1; i>=0; i--)
    {
        plc_16BitBuf[s2+n2+i] = plc_16BitBuf[s2+i];
    }
    for(i=0; i<n2; i++)
    {
        plc_16BitBuf[s2+i] = plc_16BitBuf[s1+i];
    }
}

static void WSFL(void)
{
    if(PLC_ACC_BIT&0X01)_WSFL();
    else PLC_Addr+=8;                      //没有动作跳过4步程序
}

static void WSFLP(void)
{
    if(PLC_LDP_TEST())_WSFL();
    else PLC_Addr+=8;                      //没有动作跳过4步程序
}

static void _SFWR(void)	             //移位写入 先入先出
{
    uint16_t s,d,n,temp;
    s = cos_value();
    d = addr_value();
    Flag_bit = 0xFF;
    n = cos_value();
    temp = plc_16BitBuf[d]+1;
    if(temp<n)
    {
        plc_16BitBuf[d+temp] = s;
        plc_16BitBuf[d]++;
    }
    else
    {
        PLC_BIT_ON(M8022); //进位标志
    }
}

static void SFWR(void)
{
    if(PLC_ACC_BIT&0X01)_SFWR();
    else PLC_Addr+=6;                      //没有动作跳过4步程序
}

static void SFWRP(void)
{
    if(PLC_LDP_TEST())_SFWR();
    else PLC_Addr+=6;                      //没有动作跳过4步程序
}

static void _SFRD(void)   //20190408 移位读出 先入先出
{
    uint16_t s,d,n,temp;
    s = addr_value();
    d = addr_value();
    Flag_bit = 0xFF;
    n = cos_value();
    temp = plc_16BitBuf[s];
    if(temp>0&&temp<n)//有数据可读
    {
        plc_16BitBuf[d] = plc_16BitBuf[s+1];
        for(temp=1; temp<plc_16BitBuf[s]; temp++)
        {
            plc_16BitBuf[s+temp] = plc_16BitBuf[s+temp+1];

        }
        plc_16BitBuf[s+temp] = 0;
        plc_16BitBuf[s]--;
    }
    else PLC_BIT_ON(M8020);
}

// kevin，2019年11月优化、新增
static void SFRD(void)
{
    if(PLC_ACC_BIT&0X01)_SFRD();
    else PLC_Addr+=6;                      //没有动作跳过4步程序
}

static void SFRDP(void)
{
    if(PLC_LDP_TEST())_SFRD();
    else PLC_Addr+=6;                      //没有动作跳过4步程序
}

//=======================================================================================================
// 函数名称:  static void XCH(void)
// 功能描述： 16位交换传送 XCH指令
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2015年6月27日
// 备  注:
//=======================================================================================================
static void XCH(void)
{
    signed short int temp1,temp2;
    if((PLC_ACC_BIT&0X01)==0X01)
    {
        temp1=cos_value();
        temp2=cos_value();
        PLC_Addr-=4;
        trade=temp2;
        target();//??
        trade=temp1;
        target();//??
    }
    else PLC_Addr+=4;                      //没有动作跳过4步程序
}

// kevin，2019年11月优化、新增
static void XCHP(void)
{
    signed short int temp1,temp2;
    if(PLC_LDP_TEST())
    {
        temp1=cos_value();
        temp2=cos_value();
        PLC_Addr-=4;
        trade=temp2;
        target();//??
        trade=temp1;
        target();//??
    }
    else PLC_Addr+=4;                      //没有动作跳过4步程序
}

// kevin，2019年11月优化、新增
//=======================================================================================================
// 函数名称:  static void DFMOV(void)
// 功能描述： 32位交换传送 DXCH指令
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2015年6月27日
// 备  注:
//=======================================================================================================
static void DXCH(void)
{
    signed int temp1,temp2;
    if((PLC_ACC_BIT&0X01)==0X01)
    {
        temp1=cos_u32_value();
        temp2=cos_u32_value();
        PLC_Addr-=8;
        trade=temp2;
        D_target();
        trade=temp1;
        D_target();
    }
    else PLC_Addr+=8;                    //??
}

static void DXCHP(void)
{
    signed int temp1,temp2;
    if(PLC_LDP_TEST())
    {
        temp1=cos_u32_value();
        temp2=cos_u32_value();
        PLC_Addr-=8;
        trade=temp2;
        D_target();
        trade=temp1;
        D_target();
    }
    else PLC_Addr+=8;                    //??
}

// kevin，2019年11月优化、新增
//=======================================================================================================
// 函数名称:  static void DFMOV(void)
// 功能描述： 32位多点传送 DFMOV指令
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2015年6月27日
// 备  注:
//=======================================================================================================
static void _DFMOV(void)
{
    signed short int temp,i;
    if((PLC_ACC_BIT&0X01)==0X01)
    {
        trade=cos_u32_value();            //要传递的数据
        D_target();                       //把第一个传递出去
        temp=cos_u32_value();             //            <<<-------------|
        PLC_Addr-=4;                      //PLC_Addr-=4是为了调回到上面 |<<-----|
        for(i=1; i<temp; i++)             //                                    |
        {   //                                    |
            if(Transfer_bit==1)Transfer=i*32;//                                  |
            else Transfer=i*4;             //                                    |
            PLC_Addr-=4;
            D_target();        //PLC_Addr-=4是为了传递出去要的位数	  |
        }                                 //                                    |
        PLC_Addr+=2;
        Transfer=0;           //PLC_Addr+=2是为了跳过上面调回去的---|
    }
}

static void DFMOV(void)
{
    if((PLC_ACC_BIT&0X01)==0X01)_DFMOV();
    else PLC_Addr+=12;                    //??
}

static void DFMOVP(void)
{
    if(PLC_LDP_TEST())_DFMOV();
    else PLC_Addr+=12;                    //??
}

// kevin，2019年11月优化、新增
//=======================================================================================================
// 函数名称:  static void FMOV(void)
// 功能描述： 16位多点传送 FMOV指令
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2015年6月27日
// 备  注:
//=======================================================================================================
static void _FMOV(void)
{
    signed short int temp,i;
    trade=cos_value();                //要传递的数据
    target();                         //把第一个传递出去
    temp=cos_value();                 //            <<<-------------|
    PLC_Addr-=2;                      //PLC_Addr-=2是为了调回到上面 |<<-----|
    for(i=1; i<temp; i++)             //                                    |
    {   //                                    |
        if(Transfer_bit==1)
            Transfer=i*16;//                                  |
        else
            Transfer=i*2;             //                                    |
        PLC_Addr-=2;
        target();          //PLC_Addr-=2是为了传递出去要的位数	  |
    }                                 //                                    |
    PLC_Addr+=2;
    Transfer=0;           //PLC_Addr+=2是为了跳过上面调回去的---|
}

static void FMOV(void)
{
    if((PLC_ACC_BIT&0X01)==0X01)_FMOV();
    else
    {
        PLC_Addr+=6;                    //没有动作跳过6步程序
    }
}

static void FMOVP(void)
{
    if(PLC_LDP_TEST())_FMOV();
    else
    {
        PLC_Addr+=6;                    //没有动作跳过6步程序
    }
}

// kevin，2019年11月优化、新增
//=======================================================================================================
// 函数名称:  static void FMOV(void)
// 功能描述： 16位成批传送 BMOV指令
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2015年6月27日
// 备  注:
//=======================================================================================================
static void _BMOV(void)
{
    signed short int n,i;
    trade=cos_value();                //要传递的数据
    target();                         //把第一个传递出去
    n=cos_value();

    PLC_Addr-=2;                      //<<<---------------------------------|
    for(i = 1; i < n; i++)
    {
        if(Transfer_bit1==1)
            Transfer=i*16;
        else
            Transfer=i*2;

        PLC_Addr-=4;
        trade=cos_value();                //要传递的数据

        if(Transfer_bit==1)
            Transfer =i*16;
        else
            Transfer =i*2;
        target();                         //把第传递出去
    }
    PLC_Addr+=2;                        //PLC_Addr+=2是为了跳过上面调回去的-----|
    Transfer=0;
}

static void BMOV(void)
{
    if((PLC_ACC_BIT&0X01)==0X01)_BMOV();
    else
    {
        PLC_Addr+=6;                          //按键没按下就跳过6步程序
    }
}

static void BMOVP(void)
{
    if(PLC_LDP_TEST())_BMOV();
    else
    {
        PLC_Addr+=6;                          //按键没按下就跳过6步程序
    }
}

// kevin，2019年11月优化、新增
static void DCML(void)	                 //32位数据取反
{
    int32_t temp1;
    if((PLC_ACC_BIT&0X01)==0X01)
    {
        temp1=cos_u32_value();
        trade=~temp1;
        D_target();
    }
    else PLC_Addr+=8;                     //跳过8步程序
}
static void DCMLP(void)	                 //32位数据取反
{
    int32_t temp1;
    if(PLC_LDP_TEST())
    {
        temp1=cos_u32_value();
        trade=~temp1;
        D_target();
    }
    else PLC_Addr+=8;                     //跳过8步程序
}

static void CML(void)	                    //数据取反
{
    signed short int temp1;
    if((PLC_ACC_BIT&0X01)==0X01)
    {
        temp1=cos_value();
        trade=~temp1;
        target();
    }
    else PLC_Addr+=4;                   //跳过4步程序
}

// kevin，2019年11月优化、新增
static void CMLP(void)	                    //数据取反
{
    signed short int temp1;
    if(PLC_LDP_TEST())
    {
        temp1=cos_value();
        trade=~temp1;
        target();
    }
    else PLC_Addr+=4;                   //跳过4步程序
}

// kevin，2019年11月优化、新增
uint16_t bcd[4]= {0x1,0x10,0x100,0x1000};
static void _SMOV(void)	          //16位比较传送指令
{
    uint16_t temp1,temp2,temp3,temp4,temp5,temp6;
    temp1=cos_value();
    temp2=cos_value();
    temp3=cos_value();
    temp4=addr_value();
    temp5=cos_value();
    temp1%=bcd[temp2];
    for(temp6=0; temp6<temp3; temp6++)
    {
        temp2--;
        temp5--;
        plc_16BitBuf[temp4]|=(temp1/bcd[temp2])*bcd[temp5];
        if((temp2==1)&&(temp5==1))plc_16BitBuf[temp4]|=temp1%0x10*bcd[temp5], temp6=temp3+1;
        else temp1%=bcd[temp2];
    }
}

static void SMOV(void)	          //16位比较传送指令
{
    if((PLC_ACC_BIT&0X01)==0X01)_SMOV();
    else PLC_Addr+=10;              //跳过10步程序
}

static void SMOVP(void)	          //16位比较传送指令
{
    if(PLC_LDP_TEST())_SMOV();
    else PLC_Addr+=10;              //跳过10步程序
}

// kevin，2019年11月优化、新增
static void _DZCP(void)	          //16位比较传送指令
{
    int32_t temp1,temp2,temp3,temp4;
    temp1=cos_u32_value();
    temp2=cos_u32_value();
    temp3=cos_u32_value();
    temp4=addr_value();
    Flag_bit=0XFF;
    PLC_BIT_OFF(temp4);
    PLC_BIT_OFF(temp4+1);
    PLC_BIT_OFF(temp4+2);
    if(temp1>temp3)       PLC_BIT_ON(temp4);
    else if((temp1<=temp3)&&(temp3<=temp2)) PLC_BIT_ON(temp4+1);
    else if(temp2<temp3)  PLC_BIT_ON(temp4+2);
    PLC_Addr+=2;
}

static void DZCP(void)	          //16位比较传送指令
{
    if((PLC_ACC_BIT&0X01)==0X01)_DZCP();
    else PLC_Addr+=16;              //跳过16步程序
}

static void DZCPP(void)	          //16位比较传送指令
{
    if(PLC_LDP_TEST())_DZCP();
    else PLC_Addr+=16;              //跳过16步程序
}

static void _ZCP(void)	            //16位比较传送指令
{
    signed short int temp1,temp2,temp3,temp4;
    temp1=cos_value();
    temp2=cos_value();
    temp3=cos_value();
    temp4=addr_value();
    Flag_bit=0XFF;
    PLC_BIT_OFF(temp4);
    PLC_BIT_OFF(temp4+1);
    PLC_BIT_OFF(temp4+2);
    if(temp1>temp3)       PLC_BIT_ON(temp4);
    else if((temp1<=temp3)&&(temp3<=temp2)) PLC_BIT_ON(temp4+1);
    else if(temp2<temp3)  PLC_BIT_ON(temp4+2);
}

static void ZCP(void)	            //16位比较传送指令
{
    if((PLC_ACC_BIT&0X01)==0X01)_ZCP();
    else
    {
        PLC_Addr+=8;              //跳过8步程序
    }
}

static void ZCPP(void)	            //16位比较传送指令
{
    if(PLC_LDP_TEST())_ZCP();
    else
    {
        PLC_Addr+=8;              //跳过8步程序
    }
}

// kevin，2019年11月优化、新增
//======================================================================================================
// 函数名称: static void TCMP(void)
// 功能描述：TCMP指令函数
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2015年7月26日
// 备  注:
//=======================================================================================================
static void _TCMP(void)
{
    uint16_t h,min,s,temp,temp1;
    h=cos_value();
    min=cos_value();
    s=cos_value();
    temp=addr_value();
    temp1=addr_value();
    Flag_bit=0XFF;
    PLC_BIT_OFF(temp1);
    PLC_BIT_OFF(temp1+1);
    PLC_BIT_OFF(temp1+2);
    if( (h>plc_16BitBuf[temp]) ||
        ((h==plc_16BitBuf[temp])&&(min>plc_16BitBuf[temp+1])) ||
        ((h==plc_16BitBuf[temp])&&(min==plc_16BitBuf[temp+1])&&(s>plc_16BitBuf[temp+2]))
            )
    {
        PLC_BIT_ON(temp1);
    }
    else if((h==plc_16BitBuf[temp])&&(min==plc_16BitBuf[temp+1])&&(s==plc_16BitBuf[temp+2]))
    {
        PLC_BIT_ON(temp1+1);
    }
    else if((h<=plc_16BitBuf[temp])&&(min<=plc_16BitBuf[temp+1])&&(s<plc_16BitBuf[temp+2]))
    {
        PLC_BIT_ON(temp1+2);
    }
}

static void TCMP(void)
{
    if((PLC_ACC_BIT&0X01)==0X01)_TCMP();
    else PLC_Addr+=10;
}

static void TCMPP(void)
{
    if(PLC_LDP_TEST())_TCMP();
    else PLC_Addr+=10;
}

// kevin，2019年11月优化、新增
//======================================================================================================
// 函数名称: static void TZCP(void)
// 功能描述：TZCP指令函数
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2015年7月26日
// 备  注:
//=======================================================================================================
static void _TZCP(void)
{
    uint16_t h,min,s,temp,temp1,temp3,temp4,h1,min1,s1;
    if((PLC_ACC_BIT&0X01)==0X01)
    {
        temp3=addr_value();               //S1 区域比较下限值
        h=plc_16BitBuf[temp3];
        min=plc_16BitBuf[temp3+1];
        s=plc_16BitBuf[temp3+2];

        temp4=addr_value();               //S2 区域比较上限值
        h1=plc_16BitBuf[temp4];
        min1=plc_16BitBuf[temp4+1];
        s1=plc_16BitBuf[temp4+2];

        temp=addr_value();                //S3 时间比较值

        temp1=addr_value();
        Flag_bit=0XFF;
        PLC_BIT_OFF(temp1);
        PLC_BIT_OFF(temp1+1);
        PLC_BIT_OFF(temp1+2);
        if((h>=plc_16BitBuf[temp])&&(min>=plc_16BitBuf[temp+1])&&(s>plc_16BitBuf[temp+2]))
        {
            PLC_BIT_ON(temp1);
        }
        else if(((h<=plc_16BitBuf[temp])&&(min<=plc_16BitBuf[temp+1])&&(s<=plc_16BitBuf[temp+1]))&&((h1>=plc_16BitBuf[temp])&&(min1>=plc_16BitBuf[temp+1])&&(s1>=plc_16BitBuf[temp+2])))
        {
            PLC_BIT_ON(temp1+1);
        }
        else if((h1<=plc_16BitBuf[temp])&&(min1<=plc_16BitBuf[temp+1])&&(s1<plc_16BitBuf[temp+2]))
        {
            PLC_BIT_ON(temp1+2);
        }
    }
}

// kevin，2019年11月优化、新增
static void TZCP(void)
{
    if((PLC_ACC_BIT&0X01)==0X01) _TZCP();
    else PLC_Addr+=8;//??
}

static void TZCPP(void)
{
    if(PLC_LDP_TEST()) _TZCP();
    else PLC_Addr+=8;//??
}

//======================================================================================================
// 函数名称: static void TADD(void)
// 功能描述：TADD指令函数
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2015年7月26日
// 备  注:
//=======================================================================================================
static void _TADD(void)
{
    uint16_t temp,temp1,temp2;
    temp1=addr_value();
    temp2=addr_value();
    temp=addr_value();
    plc_16BitBuf[temp]=plc_16BitBuf[temp1]+plc_16BitBuf[temp2];
    plc_16BitBuf[temp+1]=plc_16BitBuf[temp1+1]+plc_16BitBuf[temp2+1];
    plc_16BitBuf[temp+2]=plc_16BitBuf[temp1+2]+plc_16BitBuf[temp2+2];
}

static void TADD(void)
{
    if(PLC_ACC_BIT&0X01)_TADD();
    else PLC_Addr+=6;//??
}

static void TADDP(void)
{
    if(PLC_LDP_TEST())_TADD();
    else PLC_Addr+=6;//??
}
//======================================================================================================
// 函数名称: static void TSUB(void)
// 功能描述：TSUB指令函数
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2015年7月26日
// 备  注:
//=======================================================================================================
static void _TSUB(void)
{
    uint16_t temp,temp1,temp2;
    temp1=addr_value();
    temp2=addr_value();
    temp=addr_value();
    plc_16BitBuf[temp]=plc_16BitBuf[temp1]-plc_16BitBuf[temp2];
    plc_16BitBuf[temp+1]=plc_16BitBuf[temp1+1]-plc_16BitBuf[temp2+1];
    plc_16BitBuf[temp+2]=plc_16BitBuf[temp1+2]-plc_16BitBuf[temp2+2];
}

static void TSUB(void)
{
    if(PLC_ACC_BIT&0X01)_TSUB();
    else PLC_Addr+=6;//??
}

static void TSUBP(void)
{
    if(PLC_LDP_TEST())_TSUB();
    else PLC_Addr+=6;//??
}

//======================================================================================================
// 函数名称: static void TRD(void)
// 功能描述：TRD指令函数
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2015年7月26日
// 备  注:
//=======================================================================================================
static void _TRD(void)
{
    uint16_t temp;
    temp=addr_value();
    // kevin，20180524修改
    plc_16BitBuf[temp] =D8018; // 年
    plc_16BitBuf[temp+1] =D8017; // 月
    plc_16BitBuf[temp+2] =D8016; // 日
    plc_16BitBuf[temp+3] =D8015; // 时
    plc_16BitBuf[temp+4] =D8014; // 分
    plc_16BitBuf[temp+5] =D8013; // 秒
    plc_16BitBuf[temp+6] =D8019; // 星期
}

static void TRD(void)
{
    if(PLC_ACC_BIT&0X01)_TRD();
    else PLC_Addr+=2;              //跳过2步程序
}

static void TRDP(void)
{
    if(PLC_LDP_TEST())_TRD();
    else PLC_Addr+=2;              //跳过2步程序
}
//======================================================================================================
// 函数名称: static void TWR(void)
// 功能描述：TWR指令函数
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2015年7月26日
// 备  注:  SD2068 增加星期的输入，放在最后
//=======================================================================================================
static void _TWR(void)
{
    uint16_t temp,week;
    temp=addr_value();
#if RTC_FUNC
    RTC_Set(plc_16BitBuf[temp],plc_16BitBuf[temp+1],plc_16BitBuf[temp+2]-1,plc_16BitBuf[temp+3],plc_16BitBuf[temp+4],plc_16BitBuf[temp+5]);
#endif

//#if RTC_SD2068
//
//    week=RTC2086_Get_Week(plc_16BitBuf[temp],plc_16BitBuf[temp+1],plc_16BitBuf[temp+2]);
//    //RTC_Set(plc_16BitBuf[temp],plc_16BitBuf[temp+4],plc_16BitBuf[temp+3],plc_16BitBuf[temp+2],plc_16BitBuf[temp+1],plc_16BitBuf[temp]);
//    I2CWriteDate(plc_16BitBuf[temp+5],plc_16BitBuf[temp+4],plc_16BitBuf[temp+3],week,plc_16BitBuf[temp+2],plc_16BitBuf[temp+1],plc_16BitBuf[temp]);
//#endif

}

static void TWR(void)
{
    if(PLC_ACC_BIT&0X01)_TWR();
    else PLC_Addr+=2;              //跳过2步程序
}

static void TWRP(void)
{
    if(PLC_LDP_TEST())_TWR();
    else PLC_Addr+=2;              //跳过2步程序
}

static void HOUR(void) //计时器
{
    uint16_t temp;
    uint16_t addr1,addr2;
    if((PLC_ACC_BIT&0X01)&&PLC_BIT_PLS_TEST(M8013))
    {
        temp = cos_value();
        addr1 = addr_value();
        addr2 = addr_value();
        plc_16BitBuf[addr1+1]++;
        if(plc_16BitBuf[addr1+1]>=3600)
        {
            plc_16BitBuf[addr1+1] = 0;
            if(plc_16BitBuf[addr1]<0x7FFF) plc_16BitBuf[addr1]++;
        }
        if(plc_16BitBuf[addr1]>=temp) PLC_BIT_ON(addr2);
        else PLC_BIT_OFF(addr2);
    }
    else
    {
        PLC_Addr+=6;
    }
}
static void DHOUR(void) //32计时器
{
    uint32_t temp,addr1;
    uint16_t addr2;
    if((PLC_ACC_BIT&0X01)&&PLC_BIT_PLS_TEST(M8013))
    {
        temp = cos_u32_value();
        addr1 = addr_value_prog();
        PLC_Addr+=2;
        addr2 = addr_value();
        PLC_Addr+=2;
//        PLC_RAM32(addr1+4)++;
        PLC_W_RAM_32BIT((addr1+4),PLC_R_RAM_32BIT(addr1+4)+1);
        if(PLC_R_RAM_32BIT(addr1+4)>=3600)
        {
            PLC_W_RAM_32BIT((addr1+4) , 0);
            if(PLC_R_RAM_32BIT(addr1)<0x7FFFFFFF)
//                PLC_RAM32(addr1)++;
                PLC_W_RAM_32BIT((addr1),PLC_R_RAM_32BIT(addr1)+1);
        }
        if(PLC_R_RAM_32BIT(addr1)>=temp) PLC_BIT_ON(addr2);
        else PLC_BIT_OFF(addr2);
    }
    else
    {
        PLC_Addr+=12;
    }
}

//======================================================================================================
// 函数名称: static void ECMP(void)
// 功能描述：ECMP指令函数
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2015年7月26日
// 备  注:
//=======================================================================================================

static void _DECMP(void)	          //浮点比较传送指令
{
    signed short int temp3;
    static float temp1,temp2;
    temp1=float_value();
    temp2=float_value();
    temp3=addr_value();
    Flag_bit=0XFF;
    PLC_BIT_OFF(temp3);
    PLC_BIT_OFF(temp3+1);
    PLC_BIT_OFF(temp3+2);
    if(temp1>temp2)       PLC_BIT_ON(temp3);
    else if(temp1==temp2) PLC_BIT_ON(temp3+1);
    else if(temp1<temp2)  PLC_BIT_ON(temp3+2);
    PLC_Addr+=2;                   //20190331 kevin修复
}

static void DECMP(void)	          //浮点比较传送指令
{
    if((PLC_ACC_BIT&0X01)==0X01)_DECMP();
    else PLC_Addr+=12;              //跳过12步程序
}
static void DECMPP(void)	          //浮点比较传送指令P
{

    if(PLC_LDP_TEST())_DECMP();
    else PLC_Addr+=12;              //跳过12步程序
}

// kevin，2019年11月优化、新增
static void _DEZCP(void)	          //浮点数区间比较
{
    float temp1,temp2,temp3;
    uint32_t temp4;
    temp1=float_value();
    temp2=float_value();
    temp3=float_value();
    temp4=addr_value();
    Flag_bit=0XFF;
    PLC_BIT_OFF(temp4);
    PLC_BIT_OFF(temp4+1);
    PLC_BIT_OFF(temp4+2);
    if(temp1>temp3)       PLC_BIT_ON(temp4+0);
    else if((temp1<=temp3)&&(temp3<=temp2)) PLC_BIT_ON(temp4+1);
    else if(temp2<temp3)  PLC_BIT_ON(temp4+2);
    PLC_Addr+=2;                   //20190331 kevin修复

}

// kevin，2019年11月优化、新增
static void DEZCP(void)	          //浮点数区间比较
{
    if((PLC_ACC_BIT&0X01)==0X01)_DEZCP();
    else PLC_Addr+=16;              //跳过16步程序
}
static void DEZCPP(void)	          //浮点数区间比较P
{
    if(PLC_LDP_TEST())_DEZCP();
    else PLC_Addr+=16;              //跳过16步程序
}

// kevin，2019年11月优化、新增
//======================================================================================================
// 函数名称: static void DCMP(void)
// 功能描述：DCMP指令函数
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2015年7月26日
// 备  注:
//=======================================================================================================
static void _DCMP(void)	          //32位比较传送指令
{
    signed short int temp3;
    static int temp1,temp2;
    temp1=cos_u32_value();
    temp2=cos_u32_value();
    temp3=addr_value();
    Flag_bit=0XFF;
    PLC_BIT_OFF(temp3);
    PLC_BIT_OFF(temp3+1);
    PLC_BIT_OFF(temp3+2);
    if(temp1>temp2)       PLC_BIT_ON(temp3+0);
    else if(temp1==temp2) PLC_BIT_ON(temp3+1);
    else if(temp1<temp2)  PLC_BIT_ON(temp3+2);
    PLC_Addr+=2;
}

static void DCMP(void)	          //32位比较传送指令
{    if(PLC_ACC_BIT&0X01)_DCMP();
    else PLC_Addr+=16;              //跳过12步程序
}

static void DCMPP(void)
{
    if(PLC_LDP_TEST())  _DCMP();      //上升沿判断
    else
        PLC_Addr+=16;		         //条件不满足执行跳过程序，减小CPU开销
}

// kevin，2019年11月优化、新增
//======================================================================================================
// 函数名称: static void CMP(void)
// 功能描述：CMP指令函数
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2015年7月26日
// 备  注:
//=======================================================================================================
static void _CMP(void)	          //16位比较传送指令
{
    signed short int temp1,temp2,temp3;
    temp1=cos_value();
    temp2=cos_value();
    temp3=addr_value();
    Flag_bit=0XFF;
    PLC_BIT_OFF(temp3);
    PLC_BIT_OFF(temp3+1);
    PLC_BIT_OFF(temp3+2);
    if(temp1>temp2)       PLC_BIT_ON(temp3);
    else if(temp1==temp2) PLC_BIT_ON(temp3+1);
    else if(temp1<temp2)  PLC_BIT_ON(temp3+2);
}

static void CMP(void)	          //16位比较传送指令
{
    if((PLC_ACC_BIT&0X01)==0X01)_CMP();
    else PLC_Addr+=6;              //跳过6步程序
}

static void CMPP(void)
{
    if(PLC_LDP_TEST())    _CMP();                   //上升沿判断
    else
        PLC_Addr+=6;		                          //条件不满足执行跳过程序，减小CPU开销
}


static void _RD3A(void)
{
    signed short int temp1,temp2;
    temp1=temp1;
    temp1=cos_value();
    temp2=cos_value();
    temp2 &=0x0007;
    trade=plc_16BitBuf[0x0C1E + temp2];
    target();

}

static void RD3A(void)	          //RD3A
{
    if((PLC_ACC_BIT&0X01)==0X01)_RD3A();
    else PLC_Addr+=6;              //跳过6步程序
}

static void _WR3A(void)	          //
{
    signed short int temp1,temp2,temp3;
    temp1=temp1;
    temp1=cos_value();
    temp2=cos_value();
    temp3=cos_value();

    temp2 &=0x0007;
    plc_16BitBuf[0x0C32 + temp2]=temp3;



}

static void WR3A(void)	          //_WR3A
{
    if((PLC_ACC_BIT&0X01)==0X01)_WR3A();
    else PLC_Addr+=6;              //跳过6步程序
}







// kevin，2019年11月优化、新增
static void _INC(void)	            //逻辑运算 加1指令
{
    trade=cos_value();
    PLC_Addr-=2;
    trade++;
    target();
}

static void INC(void)	            //逻辑运算 加1指令
{
    if((PLC_ACC_BIT&0X01)==0X01)_INC();
    else PLC_Addr+=2;              //跳过2步程序
}

// kevin，2019年11月优化、新增
static void INCP(void)	  //INCP
{
    if(PLC_LDP_TEST())_INC();//上升沿判断
    else
    {
        PLC_Addr+=2;		                            //条件不满足执行跳过程序，减小CPU开销
    }
}

static void _DINC(void)	         //32位逻辑运算 加1指令
{
    trade=(uint32_t)cos_u32_value()+1;
    PLC_Addr-=4;
    D_target();
}

static void DINC(void)	         //32位逻辑运算 加1指令
{
    if((PLC_ACC_BIT&0X01)==0X01)_DINC();
    else PLC_Addr+=4;              //跳过4步程序
}

static void DINCP(void)	  //
{
    if(PLC_LDP_TEST())_DINC();//上升沿判断
    else PLC_Addr+=4;		                           //条件不满足执行跳过程序，减小CPU开销
}

// kevin，2019年11月优化、新增
//
static void _DEC(void)             //逻辑运算 减1指令
{
    trade=cos_value()-1;
    PLC_Addr-=2;
    target();
}


// kevin，2019年11月优化、新增
//
static void DEC(void)             //逻辑运算 减1指令
{
    if(PLC_ACC_BIT&0X01)_DEC();
    else PLC_Addr+=2;              //跳过2步程序
}


// kevin，2019年11月优化、新增
//
static void DECP(void)	          //INCP
{
    if(PLC_LDP_TEST())  _DEC();            //上升沿判断
    else
        PLC_Addr+=2;		                           //条件不满足执行跳过程序，减小CPU开销
}

// kevin，2019年11月优化、新增
//
static void _DDEC(void)                             //32位逻辑运算 减1指令
{
    trade=cos_u32_value()-1;
    PLC_Addr-=4;
    D_target();
}

static void DDEC(void)                             //32位逻辑运算 减1指令
{
    if((PLC_ACC_BIT&0X01)==0X01)_DDEC();
    else PLC_Addr+=4;              //跳过4步程序
}

static void DDECP(void)                             //32位逻辑运算 减1指令
{
    if(PLC_LDP_TEST())_DDEC();
    else PLC_Addr+=4;              //跳过4步程序
}

// kevin，2019年11月优化、新增
//
static void _NEG(void)                             //逻辑运算
{
    trade=0-cos_value();
    PLC_Addr-=2;
    target();
}

static void NEG(void)                             //逻辑运算
{
    if((PLC_ACC_BIT&0X01)==0X01)_NEG();
    else PLC_Addr+=2;              //跳过2步程序
}

static void NEGP(void)                             //逻辑运算
{
    if(PLC_LDP_TEST())_NEG();
    else PLC_Addr+=2;              //跳过2步程序
}

static void _DNEG(void)                             //32位逻辑运算
{
    trade=0-cos_u32_value();
    PLC_Addr-=4;
    D_target();
}

static void DNEG(void)                             //32位逻辑运算
{
    if((PLC_ACC_BIT&0X01)==0X01)_DNEG();
    else PLC_Addr+=4;              //跳过4步程序
}

static void DNEGP(void)                             //32位逻辑运算
{
    if(PLC_LDP_TEST())_DNEG();
    else PLC_Addr+=4;              //跳过4步程序
}

static void _WAND(void)	                          //逻辑运算“与”
{
    signed short int temp1,temp2;
    temp1=cos_value();
    temp2=cos_value();
    trade=temp1&temp2;
    target();
}

// kevin，2019年11月优化、新增
//
static void WAND(void)	                          //逻辑运算“与”
{
    if((PLC_ACC_BIT&0X01)==0X01)_WAND();
    else PLC_Addr+=6;              //跳过6步程序
}

static void WANDP(void)	                          //逻辑运算“与”
{
    if(PLC_LDP_TEST())_WAND();
    else PLC_Addr+=6;              //跳过6步程序
}

static void DWAND(void)	                          //32逻辑运算“与”
{
    uint32_t temp1,temp2;
    if((PLC_ACC_BIT&0X01)==0X01)
    {
        temp1=cos_u32_value();
        temp2=cos_u32_value();;
        trade=temp1&temp2;
        D_target();
    }
    else PLC_Addr+=12;              //跳过12步程序
}

static void DWANDP(void)	                          //32逻辑运算“与”
{
    uint32_t temp1,temp2;
    if(PLC_LDP_TEST())
    {
        temp1=cos_u32_value();
        temp2=cos_u32_value();;
        trade=temp1&temp2;
        D_target();
    }
    else PLC_Addr+=12;              //跳过12步程序
}

// kevin，2019年11月优化、新增
//
static void WOR(void)	                           //逻辑运算“或”
{
    signed short int temp1,temp2;
    if((PLC_ACC_BIT&0X01)==0X01)
    {
        temp1=cos_value();
        temp2=cos_value();
        trade=temp1|temp2;
        target();
    }
    else PLC_Addr+=6;              //跳过6步程序
}

static void WORP(void)	                           //逻辑运算“或”
{
    signed short int temp1,temp2;
    if(PLC_LDP_TEST())
    {
        temp1=cos_value();
        temp2=cos_value();
        trade=temp1|temp2;
        target();
    }
    else PLC_Addr+=6;              //跳过6步程序
}

static void DWOR(void)	                           //32逻辑运算“或”
{
    uint32_t temp1,temp2;
    if((PLC_ACC_BIT&0X01)==0X01)
    {
        temp1=cos_u32_value();
        temp2=cos_u32_value();;
        trade=temp1|temp2;
        D_target();
    }
    else PLC_Addr+=12;              //跳过12步程序
}

static void DWORP(void)	                           //32逻辑运算“或”
{
    uint32_t temp1,temp2;
    if(PLC_LDP_TEST())
    {
        temp1=cos_u32_value();
        temp2=cos_u32_value();;
        trade=temp1|temp2;
        D_target();
    }
    else PLC_Addr+=12;              //跳过12步程序
}

static void WXOR(void)	                          //逻辑运算“异或”
{
    if((PLC_ACC_BIT&0X01)==0X01)
    {
        trade=cos_value()^cos_value();
        target();
    }
    else PLC_Addr+=6;              //跳过6步程序
}

static void WXORP(void)	                          //逻辑运算“异或”
{
    if(PLC_LDP_TEST())
    {
        trade=cos_value()^cos_value();
        target();
    }
    else PLC_Addr+=6;              //跳过6步程序
}

static void DWXOR(void)	                          //逻辑运算“异或”
{
    uint32_t temp1,temp2;
    if((PLC_ACC_BIT&0X01)==0X01)
    {
        temp1=cos_u32_value();
        temp2=cos_u32_value();
        trade=temp1^temp2;
        D_target();
    }
    else PLC_Addr+=12;              //跳过12步程序
}

static void DWXORP(void)	                          //逻辑运算“异或”
{
    uint32_t temp1,temp2;
    if(PLC_LDP_TEST())
    {
        temp1=cos_u32_value();
        temp2=cos_u32_value();
        trade=temp1^temp2;
        D_target();
    }
    else PLC_Addr+=12;              //跳过12步程序
}

void _SUM(void) //求为1的位数总和
{
    uint16_t temp,n;
    temp=cos_value();
    Flag_bit = 0xFF;
    for (n = 0; temp; n++)
        temp &= temp - 1;
    trade = n;
    target();
}
void SUM(void)
{
    if(PLC_ACC_BIT&0X01)_SUM();
    else PLC_Addr+=4;
}
void SUMP(void)
{
    if(PLC_LDP_TEST())_SUM();
    else PLC_Addr+=4;
}

void _DSUM(void) //求为1的位数总和	32
{
    uint32_t temp,n;
    temp=cos_u32_value();
    Flag_bit = 0xFF;
    for (n = 0; temp; n++)
        temp &= temp - 1;
    trade = n;
    D_target();
}
void DSUM(void)
{
    if(PLC_ACC_BIT&0X01)_DSUM();
    else PLC_Addr+=8;
}
void DSUMP(void)
{
    if(PLC_LDP_TEST())_DSUM();
    else PLC_Addr+=8;
}

void _BON(void)  //判断某位是否置位
{
    uint16_t temp,out,n;
    temp=cos_value();
    out = addr_value();
    n=cos_value();
    if((temp>>n)&0x0001) PLC_BIT_ON(out);
    else PLC_BIT_OFF(out);
}

void BON(void)
{
    if(PLC_ACC_BIT&0X01)_BON();
    else PLC_Addr+=6;
}

// kevin，2019年11月优化、新增
//
void BONP(void)
{
    if(PLC_LDP_TEST())_BON();
    else PLC_Addr+=6;
}

void _DBON(void)  //判断某位是否置位32
{
    uint32_t temp,n;
    uint16_t out;
    temp=cos_u32_value();
    out = addr_value();
    Flag_bit=0XFF;
    PLC_Addr+=2;
    n=cos_u32_value();
    if((temp>>n)&0x00000001) PLC_BIT_ON(out);
    else PLC_BIT_OFF(out);

}

void DBON(void)
{
    if(PLC_ACC_BIT&0X01)_DBON();
    else PLC_Addr+=12;
}

void DBONP(void)
{
    if(PLC_LDP_TEST())_DBON();
    else PLC_Addr+=12;
}

void _MEAN(void)
{
    uint16_t temp,temp2;
    uint64_t data;
    uint32_t temp1;
    data = 0;              //20190324 kevin修正data不为0的bug
    temp1=addr_value_prog();
    PLC_Addr+=2;        //些跳过输出
    temp2=cos_value();
    if(Flag_bit==0xff)  //是不是K4M0之类的寄存器
    {
        for(temp=0; temp<temp2; temp++)
        {
            data+=PLC_R_RAM_16BIT(temp1+temp*2);
        }
        PLC_Addr-=4;        //跳回输出
    }
    else
    {
        data=(uint16_t)temp1;
        PLC_Addr-=4;   //回跳
        for(temp=1; temp<temp2; temp++)
        {
            PLC_Addr-=2;
            Transfer=temp*16;
            data+=addr_value_prog();
        }
        Flag_bit=0xff;
    }
    trade=data/temp2;
    target();
    PLC_Addr+=2;
}

// kevin，2019年11月优化、新增
//
void MEAN(void)
{
    if(PLC_ACC_BIT&0X01)_MEAN();
    else PLC_Addr+=6;
}

void MEANP(void)
{
    if(PLC_LDP_TEST())_MEAN();
    else PLC_Addr+=6;
}

void _DMEAN(void) //20190324 kevin增加指令
{
    uint32_t temp,temp2;
    uint64_t data;
    uint32_t temp1;
    data = 0;
    temp1=addr_value_prog(); //取得要求平均数寄存器的首地址
    PLC_Addr+=6;        //跳过中间到最后操作数
    temp2=cos_u32_value(); //取得平均数量
    if(Flag_bit==0xff)  //是不是K4M0之类的寄存器
    {
        for(temp=0; temp<temp2; temp++)
        {
            data+=PLC_R_RAM_32BIT(temp1+temp*4);
        }
        PLC_Addr-=8;        //跳回到中间输出寄存器
    }
    else
    {
        data=temp1;
        PLC_Addr-=8;   //
        for(temp=1; temp<temp2; temp++)
        {
            PLC_Addr-=4;
            Transfer=temp*32;
            data+=addr_value_prog();
        }
        Flag_bit=0xff;
    }
    trade=data/temp2;
    D_target();
    PLC_Addr+=4;  //跳到指令结束位置
}

void DMEAN(void)
{
    if(PLC_ACC_BIT&0X01)_DMEAN();
    else PLC_Addr+=12;
}

void DMEANP(void)
{
    if(PLC_LDP_TEST())_DMEAN();
    else PLC_Addr+=12;
}

static void _SER(void)	  //SER 数据查找    20190408 kevin
{
    uint16_t s1,s2,d,n;
    uint16_t temp,dat;
    s1 = addr_value();
    s2 = cos_value();
    d = addr_value();
    n = cos_value();
    Flag_bit = 0xFF;
    for(temp=0; temp<5; temp++) plc_16BitBuf[d+temp] = 0; //清零输出
    for(temp=0; temp<n; temp++) //求相等数 数量 与 位置
    {
        if(plc_16BitBuf[s1+temp]==s2)
        {
            plc_16BitBuf[d]++;//相等数数量
            if(PLC_PL_BIT_TEST(PLC_Addr-PLC_LAD_START_ADDR)==off)
            {
                plc_16BitBuf[d+1] = temp;//相等数位置第一个
                PLC_PL_BIT_ON(PLC_Addr-PLC_LAD_START_ADDR);
            }
            plc_16BitBuf[d+2] = temp;//相等数位置最后一个
        }
    }
    PLC_PL_BIT_OFF(PLC_Addr-PLC_LAD_START_ADDR);
    dat = plc_16BitBuf[s1];
    for(temp=0; temp<n; temp++) //求最后一个最小值位置
    {
        if(dat>=plc_16BitBuf[s1+temp])
        {
            dat = plc_16BitBuf[s1+temp];//
            plc_16BitBuf[d+3] = temp;//最小值位置最后一个
        }
    }
    dat = plc_16BitBuf[s1];
    for(temp=0; temp<n; temp++) //求最后一个最大值位置
    {
        if(dat<=plc_16BitBuf[s1+temp])
        {
            dat = plc_16BitBuf[s1+temp];//
            plc_16BitBuf[d+4] = temp;//最大值位置最后一个
        }
    }
}
void SER(void)
{
    if(PLC_ACC_BIT&0X01)_SER();
    else PLC_Addr+=8;
}
void SERP(void)
{
    if(PLC_LDP_TEST())_SER();
    else PLC_Addr+=8;
}

static void _DSER(void)	  //DSER 数据查找      20190408 kevin
{
    uint32_t s1,s2,d,n;
    uint32_t temp,dat;
    s1 = addr_value_prog();
    PLC_Addr+=2;
    s2 = cos_u32_value();
    d = addr_value_prog();
    PLC_Addr+=2;
    n = cos_u32_value();
    for(temp=0; temp<5; temp++)
        PLC_W_RAM_32BIT((d+temp*4) , 0); //清零输出
    for(temp=0; temp<n; temp++) //求相等数 数量 与 位置
    {
        if(PLC_R_RAM_32BIT(s1+temp*4)==s2)
        {
//            PLC_RAM32(d)++;//相等数数量
            PLC_W_RAM_32BIT((d),PLC_R_RAM_32BIT(d)+1);
            if(PLC_PL_BIT_TEST(PLC_Addr-PLC_LAD_START_ADDR)==off)
            {
                PLC_W_RAM_32BIT((d+4) , temp);//相等数位置第一个
                PLC_PL_BIT_ON(PLC_Addr-PLC_LAD_START_ADDR);
            }
            PLC_W_RAM_32BIT((d+8) , temp);//相等数位置最后一个
        }
    }
    PLC_PL_BIT_OFF(PLC_Addr-PLC_LAD_START_ADDR);
    dat = PLC_R_RAM_32BIT(s1);
    for(temp=0; temp<n; temp++) //求最后一个最小值位置
    {
        if(dat>=PLC_R_RAM_32BIT(s1+temp*4))
        {
            dat = PLC_R_RAM_32BIT(s1+temp*4);//
            PLC_W_RAM_32BIT((d+12) , temp);//最小值位置最后一个
        }
    }
    dat = PLC_R_RAM_32BIT(s1);
    for(temp=0; temp<n; temp++) //求最后一个最大值位置
    {
        if(dat<=PLC_R_RAM_32BIT(s1+temp*4))
        {
            dat = PLC_R_RAM_32BIT(s1+temp*4);//
            PLC_W_RAM_32BIT((d+16) , temp);//最大值位置最后一个
        }
    }
}
void DSER(void)
{
    if(PLC_ACC_BIT&0X01)_DSER();
    else PLC_Addr+=16;
}
void DSERP(void)
{
    if(PLC_LDP_TEST())_DSER();
    else PLC_Addr+=16;
}
static void ABSD(void)	  //ABSD 凸轮控制器 绝对方式 20190407kevin C199-- plc_16BitBuf[HELON_REG_C_START_ADDR+C_number]
{
    uint16_t s1,s2,d1,n,temp;
    if(PLC_ACC_BIT&0X01)
    {
        s1 = addr_value();
        s2 = (uint8_t)addr_value(); //取得C编号
        d1 = addr_value();
        n  = cos_value();
        Flag_bit = 0xFF;
        if(s2<200&&n>0) //c0-c199  16位计数器
        {
            for(temp=0; temp<n; temp++)
            {
                if(plc_16BitBuf[s1+temp*2]<plc_16BitBuf[s1+temp*2+1])//前值小于等于后值 区间内
                {
                    if(plc_16BitBuf[HELON_REG_C_START_ADDR + s2] >= plc_16BitBuf[s1 + temp * 2] && plc_16BitBuf[HELON_REG_C_START_ADDR + s2] < plc_16BitBuf[s1 + temp * 2 + 1])
                    {
                        PLC_BIT_ON(d1+temp);
                    }
                    else PLC_BIT_OFF(d1+temp);
                }
                else if (plc_16BitBuf[s1+temp*2]>plc_16BitBuf[s1+temp*2+1]) //前值大于后值
                {
                    if(plc_16BitBuf[HELON_REG_C_START_ADDR + s2] < plc_16BitBuf[s1 + temp * 2] && plc_16BitBuf[HELON_REG_C_START_ADDR + s2] >= plc_16BitBuf[s1 + temp * 2 + 1])
                    {
                        PLC_BIT_OFF(d1+temp);
                    }
                    else PLC_BIT_ON(d1+temp);
                }
                else  //两值相等
                {
                    if(plc_16BitBuf[HELON_REG_C_START_ADDR + s2] == plc_16BitBuf[s1 + temp * 2])
                    {
                        PLC_BIT_ON(d1+temp);
                    }
                    else PLC_BIT_OFF(d1+temp);
                }
            }
        }
    }
    else PLC_Addr+=8;
}

// kevin，2019年11月优化、新增
//
static void DABSD(void)	  //DABSD 凸轮控制器 绝对方式   20190407kevin    RAM_C200_ADDR + (C_number - 200) *4
{
    uint32_t s1;
    uint16_t s2,d1,n,temp;
    if(PLC_ACC_BIT&0X01)
    {
        s1 = addr_value_prog();
        PLC_Addr+=2;
        s2 = addr_value(); //取得C编号
        PLC_Addr+=2;
        d1 = addr_value();
        PLC_Addr+=2;
        n  = cos_u32_value();
        Flag_bit = 0xFF;
        if(s2 >= HELON_REG_C200_START_ADDR && n > 0) //C200  32位计数器 kevin modify 此处应该更改地址
        {
            s2 -= HELON_REG_C200_START_ADDR;  //计算C200以上地址 kevin modify 此处应该更改地址
            s2 /= 2;
            for(temp=0; temp<n; temp++)
            {
                if(PLC_R_RAM_32BIT(s1+temp*8)< PLC_R_RAM_32BIT(s1+temp*8+4))      //前值小于等于后值 区间内
                {
                    if( PLC_R_RAM_32BIT(RAM_C200_ADDR +s2 *4)>= PLC_R_RAM_32BIT(s1+temp*8) && PLC_R_RAM_32BIT(RAM_C200_ADDR +s2*4)<PLC_R_RAM_32BIT(s1+temp*8+4))
                    {
                        PLC_BIT_ON(d1+temp);
                    }
                    else PLC_BIT_OFF(d1+temp);
                }
                else if (PLC_R_RAM_32BIT(s1+temp*8)>PLC_R_RAM_32BIT(s1+temp*8+4)) //前值大于后值
                {
                    if( PLC_R_RAM_32BIT(RAM_C200_ADDR +s2*4)<PLC_R_RAM_32BIT(s1+temp*8) && PLC_R_RAM_32BIT(RAM_C200_ADDR +s2*4)>=PLC_R_RAM_32BIT(s1+temp*8+4))
                    {
                        PLC_BIT_OFF(d1+temp);
                    }
                    else PLC_BIT_ON(d1+temp);
                }
                else  //两值相等
                {
                    if( PLC_R_RAM_32BIT(RAM_C200_ADDR +s2*4)== PLC_R_RAM_32BIT(s1+temp*8))
                    {
                        PLC_BIT_ON(d1+temp);
                    }
                    else PLC_BIT_OFF(d1+temp);
                }
            }
        }
    }
    else PLC_Addr+=16;
}
static void INCD(void)	  //INCD 凸轮控制器 增量方式  20190408 kevin
{
    uint16_t s1,s2,d1,n,temp;
    s1 = addr_value();
    s2 = (uint8_t)addr_value(); //取得C编号
    d1 = addr_value();
    n  = cos_value();
    Flag_bit = 0xFF;
    if(PLC_ACC_BIT&0X01)
    {
        if(s2<199&&n>0) //c0-c199  16位计数器
        {
            if(PLC_PL_BIT_TEST(PLC_Addr-PLC_LAD_START_ADDR)==off) //启动时清零计数器
            {
                PLC_PL_BIT_ON(PLC_Addr-PLC_LAD_START_ADDR);
                plc_16BitBuf[HELON_REG_C_START_ADDR + s2] =0;
                plc_16BitBuf[HELON_REG_C_START_ADDR + s2 + 1] =0;
            }
            temp =  plc_16BitBuf[HELON_REG_C_START_ADDR + s2 + 1];  //取得C+1计数值
            if(plc_16BitBuf[HELON_REG_C_START_ADDR + s2] >= plc_16BitBuf[s1 + temp]) //当前 计数值到
            {
                plc_16BitBuf[HELON_REG_C_START_ADDR + s2] = 0;//C重新计数
                if(temp<n)
                {
                    plc_16BitBuf[HELON_REG_C_START_ADDR + s2 + 1]++;//C+1 +1
                }
                else //循环结束
                {
                    plc_16BitBuf[HELON_REG_C_START_ADDR + s2 + 1] = 0;
                    PLC_BIT_ON(M8029);
                }
                PLC_BIT_OFF(d1+temp);
            }
            else if(plc_16BitBuf[HELON_REG_C_START_ADDR + s2 + 1] == 0 ) PLC_BIT_OFF(M8029);
            if(plc_16BitBuf[HELON_REG_C_START_ADDR + s2 + 1] < n)	PLC_BIT_ON(d1 + plc_16BitBuf[HELON_REG_C_START_ADDR + s2 + 1]);
        }
    }
    else
    {
        if(PLC_PL_BIT_TEST(PLC_Addr-PLC_LAD_START_ADDR)==on) //清零计数
        {
            PLC_PL_BIT_OFF(PLC_Addr-PLC_LAD_START_ADDR);
            plc_16BitBuf[HELON_REG_C_START_ADDR + s2] = 0;
            plc_16BitBuf[HELON_REG_C_START_ADDR + s2 + 1] = 0;
            for(temp=0; temp<n; temp++) PLC_BIT_OFF(d1+temp);
        }
    }
}
static void TTMR(void)//示教定时器 20190404 kevin
{
    uint16_t temp,temp1;
    if(PLC_ACC_BIT&0X01)
    {
        if(PLC_BIT_PLS_TEST(M8013))
        {
            temp=addr_value();
            temp1=cos_value();
            plc_16BitBuf[temp+1]++;
        }
        else
        {
            PLC_Addr+=4;
        }
    }
    else
    {
        temp=addr_value();
        temp1=cos_value();
        if(plc_16BitBuf[temp+1]>0)
        {
            if(temp1>2) temp1 = 2;
            plc_16BitBuf[temp] = plc_16BitBuf[temp+1]*(uint16_t)(pow10(temp1));
            plc_16BitBuf[temp+1] = 0;
        }
    }
}


// kevin，2019年11月优化、新增
//
//=======================================================================================================
// 函数名称:  static void ALT(void)
// 功能描述： 交替输出 ALT指令
// 作　者:  kevin
// 日　期:  2015年8月4日
// 备  注:
//=======================================================================================================
static void ALT(void)
{
    uint16_t temp;
    if(PLC_ACC_BIT&0X01)
    {
        temp=addr_value();
        if(PLC_BIT_TEST(temp))
            PLC_BIT_OFF(temp);
        else
            PLC_BIT_ON(temp);
    }
    else PLC_Addr+=2;                      //没有动作跳过2步程序
}

static void ALTP(void)
{
    uint16_t temp;
    if(PLC_LDP_TEST())
    {
        temp=addr_value();
        if(PLC_BIT_TEST(temp))
            PLC_BIT_OFF(temp);
        else
            PLC_BIT_ON(temp);
    }
    else PLC_Addr+=2;                      //没有动作跳过2步程序
}

//=======================================================================================================
// 函数名称:  static void RAMP(void)
// 功能描述： 斜坡指令 RAMP
// 作　者:  kevin
// 日　期:  2019年4月4日
// 备  注:
//=======================================================================================================
static uint8_t RAMP_STA=0;
static void RAMP(void)
{
    uint16_t addr1,addr2,addr3,temp;
    if(PLC_ACC_BIT&0X01)
    {
        addr1 = addr_value();  //S1
        addr2 = addr_value();  //S2
        addr3 = addr_value();  //D
        temp  = cos_value();
        //if(PLC_PL_BIT_TEST(PLC_Addr-PLC_LAD_START_ADDR)==off)
        if(	RAMP_STA ==0)
        {
            //PLC_PL_BIT_ON(PLC_Addr-PLC_LAD_START_ADDR);
            RAMP_STA =1;
            plc_16BitBuf[addr3] = plc_16BitBuf[addr1];//接通时初值
        }
        if(plc_16BitBuf[addr3+1]<temp)
        {
            plc_16BitBuf[addr3+1]++;
            if(plc_16BitBuf[addr1]>plc_16BitBuf[addr2]) //从大到小
            {
                if(plc_16BitBuf[addr3]>plc_16BitBuf[addr2])  plc_16BitBuf[addr3] -= (plc_16BitBuf[addr3]-plc_16BitBuf[addr2])/(temp-plc_16BitBuf[addr3+1]);
            }
            else if (plc_16BitBuf[addr1]<plc_16BitBuf[addr2]) //从小到大
            {
                if(plc_16BitBuf[addr3]<plc_16BitBuf[addr2])  plc_16BitBuf[addr3] += (plc_16BitBuf[addr2]-plc_16BitBuf[addr3])/(temp-plc_16BitBuf[addr3+1]);
            }
            //else
        }
        else //周期到
        {
            plc_16BitBuf[addr3] = plc_16BitBuf[addr2];
            if(PLC_BIT_TEST(M8026)) //M8026为1 不循环
            {
                PLC_BIT_ON(M8029);
            }
            else//M8026为0时 循环工作 M8029接通一个扫描周期
            {
                if(PLC_BIT_TEST(M8029)==off)
                {
                    PLC_BIT_ON(M8029);
                }
                else
                {
                    PLC_BIT_OFF(M8029);
                    plc_16BitBuf[addr3] = plc_16BitBuf[addr1];//接通时初值
                    plc_16BitBuf[addr3+1] = 0;
                }
            }
        }
    }
    else
    {
        PLC_Addr+=8;                      //没有动作跳过8步程序
//        if(RAMP_STA ==1)
//        {
//            RAMP_STA =0;
//            PLC_BIT_OFF(M8029); //8029 off
//        }
    }
}


//=======================================================================================================
// 函数名称:  RS
// 功能描述： RS  S. m D. n ；S和m表示发送数据地址和点数； D和n表示接收数据地址和点数
// 输　入:
// 输　出:
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2016年11月28日，新增
// 备  注:  把RS改造成类似ADPRW指令的Modbus功能,实现Modbus主机模式

// ADPRW S. S1. S2. S3. S4./D. ； S. 子站地址，S1. 功能码，S2. S3. 发送数据地址和点数 ,S4/D 接收数据地址或者数据

// 1、主从模式切换         D8120
// 2、子站号              D8121

// 3、指令执行结束        M8122  ->M8180

// 4、重试次数            D8126  ->D8132
// 5、发生重试            M8153  ->M8128

// 6、MODBUS通信发生出错  D8152 M8152  ->D8125	 M8125

// 7、发生超时            M8129  ->M8129

//=======================================================================================================

static void RS(void)
{
    // disable by kevin
//    if(PLC_ACC_BIT&0X01)
//    {
//        uint8_t ch =0;
//
//        if( HS_MOD1_MODE ==1) // 第1路modbus主机模式
//            ch =0;
//        else if(HS_MOD2_MODE ==1) // 第2路modbus主机模式
//            ch =1;
//
//        // 超时处理，重试次数处理，
//        if(gModH[ch].txStatus == 1) // 如果有发送操作，需要判断子站发送是否超时
//        {
//            if( ((gModH[ch].fAck & 0x01) !=0x01) && ((gModH[ch].fAck & 0x02) !=0x02)) // 无应答并且应答无超时，则直接跳出
//                return;
//
//            gModH[ch].txStatus =0;
//
//            if((gModH[ch].fAck & 0x0E)) // 应答出错（长度、CRC、超时）
//            {
//                gModH[ch].fAck &= ~0x0E;
//
//                gModH[ch].retryCnt ++; // 重试次数累加
//
//                /* 重试处理 */
//                D8132 =(D8132 > 20)? 20 : D8132; // 最大重试次数限制
//
//                if(gModH[ch].retryCnt <= D8132)
//                {
//                    PLC_BIT_ON(M8128); // 标记发生重试,供梯形图上查看
//                    goto jumpLabel;
//                }
//                else  // 重试次数超设定值，则放弃次操作
//                {
//                    gModH[ch].retryCnt =0; // 累计重试次数清零
//                    PLC_BIT_ON(M8180);       // 标记执行结束
//                    PLC_BIT_ON(M8125);       // 通信出错
//                    D8125 = 8125;            // 通讯出错代号
//                    PLC_BIT_ON(M8129);       // 标记发生超时
//                    PLC_BIT_OFF(M8128);      // 发生重试清零
//                    return;
//                }
//            }
//        }
//
//        // 第一次执行或者正常执行结束
//        gModH[ch].retryCnt =0; // 累计重试次数清零
//        PLC_BIT_OFF(M8125);      // 通讯出错清零
//        D8125 =0;                // 通讯出错代码清零
//        PLC_BIT_OFF(M8129);      // 发生超时清零
//        PLC_BIT_OFF(M8128);      // 发生重试清零
//
//        /* kevin，20170212修改 */
//        if( (gModH[ch].fAck & 0x01) ==0x01) // 正常执行结束
//        {
//            gModH[ch].fAck &=~0x01;
//            PLC_BIT_ON(M8180);                  // 标记执行结束
//            return;
//        }
//        PLC_BIT_OFF(M8180);                   // 执行结束清零
//
//        /*
//        	子站功能需要改一下，不能用HS_MOD_SAVLE_ADDR，用这个，PLC程序里面读多从机时，从机号不能变。
//        	存放数据的位置需要再测试
//
//        */
//
//
//        gModH[ch].slaveAddr = HS_MOD_SAVLE_ADDR;  // 子站号
//        gModH[ch].funCode=cos_value();            // 功能码
//        gModH[ch].addr=cos_value();               // 对象的地址
//        gModH[ch].num=cos_value();                // 操作的数量
//        //place=addr_value();
//        gModH[ch].place=cos_value();              // 存放数据位置或者发送数据位置
//
//        jumpLabel:	// 重试
//        ModHostMode_SendDeal(ch,gModH[ch].slaveAddr,gModH[ch].funCode,
//                             gModH[ch].addr, gModH[ch].num,gModH[ch].place);
//
//    }
//    else
//    {
//        PLC_Addr+=8;                      //没有动作跳过8步程序
//    }
}





/* FX3U MODBUS主站指令 */
static void ADPRW(void)
{
    //disable by kevin
//    uint8_t ch =0;
//    if( md_config[0][1] ==1) // 第1路modbus主机模式
//    {
//        ch =0;
//    }
//    else if(md_config[1][1] ==1) // 第2路modbus主机模式
//    {
//        ch =1;
//    }
//
//    gModH[ch].cmdNumStatistics ++; // 统计梯形图中ADPRWD个数
//
//    if(gModH[ch].cmdSum <= (++gModH[ch].cmdNextNo))
//        gModH[ch].cmdNextNo =0;
//
//    if(PLC_ACC_BIT&0X01)
//    {
//
//        // 超时处理，重试次数处理，
//        if(gModH[ch].txStatus == 1) // 如果有发送操作，需要判断子站发送是否超时
//        {
////			 if( ((gModH[ch].fAck & 0x01) !=0x01) && ((gModH[ch].fAck & 0x02) !=0x02)) // 无应答并且应答无超时，则直接跳出
//            if(gModH[ch].fAck==0) // 无应答并且应答无超时，则直接跳出
//            {
//                PLC_Addr+=10;                 //没有动作跳过8步程序
//                return;
//            }
//
//            gModH[ch].txStatus =0;
//
//            if((gModH[ch].fAck & 0x0E)) // 应答出错（长度、CRC、超时）
//            {
//                gModH[ch].fAck &= ~0x0E;
//
//                gModH[ch].retryCnt ++;   // 重试次数累加
//
//                /* 重试处理 */
//                SET_RETRY_CNT =(SET_RETRY_CNT > RETRY_CNT_DEF)? RETRY_CNT_DEF : SET_RETRY_CNT; // 最大重试次数限制
//                if(gModH[ch].retryCnt <= SET_RETRY_CNT)
//                {
//                    PLC_BIT_ON(M8128); // 标记发生重试,供梯形图上查看
//                    PLC_Addr+=10; //没有动作跳过10步程序
//                    goto jumpLabel;
//                }
//                else  // 重试次数超设定值，则放弃次操作
//                {   // 如果要加快循环节奏，可注释
//                    gModH[ch].retryCnt =0; 		// 累计重试次数清零
//                    PLC_BIT_ON(M8122);       	// 标记执行结束
//                    PLC_BIT_ON(M8063_ERR_FLG);  // 通信出错
//                    D8063_ERR_CODE = 8063;   	// 通讯出错代号
//                    PLC_BIT_ON(TIMEOUT_FLG);    // 标记发生超时
//                    PLC_BIT_OFF(M8128);      	// 发生重试清零
//
//                    PLC_Addr+=10; //没有动作跳过10步程序
//                    return;
//                }
//            }
//        } /* if(gModH[ch].txStatus == 1) */
//
//        // 第一次执行或者正常执行结束
//        gModH[ch].retryCnt =0; // 累计重试次数清零
//        PLC_BIT_OFF(M8063_ERR_FLG); // 通讯出错清零
//        D8063_ERR_CODE =0;          // 通讯出错代码清零
//        PLC_BIT_OFF(TIMEOUT_FLG);   // 发生超时清零
//        PLC_BIT_OFF(M8128);         // 发生重试清零
//
//        SET_TIMEOUT =((MOD_TIMEOUT_MAX < SET_TIMEOUT) || (SET_TIMEOUT ==0))? MOD_TIMEOUT_DEF : SET_TIMEOUT; /* 超时时间设定处理 */
//
//        /* kevin，20170212修改 */
//        if( (gModH[ch].fAck & 0x01) ==0x01) // 正常执行结束
//        {
//            gModH[ch].fAck &=~0x01;
//
//            // 如果要加快循环节奏，可注释
//            PLC_BIT_ON(M8122); // 标记执行结束
//            PLC_Addr+=10; //没有动作跳过8步程序
//            return;
//        }
//        PLC_BIT_OFF(M8122);                   // 执行结束清零
//
//        if(gModH[ch].cmdCurrNo == gModH[ch].cmdNextNo)
//        {
//            if(gModH[ch].cmdSum <= (++gModH[ch].cmdCurrNo))
//                gModH[ch].cmdCurrNo =0;
//
//            /*
//            S  子站号
//            S1 功能码
//            S2 Modbus地址
//            S3 访问点数
//            S4 数据存储软元件位置
//            */
//            gModH[ch].slaveAddr =cos_value();      // 子站号
//            gModH[ch].funCode =cos_value();        // 功能码
//            gModH[ch].addr =cos_value();           // 对象的地址
//            gModH[ch].num =cos_value();            // 操作的数量
////				gModH[ch].place =cos_value();        // 存放数据位置或者发送数据位置
//            gModH[ch].place =addr_value();         // 存放数据位置或者发送数据位置
//        }
//        else
//        {
//            PLC_Addr+=10; //没有动作跳过8步程序
//            return;
//        }
//
//        jumpLabel:	// 重试
//
//
//        ModHostMode_SendDeal(ch, gModH[ch].slaveAddr, gModH[ch].funCode,
//                             gModH[ch].addr, gModH[ch].num, gModH[ch].place);
//    }
//    else
//    {
//
////jumpLabel0:
//        PLC_Addr+=10;                      //没有动作跳过8步程序
//        /* 跳开不执行的 */
//        if(gModH[ch].cmdSum <= (++gModH[ch].cmdCurrNo))
//            gModH[ch].cmdCurrNo =0;
//    }
}



// kevin，2019年11月优化、新增
//
static void _DBCD(void)	            //二进制转换DBCD
{
    signed int can1,add1,add2,add3,add4,buffer1,buffer2,buffer3,buffer4;
    PLC_Err=PLC_Addr;
    can1=cos_u32_value();
    add1=can1%10;
    add2=can1/10;
    add2=add2%10;
    add3=can1/100;
    add3=add3%10;
    add4=can1/1000;
    add4=add4%10;

    buffer1=can1/10000;
    buffer1=buffer1%10;

    buffer2=can1/100000;
    buffer2=buffer2%10;

    buffer3=can1/1000000;
    buffer3=buffer3%10;

    buffer4=can1/10000000;
    buffer4=buffer4%10;

    trade=buffer4*16*256*65536+buffer3*256*65536+buffer2*16*65536+buffer1*65536+add4*16*256+add3*256+add2*16+add1;
    D_target();
}

static void DBCD(void)	            //二进制转换DBCD
{
    if(PLC_ACC_BIT&0X01)_DBCD();
    else PLC_Addr+=8;              //跳过4步程序
}

static void DBCDP(void)	            //二进制转换DBCD
{
    if(PLC_LDP_TEST())_DBCD();
    else PLC_Addr+=8;              //跳过4步程序
}

static void _BCD(void)	            //二进制转换BCD
{
    signed short Ia, Ic;
    PLC_Err=PLC_Addr;
    Ic = cos_value();
    Ia   = (Ic / 1000) << 12;
    Ic  %= 1000;
    Ia  |= (Ic / 100 ) << 8;
    Ic  %= 100;
    Ia  |= (Ic / 10 ) << 4;
    Ic  %= 10;
    Ia  |=  Ic;
    trade=Ia;
    target();
}

static void BCD(void)	            //二进制转换BCD
{
    if((PLC_ACC_BIT&0X01)==0X01)_BCD();
    else PLC_Addr+=4;              //跳过4步程序
}

static void BCDP(void)	            //二进制转换BCD
{
    if(PLC_LDP_TEST())_BCD();
    else PLC_Addr+=4;              //跳过4步程序
}

// kevin，2019年11月优化、新增
//
static void _DBIN(void)	         //二进制转换DBIN
{
    signed int can1,add1,add2,add3,add4,buffer1,buffer2,buffer3,buffer4;
    PLC_Err=PLC_Addr;
    can1=cos_u32_value();
    add1=can1%16;
    add2=can1/16;
    add2=add2%16;
    add3=can1/256;
    add3=add3%16;
    add4=can1/(16*256);
    add4=add4%16;

    can1=can1/65536;
    buffer1=can1%16;
    buffer2=can1/16;
    buffer2=buffer2%16;
    buffer3=can1/256;
    buffer3=buffer3%16;
    buffer4=can1/(16*256);
    buffer4=buffer4%16;

    trade=buffer4*10000000+buffer3*1000000+buffer2*100000+buffer1*10000+add4*1000+add3*100+add2*10+add1;

    D_target();
}

static void DBIN(void)	         //二进制转换DBIN
{
    if(PLC_ACC_BIT&0X01)_DBIN();
    else PLC_Addr+=8;              //跳过4步程序
}

static void DBINP(void)	         //二进制转换DBIN
{
    if(PLC_LDP_TEST())_DBIN();
    else PLC_Addr+=8;              //跳过4步程序
}

static void _BIN(void)	            //二进制转换BIN
{
    signed short Ia, Ic;

    PLC_Err=PLC_Addr;
    Ic = cos_value();
    Ia   = ((Ic >> 12) & 0x0f) * 1000;
    Ia  += ((Ic >> 8 ) & 0x0f) * 100;
    Ia  += ((Ic >> 4 ) & 0x0f) * 10;
    Ia  +=   Ic        & 0x0f;
    trade=Ia;
    target();
}

static void BIN(void)	            //二进制转换BIN
{
    if((PLC_ACC_BIT&0X01)==0X01)_BIN();
    else PLC_Addr+=4;              //跳过4步程序
}

static void BINP(void)	            //二进制转换BIN
{
    if(PLC_LDP_TEST())_BIN();
    else PLC_Addr+=4;              //跳过4步程序
}

//=======================================================================================================
// 函数名称:  static void ADD(void)
// 功能描述： 16位交换传送 ADD指令  加法
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2015年8月4日
// 备  注:
//=======================================================================================================
static void ADD(void)	   //16位加
{
    int16_t temp1,temp2;

    if(PLC_ACC_BIT&0X01)
    {
        temp1=cos_value();
        temp2 =cos_value();
        trade=temp1+temp2;
        //trade=cos_value()+cos_value();
        target();
    }
    else PLC_Addr+=6;              //跳过6步程序
}
static void ADDP(void)	   //16位加上升沿
{
    int16_t temp1,temp2;
    if(PLC_LDP_TEST())
    {
        temp1=cos_value();
        temp2 =cos_value();
        trade=temp1+temp2;
        // trade=cos_value()+cos_value();
        target();
    }
    else PLC_Addr+=6;              //跳过6步程序
}
static void DADD(void)   //32位加
{
    if((PLC_ACC_BIT&0X01)==0X01)
    {
        PLC_Err=PLC_Addr;
        trade=cos_u32_value()+cos_u32_value();
        D_target();
    }
    else PLC_Addr+=12;              //跳过12步程序
}
static void DADDP(void)   //32位加上升沿
{
    if(PLC_LDP_TEST())
    {
        PLC_Err=PLC_Addr;
        trade=cos_u32_value()+cos_u32_value();
        D_target();
    }
    else PLC_Addr+=12;              //跳过12步程序
}



static void SUB(void)	   //16位减法
{
    if(PLC_ACC_BIT&0X01)
    {
        PLC_Err=PLC_Addr;
        trade=cos_value()-cos_value();
        target();
    }
    else PLC_Addr+=6;              //跳过6步程序
}
static void SUBP(void)	   //16位减法
{
    if(PLC_LDP_TEST())
    {
        PLC_Err=PLC_Addr;
        trade=cos_value()-cos_value();
        target();
    }
    else PLC_Addr+=6;              //跳过6步程序
}
static void DSUB(void)  //32位减
{
    if((PLC_ACC_BIT&0X01)==0X01)
    {
        PLC_Err=PLC_Addr;
        trade=cos_u32_value()-cos_u32_value();
        D_target();
    }
    else PLC_Addr+=12;              //跳过12步程序
}
static void DSUBP(void)  //32位减
{
    if(PLC_LDP_TEST())
    {
        PLC_Err=PLC_Addr;
        trade=cos_u32_value()-cos_u32_value();
        D_target();
    }
    else PLC_Addr+=12;              //跳过12步程序
}

static void MUL(void)	 //乘法
{
    signed int temp1,temp2;
    uint32_t temp3;
    if((PLC_ACC_BIT&0X01)==0X01)
    {
        PLC_Err=PLC_Addr;
        temp1=cos_value();
        temp2=cos_value();
        temp3=addr_value_prog();
        PLC_W_RAM_32BIT((temp3),temp1*temp2);
    }
    else PLC_Addr+=6;              //跳过6步程序
}
// kevin，2019年11月优化、新增
//
static void MULP(void)	 //乘法
{
    signed int temp1,temp2;
    uint32_t temp3;
    if(PLC_LDP_TEST())
    {
        PLC_Err=PLC_Addr;
        temp1=cos_value();
        temp2=cos_value();
        temp3=addr_value_prog();
        PLC_W_RAM_32BIT((temp3),temp1*temp2);
    }
    else PLC_Addr+=6;              //跳过6步程序
}

/* kevin，20170529修复 */
static void DMUL(void)
{
    if(PLC_ACC_BIT&0X01)
    {
        uint16_t index;
        u64data.data=(int64_t)(cos_u32_value()*cos_u32_value()); // 些存放64位共同体
        index=addr_value();  // 存放的位置
        PLC_Addr+=2;
        plc_16BitBuf[index] = u64data.data1[0]; // 输出
        plc_16BitBuf[index + 1] = u64data.data1[1];
        plc_16BitBuf[index + 2] = u64data.data1[2];
        plc_16BitBuf[index + 3] = u64data.data1[3];
    }
    else
    {
        PLC_Addr+=12; // 跳过12步程序
    }
}

static void DMULP(void)
{
    if(PLC_LDP_TEST())
    {
        uint16_t index;
        u64data.data=(int64_t)(cos_u32_value()*cos_u32_value()); // 些存放64位共同体
        index=addr_value();  // 存放的位置
        PLC_Addr+=2;
        plc_16BitBuf[index] = u64data.data1[0]; // 输出
        plc_16BitBuf[index + 1] = u64data.data1[1];
        plc_16BitBuf[index + 2] = u64data.data1[2];
        plc_16BitBuf[index + 3] = u64data.data1[3];
    }
    else
    {
        PLC_Addr+=12; // 跳过12步程序
    }
}

static void DIV(void)	 //除法
{
    signed short int temp1,temp2,temp3;
    if((PLC_ACC_BIT&0X01)==0X01)
    {
        PLC_Err=PLC_Addr;
        temp1=cos_value();
        temp2=cos_value();
        temp3=addr_value();
        plc_16BitBuf[temp3]=temp1/temp2;
        plc_16BitBuf[temp3+1]=temp1%temp2;
    }
    else PLC_Addr+=6;              //跳过6步程序
}

static void DIVP(void)	 //除法
{
    signed short int temp1,temp2,temp3;
    if(PLC_LDP_TEST())
    {
        PLC_Err=PLC_Addr;
        temp1=cos_value();
        temp2=cos_value();
        temp3=addr_value();
        plc_16BitBuf[temp3]=temp1/temp2;
        plc_16BitBuf[temp3+1]=temp1%temp2;
    }
    else PLC_Addr+=6;              //跳过6步程序
}

/* kevin，20170529修复 */
static void DDIV(void)
{
    if(PLC_ACC_BIT&0X01)
    {
        int32_t divisor,dividend;
        uint16_t index;

        divisor = cos_u32_value();
        dividend = cos_u32_value();

        u32data.data =divisor / dividend; // 商
        u32data1.data =divisor % dividend; // 余数

        index =addr_value(); // 存放的位置
        PLC_Addr+=2;
        plc_16BitBuf[index] = u32data.data1[0]; // 输出
        plc_16BitBuf[index + 1]=u32data.data1[1];
        plc_16BitBuf[index + 2]=u32data1.data1[0];
        plc_16BitBuf[index + 3]=u32data1.data1[1];
    }
    else
    {
        PLC_Addr+=12;              //跳过12步程序
    }
}

static void DDIVP(void)
{
    if(PLC_LDP_TEST())
    {
        int32_t divisor,dividend;
        uint16_t index;

        divisor = cos_u32_value();
        dividend = cos_u32_value();

        u32data.data =divisor / dividend; // 商
        u32data1.data =divisor % dividend; // 余数

        index =addr_value(); // 存放的位置
        PLC_Addr+=2;
        plc_16BitBuf[index] = u32data.data1[0]; // 输出
        plc_16BitBuf[index + 1]=u32data.data1[1];
        plc_16BitBuf[index + 2]=u32data1.data1[0];
        plc_16BitBuf[index + 3]=u32data1.data1[1];
    }
    else
    {
        PLC_Addr+=12;              //跳过12步程序
    }
}

// kevin，2019年11月优化、新增
//
//======================================================================================================
// 函数名称:  static void DESQR(void)
// 功能描述： 浮点二进制换整数
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2014年6月27日
// 备  注:
//=======================================================================================================
static void DINT(void)
{
    if(PLC_ACC_BIT&0X01)
    {
        trade=(uint32_t)float_value();
        D_target();
    }
    else PLC_Addr+=8;              //跳过8步程序
}

static void DINTP(void)
{
    if(PLC_LDP_TEST())
    {
        trade=(uint32_t)float_value();
        D_target();
    }
    else PLC_Addr+=8;              //跳过8步程序
}
//======================================================================================================
// 函数名称:  static void INT(void)
// 功能描述： 浮点二进制换整数
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2014年6月27日
// 备  注:
//=======================================================================================================
static void INT(void)
{
    if(PLC_ACC_BIT&0X01)
    {
        trade=(uint16_t)float_value();
        PLC_Addr-=2;
        target();
    }
    else PLC_Addr+=4;              //跳过4步程序
}

static void INTP(void)
{
    if(PLC_LDP_TEST())
    {
        trade=(uint16_t)float_value();
        PLC_Addr-=2;
        target();
    }
    else PLC_Addr+=4;              //跳过4步程序
}

// kevin，2019年11月优化、新增
//
//======================================================================================================
// 函数名称:  static void FLT(void)
// 功能描述： 整数转浮点 FLT
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2014年6月27日
// 备  注:
//=======================================================================================================
static void FLT(void)
{
    if(PLC_ACC_BIT&0X01)
    {
        trade1=(float)cos_value();
        float_target();
        PLC_Addr-=2;
    }
    else PLC_Addr+=4;              //跳过4步程序
}

static void FLTP(void)
{
    if(PLC_LDP_TEST())
    {
        trade1=(float)cos_value();
        float_target();
        PLC_Addr-=2;
    }
    else PLC_Addr+=4;              //跳过4步程序
}

static void DFLT(void)	                 //整数转浮点
{
    signed int temp1;
    if(PLC_ACC_BIT&0X01)
    {
        temp1=cos_u32_value();
        trade1=(float)temp1;
        float_target();
    }
    else PLC_Addr+=8;              //跳过8步程序
}

static void DFLTP(void)	                 //整数转浮点
{
    signed int temp1;
    if(PLC_LDP_TEST())
    {
        temp1=cos_u32_value();
        trade1=(float)temp1;
        float_target();
    }
    else PLC_Addr+=8;              //跳过8步程序
}

// kevin，2019年11月优化、新增
//
static void _DEBCD(void)//二进制转十进制浮点 9步 kevin 20190331
{
    double temp;
    int32_t temp1=0;
    temp=float_value();
    if(temp!=0)
    {
        while(temp<1000) //小于1000
        {
            temp *= 10.0;
            temp1--;
        }
        while(temp>10000) //大于10000
        {
            temp/=10.0;
            temp1++;
        }
        trade = (temp1<<16) + (int32_t)temp;
        D_target();
    }
}

static void DEBCD(void)//二进制转十进制浮点 9步 kevin 20190331
{
    if(PLC_ACC_BIT&0X01)_DEBCD();
    else PLC_Addr+=8;              //跳过8步程序
}

static void DEBCDP(void)//二进制转十进制浮点 9步 kevin 20190331
{
    if(PLC_LDP_TEST())_DEBCD();
    else PLC_Addr+=8;              //跳过8步程序
}

static void _DEBIN(void)//十进制转二进制浮点 9步 kevin 20190331
{
    float temp;
    int32_t temp1;
    temp1=cos_u32_value();
    temp = (float)(temp1&0x0000ffff);
    trade1 = temp* (float)pow(10,temp1>>16);
    float_target();
}

static void DEBIN(void)//十进制转二进制浮点 9步 kevin 20190331
{
    if(PLC_ACC_BIT&0X01)_DEBIN();
    else PLC_Addr+=8;              //跳过8步程序
}

static void DEBINP(void)//十进制转二进制浮点P 9步 kevin 20190331
{
    if(PLC_LDP_TEST())_DEBIN();
    else PLC_Addr+=8;              //跳过8步程序
}

// kevin，2019年11月优化、新增
//
//======================================================================================================
// 函数名称:  static void DTAN(void)
// 功能描述： 浮点正切 DTAN
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2014年6月27日
// 备  注:
//=======================================================================================================
static void DTAN(void)
{
    if(PLC_ACC_BIT&0X01)
    {
        trade1=(float)tan((double)float_value());
        float_target();
    }
    else PLC_Addr+=8;              //跳过8步程序
}

static void DTANP(void)
{
    if(PLC_LDP_TEST())
    {
        trade1=(float)tan((double)float_value());
        float_target();
    }
    else PLC_Addr+=8;              //跳过8步程序
}
//======================================================================================================
// 函数名称:  static void DCOS(void)
// 功能描述： 浮点正余 DCOS
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2014年6月27日
// 备  注:
//=======================================================================================================
static void DCOS(void)
{
    if(PLC_ACC_BIT&0X01)
    {
        trade1=(float)cos((double)float_value());
        float_target();
    }
    else PLC_Addr+=8;              //跳过8步程序
}

// kevin，2019年11月优化、新增
// 
static void DCOSP(void)
{
    if(PLC_LDP_TEST())
    {
        trade1=(float)cos((double)float_value());
        float_target();
    }
    else PLC_Addr+=8;              //跳过8步程序
}

//======================================================================================================
// 函数名称:  static void DSIN(void)
// 功能描述： 浮点正弦  DSIN
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2014年6月27日
// 备  注:
//=======================================================================================================
static void DSIN(void)
{
    if(PLC_ACC_BIT&0X01)
    {
        trade1=(float)sin((double)float_value());
        float_target();
    }
    else PLC_Addr+=8;              //跳过8步程序
}

static void DSINP(void)
{
    if(PLC_LDP_TEST())
    {
        trade1=(float)sin((double)float_value());
        float_target();
    }
    else PLC_Addr+=8;              //跳过8步程序
}

// kevin，2019年11月优化、新增
// 
//======================================================================================================
// 函数名称:  static void DESQR(void)
// 功能描述： 浮点开方计算 DESQR
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2014年6月27日
// 备  注:
//=======================================================================================================
static void DESQR(void)
{
    if(PLC_ACC_BIT&0X01)
    {
        trade1=(float)sqrt((double)float_value());
        float_target();
    }
    else PLC_Addr+=8;              //跳过8步程序
}

static void DESQRP(void)
{
    if(PLC_LDP_TEST())
    {
        trade1=(float)sqrt((double)float_value());
        float_target();
    }
    else PLC_Addr+=8;              //跳过8步程序
}


// kevin，2019年11月优化、新增
// 
//======================================================================================================
// 函数名称:  static void DEADD(void)
// 功能描述： 浮点加法运算 指令DEADD
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2014年6月27日
// 备  注:
//=======================================================================================================
static void DEADD(void)
{
    if(PLC_ACC_BIT&0X01)
    {
        trade1=float_value()+ float_value();
        float_target();
    }
    else PLC_Addr+=12;              //跳过12步程序
}

static void DEADDP(void)
{
    if(PLC_LDP_TEST())
    {
        trade1=float_value()+ float_value();
        float_target();
    }
    else PLC_Addr+=12;              //跳过12步程序
}

static void DESUB(void)	           //浮点减法运算
{
    float temp1,temp2;
    if(PLC_ACC_BIT&0X01)
    {
        temp1 = float_value();
        temp2 = float_value();
        trade1=temp1-temp2;
        float_target();
    }
    else PLC_Addr+=12;              //跳过12步程序
}

static void DESUBP(void)	           //浮点减法运算P
{
    float temp1,temp2;
    if(PLC_LDP_TEST())
    {
        temp1 = float_value();
        temp2 = float_value();
        trade1=temp1-temp2;
        float_target();
    }
    else PLC_Addr+=12;              //跳过12步程序
}

// kevin，2019年11月优化、新增
// 
static void DEDIV(void)	           //浮点除法运算
{
    float temp1,temp2;
    if((PLC_ACC_BIT&0X01)==0X01)
    {
        temp1 = float_value();
        temp2 = float_value();
        trade1=temp1/temp2;
        float_target();
    }
    else PLC_Addr+=12;              //跳过12步程序
}

static void DEDIVP(void)	           //浮点除法运算P
{
    float temp1,temp2;
    if(PLC_LDP_TEST())
    {
        temp1 = float_value();
        temp2 = float_value();
        trade1=temp1/temp2;
        float_target();
    }
    else PLC_Addr+=12;              //跳过12步程序
}

// kevin，2019年11月优化、新增
// 
static void DEMUL(void)	                //浮点乘法运算
{
    if((PLC_ACC_BIT&0X01)==0X01)
    {
        trade1=float_value()*float_value();
        float_target();
    }
    else PLC_Addr+=12;              //跳过12步程序
}

static void DEMULP(void)	                //浮点乘法运算P
{
    if(PLC_LDP_TEST())
    {
        trade1=float_value()*float_value();
        float_target();
    }
    else PLC_Addr+=12;              //跳过12步程序
}



static void Damount(void)	 //32位等于比较指令
{
    int32_t temp1,temp2;
    temp1=cos_u32_value();
    temp2=cos_u32_value();

    if((PLC_STL_Status ==1)||(MC.MC_Flg ==1))
    {
        if(PLC_STL_Status == 1)  //为STL状态区
        {
            PLC_ACC_BIT<<=1;
            if((temp1==temp2)&&(PLC_BIT_TEST(PLC_STL_Addr)))
                PLC_ACC_BIT|=1;
        }

        if (MC.MC_Flg == 1)
        {
            if((temp1==temp2)&&(MC.PLC_MC_BIT & 0x01))
                PLC_ACC_BIT = (MC.PLC_MC_BIT & 0x01);
            else
                PLC_ACC_BIT = (MC.PLC_MC_BIT & 0x00);
        }
    }
    else
    {
        PLC_ACC_BIT<<=1;
        if(temp1==temp2)     //当前值判断
            PLC_ACC_BIT|=1;
    }
}

static void amount(void)	 //16位等于比较指令
{
    signed short int temp1,temp2;
    temp1=cos_value();
    temp2=cos_value();
    if((PLC_STL_Status ==1)||(MC.MC_Flg ==1))
    {
        if(PLC_STL_Status == 1) // 为STL状态区
        {
            PLC_ACC_BIT<<=1;
            if((temp1==temp2)&&(PLC_BIT_TEST(PLC_STL_Addr)))
                PLC_ACC_BIT|=1;
        }
        if (MC.MC_Flg == 1)
        {
            if((temp1==temp2)&&(MC.PLC_MC_BIT & 0x01))
                PLC_ACC_BIT = (MC.PLC_MC_BIT & 0x01);
            else
                PLC_ACC_BIT = (MC.PLC_MC_BIT & 0x00);
        }
    }
    else
    {
        PLC_ACC_BIT<<=1;
        if(temp1==temp2) // 当前值判断
            PLC_ACC_BIT|=1;
    }
}

static void amount_OR()
{
    signed short int temp1,temp2;
    temp1=cos_value();
    temp2=cos_value();
    if((temp1==temp2)||(PLC_ACC_BIT&0X01))  //当前值判断
        PLC_ACC_BIT|=0X01;
    else
        PLC_ACC_BIT&=0xFE;
}

static void Damount_OR()
{
    int32_t temp1,temp2;
    temp1=cos_u32_value();
    temp2=cos_u32_value();
    if((temp1==temp2)||(PLC_ACC_BIT&0X01))  //当前值判断
        PLC_ACC_BIT|=0X01;
    else
        PLC_ACC_BIT&=0xFE;
}

static void Damount_and(void)	 //32位AND等于比较
{
    int32_t temp1,temp2;
    temp1=cos_u32_value();
    temp2=cos_u32_value();
    if((temp1==temp2)&&((PLC_ACC_BIT&0X01)==0X01))
        PLC_ACC_BIT|=1;
    else
        PLC_ACC_BIT&=~1;
}

static void amount_and(void)	 //16位AND等于比较
{
    signed short int temp1,temp2;
    temp1=cos_value();
    temp2=cos_value();
    if((temp1==temp2)&&((PLC_ACC_BIT&0X01)==0X01))
        PLC_ACC_BIT|=1;
    else
        PLC_ACC_BIT&=~1;
}

static void Dbig(void)		    //32位大于比较指令
{
    int32_t temp1,temp2;
    temp1=cos_u32_value();
    temp2=cos_u32_value();

    if((PLC_STL_Status == 1)||(MC.MC_Flg ==1))
    {
        if(PLC_STL_Status == 1)                       //为STL状态区
        {
            PLC_ACC_BIT<<=1;
            if((temp1>temp2)&&(PLC_BIT_TEST(PLC_STL_Addr)))
                PLC_ACC_BIT|=1;
        }

        if(MC.MC_Flg ==1)
        {
            if((temp1>temp2)&&(MC.PLC_MC_BIT & 0x01))
                PLC_ACC_BIT = (MC.PLC_MC_BIT & 0x01);
            else
                PLC_ACC_BIT = (MC.PLC_MC_BIT & 0x00);
        }

    }
    else
    {
        PLC_ACC_BIT<<=1;
        if(temp1>temp2) 						               //当前值判断
            PLC_ACC_BIT|=1;
    }
}

static void big(void)	// 16位大于比较指令
{
    signed short int temp1,temp2;
    temp1=cos_value();
    temp2=cos_value();

    if((PLC_STL_Status == 1)||(MC.MC_Flg ==1))
    {
        if(PLC_STL_Status == 1) // 为STL状态区
        {
            PLC_ACC_BIT<<=1;
            if((temp1>temp2)&&(PLC_BIT_TEST(PLC_STL_Addr)))
                PLC_ACC_BIT|=1;
        }

        if(MC.MC_Flg ==1)
        {
            if((temp1>temp2)&&(MC.PLC_MC_BIT & 0x01))
                PLC_ACC_BIT = (MC.PLC_MC_BIT & 0x01);
            else
                PLC_ACC_BIT = (MC.PLC_MC_BIT & 0x00);
        }
    }
    else
    {
        PLC_ACC_BIT<<=1;
        if(temp1>temp2) //当前值判断	billnie 20180615
            PLC_ACC_BIT|=1;
    }
}

static void big_OR()
{
    signed short int temp1,temp2;
    temp1=cos_value();
    temp2=cos_value();
    if((temp1>temp2)||(PLC_ACC_BIT&0X01))
        PLC_ACC_BIT|=0X01;
    else
        PLC_ACC_BIT&=0xFE;
}

static void Dbig_OR()
{
    int32_t temp1,temp2;
    temp1=cos_u32_value();
    temp2=cos_u32_value();
    if((temp1>=temp2)||(PLC_ACC_BIT&0X01))
        PLC_ACC_BIT|=0X01;
    else
        PLC_ACC_BIT&=0xFE;
}

static void Dbig_and(void)		//32位AND大于比较指令
{
    int32_t temp1,temp2;
    temp1=cos_u32_value();
    temp2=cos_u32_value();
    if((temp1>temp2)&&((PLC_ACC_BIT&0X01)==0X01))
        PLC_ACC_BIT|=1;
    else
        PLC_ACC_BIT&=~1;
}

static void big_and(void)		//16位AND大于比较指令
{
    signed short int temp1,temp2;
    temp1=cos_value();
    temp2=cos_value();
    if((temp1>temp2)&&((PLC_ACC_BIT&0X01)==0X01))
        PLC_ACC_BIT|=1;
    else
        PLC_ACC_BIT&=~1;
}

static void Dless(void)	     //32位小于比较指令
{
    int32_t temp1,temp2;
    temp1=cos_u32_value();
    temp2=cos_u32_value();
    if((PLC_STL_Status == 1) ||(MC.MC_Flg ==1))
    {
        if(PLC_STL_Status == 1) //为STL状态区
        {
            PLC_ACC_BIT<<=1;
            if((temp1<temp2)&&(PLC_BIT_TEST(PLC_STL_Addr)))
                PLC_ACC_BIT|=1;
        }

        if(MC.MC_Flg ==1)
        {
            if((temp1<temp2)&&(MC.PLC_MC_BIT & 0x01))
                PLC_ACC_BIT = (MC.PLC_MC_BIT & 0x01);
            else
                PLC_ACC_BIT = (MC.PLC_MC_BIT & 0x00);
        }
    }
    else
    {
        PLC_ACC_BIT<<=1;
        if(temp1<temp2) // 当前值判断
            PLC_ACC_BIT|=1;
    }
}

static void less(void)	     //小于比较
{
    signed short int temp1,temp2;
    temp1=cos_value();
    temp2=cos_value();
    if((PLC_STL_Status == 1) ||(MC.MC_Flg ==1))
    {
        if(PLC_STL_Status == 1) //为STL状态区
        {
            PLC_ACC_BIT<<=1;
            if((temp1<temp2)&&(PLC_BIT_TEST(PLC_STL_Addr)))
                PLC_ACC_BIT|=1;
        }

        if(MC.MC_Flg ==1)
        {
            if((temp1<temp2)&&(MC.PLC_MC_BIT & 0x01))
                PLC_ACC_BIT = (MC.PLC_MC_BIT & 0x01);
            else
                PLC_ACC_BIT = (MC.PLC_MC_BIT & 0x00);
        }
    }
    else
    {
        PLC_ACC_BIT<<=1;
        if(temp1<temp2) //当前值判断
            PLC_ACC_BIT|=1;
    }
}

static void less_OR()
{
    signed short int temp1,temp2;
    temp1=cos_value();
    temp2=cos_value();
    if((temp1<temp2)||(PLC_ACC_BIT&0X01))
        PLC_ACC_BIT|=0X01;
    else
        PLC_ACC_BIT&=0xFE;
}

static void Dless_OR()
{
    int32_t temp1,temp2;
    temp1=cos_u32_value();
    temp2=cos_u32_value();
    if((temp1<temp2)||(PLC_ACC_BIT&0X01))
        PLC_ACC_BIT|=0X01;
    else
        PLC_ACC_BIT&=0xFE;
}

static void Dless_and(void)	   //32位AND小于比较
{
    int32_t temp1,temp2;
    temp1=cos_u32_value();
    temp2=cos_u32_value();
    if((temp1<temp2)&&((PLC_ACC_BIT&0X01)==0X01))
        PLC_ACC_BIT|=1;
    else
        PLC_ACC_BIT&=~1;
}

static void less_and(void)	   //16位AND小于比较
{
    signed short int temp1,temp2;
    temp1=cos_value();
    temp2=cos_value();
    if((temp1<temp2)&&((PLC_ACC_BIT&0X01)==0X01))
        PLC_ACC_BIT|=1;
    else
        PLC_ACC_BIT&=~1;
}

static void Dless_amount(void)	     //32位小于等于比较指令
{
    int32_t temp1,temp2;
    temp1=cos_u32_value();
    temp2=cos_u32_value();
    if((PLC_STL_Status == 1) ||(MC.MC_Flg ==1))
    {
        if(PLC_STL_Status == 1)            //为STL状态区
        {
            PLC_ACC_BIT<<=1;
            if((temp1<=temp2)&&(PLC_BIT_TEST(PLC_STL_Addr)))
                PLC_ACC_BIT|=1;
        }

        if(MC.MC_Flg ==1)
        {
            if((temp1<=temp2)&&(PLC_BIT_TEST(PLC_STL_Addr)))
                PLC_ACC_BIT = (MC.PLC_MC_BIT & 0x01);
            else
                PLC_ACC_BIT = (MC.PLC_MC_BIT & 0x00);
        }
    }
    else
    {
        PLC_ACC_BIT<<=1;
        if(temp1<=temp2) 						    //当前值判断
            PLC_ACC_BIT|=1;
    }
}

static void less_amount(void)	      //16位小于等于比较指令
{
    signed short int temp1,temp2;
    temp1=cos_value();
    temp2=cos_value();
    if((PLC_STL_Status == 1) ||(MC.MC_Flg ==1))
    {
        if(PLC_STL_Status == 1)           //为STL状态区
        {
//			if(temp1<=temp2)
            if((temp1<=temp2)&&(PLC_BIT_TEST(PLC_STL_Addr)))
                PLC_ACC_BIT &=0xFF;
            else
                PLC_ACC_BIT &=0xfE;
        }

        if(MC.MC_Flg ==1)
        {
            if((temp1<=temp2)&&(PLC_BIT_TEST(PLC_STL_Addr)))
                PLC_ACC_BIT = (MC.PLC_MC_BIT & 0x01);
            else
                PLC_ACC_BIT = (MC.PLC_MC_BIT & 0x00);
        }
    }
    else
    {
        PLC_ACC_BIT<<=1;
        if(temp1<=temp2) 						               //当前值判断
            PLC_ACC_BIT|=1;
    }
}

static void less_amount_OR()
{
    signed short int temp1,temp2;
    temp1=cos_value();
    temp2=cos_value();
    if((temp1<=temp2)||(PLC_ACC_BIT&0X01))
        PLC_ACC_BIT|=0X01;
    else
        PLC_ACC_BIT&=0xFE;
}

static void Dless_amount_OR()
{
    int32_t temp1,temp2;
    temp1=cos_u32_value();
    temp2=cos_u32_value();
    if((temp1<=temp2)||(PLC_ACC_BIT&0X01))
        PLC_ACC_BIT|=0X01;
    else
        PLC_ACC_BIT&=0xFE;
}

static void Dless_amount_and(void)	   //32位AND小于等于比较
{
    int32_t temp1,temp2;
    temp1=cos_u32_value();
    temp2=cos_u32_value();
    if((temp1<=temp2)&&((PLC_ACC_BIT&0X01)==0X01))
        PLC_ACC_BIT|=1;
    else
        PLC_ACC_BIT&=~1;
}

static void less_amount_and(void)	//16位AND小于等于比较
{
    signed short int temp1,temp2;
    temp1=cos_value();
    temp2=cos_value();
    if((temp1<=temp2)&&(PLC_ACC_BIT&0X01))
        PLC_ACC_BIT|=0X01;
    else
        PLC_ACC_BIT&=0XFE;
}

static void Dbig_amount(void)	// 32位大于等于比较指令
{
    int32_t temp1,temp2;
    temp1=cos_u32_value();
    temp2=cos_u32_value();
    if((PLC_STL_Status == 1) ||(MC.MC_Flg ==1))
    {
        if(PLC_STL_Status == 1) //为STL状态区
        {
            PLC_ACC_BIT<<=1;
            if((temp1>=temp2)&&(PLC_BIT_TEST(PLC_STL_Addr)))
                PLC_ACC_BIT|=1;
        }

        if(MC.MC_Flg ==1)
        {
            if((temp1 >= temp2)&&(MC.PLC_MC_BIT & 0x01))
                PLC_ACC_BIT = (MC.PLC_MC_BIT & 0x01);
            else
                PLC_ACC_BIT = (MC.PLC_MC_BIT & 0x00);
        }
    }
    else
    {
        PLC_ACC_BIT<<=1;
        if(temp1>=temp2) //当前值判断
            PLC_ACC_BIT|=1;
    }
}

static void big_amount(void) //16位大于等于比较指令
{
    signed short int temp1,temp2;
    temp1=cos_value();
    temp2=cos_value();
    if((PLC_STL_Status == 1) ||(MC.MC_Flg ==1))
    {
        if(PLC_STL_Status == 1) //为STL状态区
        {
            PLC_ACC_BIT<<=1;
            if((temp1>=temp2)&&(PLC_BIT_TEST(PLC_STL_Addr)))
                PLC_ACC_BIT|=1;
        }

        if(MC.MC_Flg ==1)
        {
            if((temp1 >= temp2)&&(MC.PLC_MC_BIT & 0x01))
                PLC_ACC_BIT = (MC.PLC_MC_BIT & 0x01);
            else
                PLC_ACC_BIT = (MC.PLC_MC_BIT & 0x00);
        }
    }
    else
    {
        PLC_ACC_BIT<<=1;
        if(temp1>=temp2) // 当前值判断
            PLC_ACC_BIT|=1;
    }
}

static void big_amount_OR()
{
    signed short int temp1,temp2;
    temp1=cos_value();
    temp2=cos_value();
    if((temp1>=temp2)||(PLC_ACC_BIT&0X01))
        PLC_ACC_BIT|=0X01;
    else
        PLC_ACC_BIT&=0xFE;
}

static void Dbig_amount_OR()
{
    int32_t temp1,temp2;
    temp1=cos_u32_value();
    temp2=cos_u32_value();
    if((temp1>=temp2)||(PLC_ACC_BIT&0X01))
        PLC_ACC_BIT|=0X01;
    else
        PLC_ACC_BIT&=0xFE;
}

static void Dbig_amount_and(void)	   //32位AND大于等于比较
{
    int32_t temp1,temp2;
    temp1=cos_u32_value();
    temp2=cos_u32_value();
    if((temp1>=temp2)&&((PLC_ACC_BIT&0X01)==0X01))
        PLC_ACC_BIT|=1;
    else
        PLC_ACC_BIT&=~1;
}

static void big_amount_and(void)	   //16位AND大于等于比较
{
    signed short int temp1,temp2;
    temp1=cos_value();
    temp2=cos_value();
    if((temp1>=temp2)&&(PLC_ACC_BIT&0X01))
        PLC_ACC_BIT|=1;
    else
        PLC_ACC_BIT&=~1;
}

static void Dno_amount(void) // 32位不等于比较指令
{
    int32_t temp1,temp2;
    temp1=cos_u32_value();
    temp2=cos_u32_value();

    if((PLC_STL_Status == 1) || (MC.MC_Flg ==1))
    {
        if(PLC_STL_Status == 1)            //为STL状态区
        {
            PLC_ACC_BIT<<=1;
            if((temp1 != temp2)&&(PLC_BIT_TEST(PLC_STL_Addr)))
                PLC_ACC_BIT|=1;
        }
        if(MC.MC_Flg ==1)
        {
            if((temp1 != temp2)&&(MC.PLC_MC_BIT & 0x01))
                PLC_ACC_BIT = (MC.PLC_MC_BIT & 0x01);
            else
                PLC_ACC_BIT = (MC.PLC_MC_BIT & 0x00);
        }
    }
    else
    {
        PLC_ACC_BIT<<=1;
        if(temp1!=temp2) 					 //当前值判断
            PLC_ACC_BIT|=1;
    }
}

static void no_amount(void)	 //16位不等于比较指令
{
    signed short int temp1,temp2;
    temp1=cos_value();
    temp2=cos_value();

    if((PLC_STL_Status == 1) || (MC.MC_Flg ==1))
    {
        if(PLC_STL_Status == 1)            //为STL状态区
        {
            PLC_ACC_BIT<<=1;
            if((temp1!=temp2)&&(PLC_BIT_TEST(PLC_STL_Addr)))
                PLC_ACC_BIT|=1;
        }

        if(MC.MC_Flg ==1)
        {
            if((temp1 != temp2)&&(MC.PLC_MC_BIT & 0x01))
                PLC_ACC_BIT = (MC.PLC_MC_BIT & 0x01);
            else
                PLC_ACC_BIT = (MC.PLC_MC_BIT & 0x00);
        }
    }
    else
    {
        PLC_ACC_BIT<<=1;
        if(temp1!=temp2) 						               //当前值判断
            PLC_ACC_BIT|=1;
    }
}

static void no_amount_OR()
{
    signed short int temp1,temp2;
    temp1=cos_value();
    temp2=cos_value();
    if((temp1!=temp2)||(PLC_ACC_BIT&0X01))
        PLC_ACC_BIT|=0X01;
    else
        PLC_ACC_BIT&=0xFE;
}

static void Dno_amount_OR()
{
    int32_t temp1,temp2;
    temp1=cos_u32_value();
    temp2=cos_u32_value();
    if((temp1!=temp2)||(PLC_ACC_BIT&0X01))
        PLC_ACC_BIT|=0X01;
    else
        PLC_ACC_BIT&=0xFE;
}

static void Dno_amount_and(void)	   //32位AND不等于比较指令
{
    int32_t temp1,temp2;
    temp1=cos_u32_value();
    temp2=cos_u32_value();
    if((temp1!=temp2)&&((PLC_ACC_BIT&0X01)==0X01))
        PLC_ACC_BIT|=1;
    else
        PLC_ACC_BIT&=~1;
}

static void no_amount_and(void)	   //16位AND不等于比较指令
{
    signed short int temp1,temp2;
    temp1=cos_value();
    temp2=cos_value();
    if((temp1!=temp2)&&((PLC_ACC_BIT&0X01)==0X01))
        PLC_ACC_BIT|=1;
    else
        PLC_ACC_BIT&=~1;
}


static void LDP(void)	                        //LDP
{
    if(PLC_PL_BIT_TEST(PLC_Addr-PLC_LAD_START_ADDR))
    {
        PLC_ACC_BIT<<=1;
        if(!(PLC_LD_BIT(0X2fff&*PLC_Addr)))							                    //当前值判断
            PLC_PL_BIT_OFF(PLC_Addr-PLC_LAD_START_ADDR);
    }
    else  							                                                     //上升沿判断
    {
        if(PLC_STL_Status == 1)                                             //为STL状态区
        {
            PLC_ACC_BIT<<=1;
            if((PLC_LD_BIT(0X2fff&*PLC_Addr))&&(PLC_BIT_TEST(PLC_STL_Addr)))  //当前值判断
                PLC_ACC_BIT|=0x01,PLC_PL_BIT_ON(PLC_Addr-PLC_LAD_START_ADDR);      //
        }
        else
        {
            PLC_ACC_BIT<<=1;
            if(PLC_LD_BIT(0X2fff&*PLC_Addr))							                   //当前值判断
                PLC_ACC_BIT|=0x01,PLC_PL_BIT_ON(PLC_Addr-PLC_LAD_START_ADDR);      //
        }
    }
    PLC_Addr++;
}

static void LDF(void)	 //LDF
{
    if(PLC_PL_BIT_TEST(PLC_Addr-PLC_LAD_START_ADDR))	 //上升沿判断
    {
        if(PLC_STL_Status == 1)                       //为STL状态区
        {
            PLC_ACC_BIT<<=1;
            if((!(PLC_LD_BIT(0X2fff&*PLC_Addr)))&&(PLC_BIT_TEST(PLC_STL_Addr)))//当前值判断
                PLC_ACC_BIT|=0x01,PLC_PL_BIT_ON(PLC_Addr-PLC_LAD_START_ADDR);
        }
        else
        {
            PLC_ACC_BIT<<=1;
            if(!(PLC_LD_BIT(0X2fff&*PLC_Addr)))                     //当前值判断
                PLC_ACC_BIT|=1,PLC_PL_BIT_OFF(PLC_Addr-PLC_LAD_START_ADDR);//
        }
    }
    else
    {
        PLC_ACC_BIT<<=1,PLC_ACC_BIT&=0XFE;        //清除输出标准开关
        if(PLC_LD_BIT(0X2fff&*PLC_Addr))        //当前值判断
            PLC_PL_BIT_ON(PLC_Addr-PLC_LAD_START_ADDR);//
    }
    PLC_Addr++;
}

static void ANDP(void)	 //ANDP
{
    uint8_t  logic;
    if(PLC_PL_BIT_TEST(PLC_Addr-PLC_LAD_START_ADDR))       //查出当前步号对应的逻辑值
    {
        logic=0;
        if(!(PLC_LD_BIT(0X2fff&*PLC_Addr)))   					   //当前值判断
            PLC_PL_BIT_OFF(PLC_Addr-PLC_LAD_START_ADDR);         //
    }
    else	 							                                   //上升沿判断
    {
        if(PLC_LD_BIT(0X2fff&*PLC_Addr))	                 //当前值判断
            logic=1,PLC_PL_BIT_ON(PLC_Addr-PLC_LAD_START_ADDR);  //上升沿成立
        else
            logic=0;		                                         //上升沿不成立
    }
    if((PLC_ACC_BIT&0x01)&&(logic==1))
        PLC_ACC_BIT|=0X01;
    else
        PLC_ACC_BIT&=0XFE;
    PLC_Addr++;
}

static void ANDF(void)	 //ANDF
{
    uint8_t  logic;
    if(PLC_PL_BIT_TEST(PLC_Addr-PLC_LAD_START_ADDR))			  //上升沿判断
    {
        if(!(PLC_LD_BIT(0X2fff&*PLC_Addr)))                //当前值判断
            logic=1,PLC_PL_BIT_OFF(PLC_Addr-PLC_LAD_START_ADDR);  //
        else
            logic=0;		 //
    }
    else
    {
        logic=0;
        if(PLC_LD_BIT(0X2fff&*PLC_Addr))                   //当前值判断
            PLC_PL_BIT_ON(PLC_Addr-PLC_LAD_START_ADDR);		        //
    }
    if((PLC_ACC_BIT&0x01)&&(logic==1))
        PLC_ACC_BIT|=0X01;
    else
        PLC_ACC_BIT&=0XFE;
    PLC_Addr++;
}

static void ORP(void)	 //ORP
{
    uint8_t  logic;
    if(PLC_PL_BIT_TEST(PLC_Addr-PLC_LAD_START_ADDR))
    {
        logic=0;                                           //
        if(!(PLC_LD_BIT(0X2fff&*PLC_Addr)))							   //当前值判断
            PLC_PL_BIT_OFF(PLC_Addr-PLC_LAD_START_ADDR);		       //
    }
    else                                                   //上升沿判断
    {
        if(PLC_LD_BIT(0X2fff&*PLC_Addr))							     //当前值判断
            logic=1,PLC_PL_BIT_ON(PLC_Addr-PLC_LAD_START_ADDR); //
        else
            logic=0;		 //
    }

    if(((PLC_ACC_BIT&0x01)==0x01)||(logic==1))
        PLC_ACC_BIT|=0x01;
    else
        PLC_ACC_BIT&=0XFE;
    PLC_Addr++;
}

static void ORF(void)	 //ORF
{
    uint8_t  logic;
    if(PLC_PL_BIT_TEST(PLC_Addr-PLC_LAD_START_ADDR))							                            //上升沿判断
    {
        if(!(PLC_LD_BIT(0X2fff&*PLC_Addr)))						    //当前值判断
            logic=1,PLC_PL_BIT_OFF(PLC_Addr-PLC_LAD_START_ADDR);//
        else
            logic=0;		 //
    }
    else
    {
        logic=0;
        if(PLC_LD_BIT(0X2fff&*PLC_Addr))							   //当前值判断
            PLC_PL_BIT_ON(PLC_Addr-PLC_LAD_START_ADDR);	//
    }
    if(((PLC_ACC_BIT&0x01)==0x01)||(logic==1))
        PLC_ACC_BIT|=1;
    else
        PLC_ACC_BIT&=~1;
    PLC_Addr++;
}

static void CJ_EX(uint8_t value)  //执行跳指令
{
    PLC_Addr++;
    if((*PLC_Addr&0xff00)==0x8000)
    {
        PLC_Addr=PLC_P_Addr[value/2],PLC_Addr++;   //取低位
    }
}

static void CJ(void)
{
    if(PLC_ACC_BIT&0X01)
    {
        if((*PLC_Addr&0xff00)==0x8800) CJ_EX(*PLC_Addr);
    }
    else PLC_Addr+=2;
}

static void CJP(void)	  //CJP
{
    if(PLC_LDP_TEST())    //上升沿判断
    {
        if((*PLC_Addr&0xff00)==0x8800) CJ_EX(*PLC_Addr);
    }
    else
        PLC_Addr+=2;		      //条件不满足执行跳过程序，减小CPU开销
}

static void SRET(void)
{
    uint8_t temp;
    PLC_ACC_BIT=process[0];	    //返回上一个逻辑状态值
    PLC_Addr=p_save[0];	  	    //返回上一个子程序前的执行地址
    for(temp=62; temp>0; temp--)
    {
        process[temp]=process[temp+1];    //data mov down
        p_save[temp]=p_save[temp+1];
    }
}


static void WDT(void)
{
    if(PLC_ACC_BIT&0X01)
    {
        D8010+=plc_scan_time;
        plc_scan_time = 0;
    }
}
static void WDTP(void)
{
    if(PLC_LDP_TEST())    //上升沿判断
    {
        D8010+=plc_scan_time;
        plc_scan_time = 0;
    }
}



static void P_MOV(void)
{
    uint8_t temp;
    for(temp=62; temp>0; temp--)
    {
        process[temp+1]=process[temp];    //数据 MOV up
        p_save[temp+1]=p_save[temp];
    }
    process[0]=PLC_ACC_BIT;	               //保存上一个逻辑状态值
    p_save[0]=PLC_Addr;				             //保存上一个子程序前的执行地址
}

static void CALL_EX(uint8_t value)
{
    PLC_Addr++;
    if((*PLC_Addr&0xff00)==0x8000)
    {
        PLC_Addr++;
        P_MOV(),PLC_Addr=PLC_P_Addr[value/2];   //	先压入状态寄存器，以及前一个P指针的地址，
    }
}


//warning "kevin，2017年03月20日新增，MC和MCR指令";
void Func_MC()
{
//		MC.MC_Addr = 0x0FFF & *PLC_Addr;
    MC.PLC_MC_BIT <<= 1; // MC寄存器暂存位
    MC.MC_SFR += 1;
    MC.MC_Flg = 1; // MC运算标志
    if (PLC_ACC_BIT & 0x01)
        MC.PLC_MC_BIT |= 0x01; // 运算位
    PLC_Addr += 2;
}


//warning "kevin，2017年03月20日新增，MC和MCR指令";
void Func_MCR()
{
//		MC.MC_Addr = 0x0FFF & *PLC_Addr;
    MC.PLC_MC_BIT >>= 1; // MC寄存器暂存位
    MC.MC_SFR -= 1;
    if (MC.MC_SFR <= 0)
    {
        MC.MC_SFR =0;
        MC.MC_Flg = 0;	// MC运算标志  MCR在指令中清除
    }
    PLC_Addr ++;
}


static void CALL(void)
{
    if(PLC_ACC_BIT&0X01)
    {
        if((*PLC_Addr&0xff00)==0x8800)
        {
            CALL_EX(*PLC_Addr);
        }
    }
    else PLC_Addr+=2;
}

static void CALLP(void)	  //CALLP
{
    if(PLC_LDP_TEST())       //上升沿判断
    {
        if((*PLC_Addr&0xff00)==0x8800)CALL_EX(*PLC_Addr);
    }
    else
        PLC_Addr+=2;		        //条件不满足执行跳过程序，减小CPU开销
}

// kevin,20160929新增
static void FOR(void)
{
    //if((PLC_ACC_BIT&0X01)==0X01)
    {
        if(FOR_CMD.point<6)
            FOR_CMD.point++;             //判断嵌套数量
        else
            FOR_CMD.point=0;
        FOR_CMD.count[FOR_CMD.point] =(cos_value()-1);   //存入目标循环次数记录下来
        FOR_CMD.Addr[FOR_CMD.point]=PLC_Addr;            //存入目标起始地址
        FOR_CMD.cycle[FOR_CMD.point]=0;                  //清楚当前循环次数
    }
}

// kevin,20160929新增
static void FOR_NEXT(void)
{
    if(FOR_CMD.cycle[FOR_CMD.point]<FOR_CMD.count[FOR_CMD.point])  //当前次数小于目标次数则返回到FOR的后一个地址
    {
        PLC_Addr=FOR_CMD.Addr[FOR_CMD.point];   //返回上个起始地址
        FOR_CMD.cycle[FOR_CMD.point]++;         //PLC循环一次
    }
}


void expand_SET(void)
{
    PLC_BIT_SET(0X2FFF&*PLC_Addr);
    PLC_Addr++;
}

void expand_RST(void)
{
    RST(0X2FFF&*PLC_Addr);
    PLC_Addr++;
}

void expand_OUT(void)
{
    OUT(0X2FFF&*PLC_Addr);
    PLC_Addr++;
}

void expand_LD(void)
{
    LD(0X2FFF&*PLC_Addr);
    PLC_Addr++;
}

void expand_LDI(void)
{
    LDI(0x2FFF&*PLC_Addr);
    PLC_Addr++;
}

void expand_AND(void)
{
    AND(0x2FFF&*PLC_Addr);
    PLC_Addr++;
}

void expand_ANI(void)
{
    ANI(0x2FFF&*PLC_Addr);
    PLC_Addr++;
}

void expand_OR(void)
{
    OR(0x2FFF&*PLC_Addr);
    PLC_Addr++;
}

void expand_ORI(void)
{
    ORI(0x2FFF&*PLC_Addr);
    PLC_Addr++;
}

static void enable_T_K(void)
{
    T_value =*PLC_Addr%0x100;                //赋低8位值
    PLC_Addr++;
    T_value +=(*PLC_Addr%0x100)*0x100;       //赋高8位值
    plc_16BitBuf[HELON_REG_T_TARGET_START_ADDR + T_number] =T_value;  //赋值给地址
    timer_enable(T_number);
    // TODO ADDR DEFINE
    OUT(COIL_T_ENABLE_ADDR+(uint8_t)T_number);
}

static void enable_T_D(void)
{
    plc_16BitBuf[HELON_REG_T_TARGET_START_ADDR + T_number]=plc_16BitBuf[HELON_REG_D_START_ADDR + T_value];
    timer_enable(T_number);
    // TODO ADDR DEFINE
    OUT(COIL_T_ENABLE_ADDR+(uint8_t)T_number);
}

static void disable_T(void)
{
    timer_disble(T_number);
    // TODO ADDR DEFINE
    OUT(COIL_T_ENABLE_ADDR+(uint8_t)T_number);	 //disable T coil
// TODO BUG
    OUT(COIL_T_OVERFLOW_ADDR+(uint8_t)T_number);	 //reset T over coil
}

static void T_given_value_K(void)
{
    if(PLC_ACC_BIT&0X01)
    {
        enable_T_K();
    }
    else
    {
        PLC_Addr++;
        disable_T();
    }
}
static void T_given_value_D(void)
{
    T_value=(*PLC_Addr%0x100)/2;
    PLC_Addr++;
    switch(*PLC_Addr/0x100)
    {
        case 0x86:
            T_value+=(*PLC_Addr%0x100)*0x80;
            break;
        case 0x88:
            T_value+=(*PLC_Addr%0x100)*0x80+1000;
            break;
    }
    if((PLC_ACC_BIT&0X01)==0X01)  //是否有效
        enable_T_D();
    else
        disable_T();
}

static void operation_T(void)
{
    T_number=*PLC_Addr;       //将操作定时器的号码送入
    PLC_Addr++;				        //下一个功能取是K赋值还是D赋值
    switch(*PLC_Addr/0x100)
    {
        case 0x80:
            T_given_value_K();
            break;  //进行K赋值操作
        case 0x86:
            T_given_value_D();
            break;  //进行D赋值操作
    }
}

// kevin，2019年11月优化、新增
// 
static void STMR(void)//特殊定时器 2019 04 07 kevin
{
    uint16_t temp;
    uint16_t const *paddr;
    paddr = PLC_Addr;
    if(PLC_ACC_BIT&0X01)
    {   //T_number 定时器编号  T_value定时器比较值  plc_16BitBuf+HELON_REG_T_TARGET_START_ADDR+T_number 定时器比较值地址
        if(PLC_PL_BIT_TEST(paddr-PLC_LAD_START_ADDR+1)==off)
        {
            T_number = (uint8_t)(addr_value());
            T_value  = cos_value();
            temp = addr_value();
            plc_16BitBuf[HELON_REG_T_TARGET_START_ADDR + T_number] = T_value;                        //赋值给地址
            timer_enable(T_number);
            // TODO ADDR DEFINE
            PLC_BIT_ON(COIL_T_ENABLE_ADDR+(uint8_t)T_number);
            PLC_BIT_ON(temp);
            PLC_BIT_OFF(temp+1);
            PLC_BIT_ON(temp+2);
            PLC_BIT_OFF(temp+3);
            PLC_PL_BIT_ON(paddr-PLC_LAD_START_ADDR+1);
        }
        else if( PLC_PL_BIT_TEST(paddr-PLC_LAD_START_ADDR+2)==off)
        {
            T_number = (uint8_t)(addr_value());
            T_value  = cos_value();
            temp = addr_value();
            if(PLC_BIT_TEST(COIL_T_OVERFLOW_ADDR+(uint8_t)T_number)==on) //定时时间到
            {
                PLC_BIT_ON(temp);
                PLC_BIT_OFF(temp+1);
                PLC_BIT_OFF(temp+2);
                PLC_BIT_ON(temp+3);
                PLC_PL_BIT_ON(paddr-PLC_LAD_START_ADDR+2);
                timer_disble(T_number);;//关闭定时器
            }
            else timer_enable(T_number);
        }
        else  //等待输入断开
        {
            PLC_Addr+=6;
        }
    }
    else
    {
        if(PLC_PL_BIT_TEST(paddr-PLC_LAD_START_ADDR+2)==on)// 接通定时时间到断开
        {
            T_number = (uint8_t)(addr_value());
            T_value  = cos_value();
            temp = addr_value();
            plc_16BitBuf[HELON_REG_T_TARGET_START_ADDR + T_number] = T_value;                        //赋值给地址
            timer_enable(T_number);
            // TODO ADDR DEFINE
            PLC_BIT_ON(COIL_T_ENABLE_ADDR+(uint8_t)T_number);
            PLC_BIT_ON(temp);
            PLC_BIT_ON(temp+1);
            PLC_BIT_OFF(temp+2);
            PLC_BIT_ON(temp+3);
            PLC_PL_BIT_OFF(paddr-PLC_LAD_START_ADDR+1);
            PLC_PL_BIT_OFF(paddr-PLC_LAD_START_ADDR+2);
            PLC_PL_BIT_ON(paddr-PLC_LAD_START_ADDR+3);
        }
        else if(PLC_PL_BIT_TEST(paddr-PLC_LAD_START_ADDR+1)==on)//接通定时时间没到断开
        {
            T_number = (uint8_t)(addr_value());
            T_value  = cos_value();
            temp = addr_value();
            timer_disble(T_number);;//关闭定时器
            plc_16BitBuf[HELON_REG_T_TARGET_START_ADDR + T_number] = T_value;                        //赋值给地址
            timer_enable(T_number);
            // TODO ADDR DEFINE
            PLC_BIT_ON(COIL_T_ENABLE_ADDR+(uint8_t)T_number);
            PLC_BIT_ON(temp);
            PLC_BIT_ON(temp+1);
            PLC_BIT_OFF(temp+2);
            PLC_BIT_ON(temp+3);
            PLC_PL_BIT_OFF(paddr-PLC_LAD_START_ADDR+1);
            PLC_PL_BIT_ON(paddr-PLC_LAD_START_ADDR+3);
        }
        else if(PLC_PL_BIT_TEST(paddr-PLC_LAD_START_ADDR+3)==on)//断开延时
        {
            T_number = (uint8_t)(addr_value());
            T_value  = cos_value();
            temp = addr_value();
            // TODO ADDR DEFINE
            if(PLC_BIT_TEST(COIL_T_OVERFLOW_ADDR+(uint8_t)T_number)==on) //定时时间到 溢出线圈置位
            {
                PLC_BIT_OFF(temp);
                PLC_BIT_OFF(temp+1);
                PLC_BIT_OFF(temp+2);
                PLC_BIT_OFF(temp+3);
                PLC_PL_BIT_OFF(paddr-PLC_LAD_START_ADDR+3);
                timer_disble(T_number);;//关闭定时器
            }
            else timer_enable(T_number);
        }
        else PLC_Addr+=6;
    }
}
//////////////////////////////////////////////////////////////////////////////////
// kevin，20170419整理
static void enable_C_K(void) // 用常数K进行赋值
{
    uint16_t temp_bit,*p_C_enable_coil;
    uint32_t C;
    C_value=*PLC_Addr%0x100; // 赋低8位值
    PLC_Addr++;
    C_value+=(*PLC_Addr%0x100)*0x100; // 赋高8位值
    if(C_number >= 200) // 判断是不是C200以上的 寄存器
    {
        PLC_Addr++;
        /* 得到C设定值 */
        C_value+=(*PLC_Addr%0x100)*0x10000; //赋低8位值
        PLC_Addr++;
        C_value+=(*PLC_Addr%0x100)*0x1000000;//赋高8位值
        C = RAM_C200_ADDR + (C_number - 200)*4; // C200及以上
        {
//					PLC_Addr++;
//					/* 得到C设定值 */
//					C_value+=(*PLC_Addr%0x100)*0x10000; //赋低8位值
//					PLC_Addr++;
//					C_value+=(*PLC_Addr%0x100)*0x1000000;//赋高8位值
//					C = RAM_C200_ADDR + (C_number - 200)*4; // C200及以上

            temp_bit=1<<(C_number%0x10);
            if(PLC_R_RAM_32BIT(C) < C_value) //把C当前值与目标值进行比较
            {
                p_C_enable_coil =plc_16BitBuf+COIL_C_ENABLE_ADDR/16+(C_number/0X10);
                if(!((*p_C_enable_coil&temp_bit)==temp_bit))
                    PLC_W_RAM_32BIT((C),PLC_R_RAM_32BIT(C)+1);
            }
//					if(PLC_R_RAM_32BIT(C) < C_value) // 比较溢出值
//							PLC_BIT_OFF(COIL_C_OVERFLOW_ADDR+C_number);// COIL_C_OVERFLOW_ADDR是C溢出线圈的起始地址
//					else
//							PLC_BIT_ON(COIL_C_OVERFLOW_ADDR+C_number);
        }
        if(PLC_R_RAM_32BIT(C) < C_value) // 比较溢出值
            PLC_BIT_OFF(COIL_C_OVERFLOW_ADDR+C_number);// COIL_C_OVERFLOW_ADDR是C溢出线圈的起始地址
        else
            PLC_BIT_ON(COIL_C_OVERFLOW_ADDR+C_number);
    }
    else
    {
        static uint16_t *p_data;
        p_data= plc_16BitBuf + HELON_REG_C_START_ADDR + C_number; // C当前值

        temp_bit=1<<(C_number%0x10);
        if(*p_data < C_value)
        {
            p_C_enable_coil=plc_16BitBuf+COIL_C_ENABLE_ADDR/16+(C_number/0X10);//0x0270是C使能线圈字节地址
            if(!((*p_C_enable_coil&temp_bit)==temp_bit))
                *p_data+=1;
        }
        if(*p_data < C_value) //比较溢出值
            PLC_BIT_OFF(COIL_C_OVERFLOW_ADDR+C_number); // COIL_C_OVERFLOW_ADDR是C溢出线圈的起始地址
        else
            PLC_BIT_ON(COIL_C_OVERFLOW_ADDR+C_number);
    }
    OUT(COIL_C_ENABLE_ADDR+(uint8_t)C_number); // COIL_C_ENABLE_ADDR是C复位线圈的起始地址
}

// kevin，20170419修复，C200以上死机的问题
static void enable_C_D(void) // 用寄存器D进行赋值
{
    static uint16_t *p_data;
    uint16_t temp_bit,*p_C_enable_coil;
    uint32_t C;
    int32_t temp2;
    temp2=C_value;
    C_value=plc_16BitBuf[HELON_REG_D_START_ADDR + temp2]; // 得到C设定值

    if(C_number >= 200) //判断是不是C200以上的 寄存器
    {
        C_value += plc_16BitBuf[HELON_REG_D_START_ADDR + temp2 + 1] * 0x10000;
        C = RAM_C200_ADDR + (C_number-200)*4;
        temp_bit =1<<(C_number % 0x10);


        // kevin，20170419修复，C200以上死机问题
        //	M8200	  0X0F00+200

        //if(PLC_R_RAM_32BIT(C) < C_value) //把C当前值与目标值进行比较
        {
            //TODO 0x270是固定数，应该为地址
            p_C_enable_coil=plc_16BitBuf + COIL_C_ENABLE_ADDR/16 + (C_number/0X10);
            if(!((*p_C_enable_coil & temp_bit) == temp_bit))
                if(PLC_BIT_TEST(M8200+C_number-200))
//                    PLC_RAM32(C)-=1;
                    PLC_W_RAM_32BIT((C),PLC_R_RAM_32BIT(C)-1);
                else
                if(PLC_R_RAM_32BIT(C) < C_value)
                    PLC_W_RAM_32BIT((C),PLC_R_RAM_32BIT(C)+1);
//                    PLC_RAM32(C)+=1;//把C当前值与目标值进行比较

        }


        if(PLC_R_RAM_32BIT(C) < C_value) //把C当前值与目标值进行比较
            PLC_BIT_OFF(COIL_C_OVERFLOW_ADDR + C_number);	// COIL_C_OVERFLOW_ADDR是C溢出线圈的起始位地址
        else
            PLC_BIT_ON(COIL_C_OVERFLOW_ADDR + C_number);
        PLC_Addr+=2;
    }
    else
    {
        p_data= plc_16BitBuf + HELON_REG_C_START_ADDR + C_number; // 当前值
        temp_bit=1<<(C_number%0x10);
        if(*p_data<C_value) //把C当前值与目标值进行比较
        {
            p_C_enable_coil=plc_16BitBuf+COIL_C_ENABLE_ADDR/16+(C_number/0X10); //比较enable coil
            if(!((*p_C_enable_coil&temp_bit)==temp_bit))
                *p_data+=1;
        }
        if(*p_data<=C_value)  //比较溢出值
            PLC_BIT_OFF(COIL_C_OVERFLOW_ADDR+C_number);// COIL_C_OVERFLOW_ADDR是C溢出线圈的起始位地址
        else
            PLC_BIT_ON(COIL_C_OVERFLOW_ADDR+C_number);
    }
    OUT(COIL_C_ENABLE_ADDR + (uint8_t)C_number); // COIL_C_ENABLE_ADDR是C复位线圈的起始位地址
}

static void disable_C_K(void)
{
    uint32_t C;
    static uint16_t *p_data;

    C_value=*PLC_Addr%0x100; //赋低8位值
    PLC_Addr++;
    C_value+=(*PLC_Addr%0x100)*0x100; //赋高8位值

    if(C_number>=200) //判断是不是C200以上的 寄存器
    {


        PLC_Addr++;
        C_value +=(*PLC_Addr%0x100)*0x10000; //赋低8位值 // kevin，20170426修复=改为+=
        PLC_Addr++;
        C_value +=(*PLC_Addr%0x100)*0x1000000;//赋高8位值

        C= RAM_C200_ADDR + (C_number - 200) *4;

        if(PLC_R_RAM_32BIT(C) < C_value) //把C当前值与目标值进行比较
            PLC_BIT_OFF(COIL_C_OVERFLOW_ADDR+C_number); // COIL_C_OVERFLOW_ADDR是C溢出线圈的起始位地址
        else
            PLC_BIT_ON(COIL_C_OVERFLOW_ADDR+C_number);
    }
    else
    {
        p_data= plc_16BitBuf + HELON_REG_C_START_ADDR + C_number;
        if(*p_data < C_value) //比较溢出值
            PLC_BIT_OFF(COIL_C_OVERFLOW_ADDR+C_number);
        else
            PLC_BIT_ON(COIL_C_OVERFLOW_ADDR+C_number);
    }
    OUT(COIL_C_ENABLE_ADDR+(uint8_t)C_number); // COIL_C_ENABLE_ADDR是C复位线圈的起始位地址
}

static void disable_C_D(void)	//关闭计数器C
{
    uint32_t C;
    static uint16_t *p_data;
    int32_t temp;
    if(C_number >= 200) //判断是不是C200以上的 寄存器
    {   temp = C_value;
        C_value=plc_16BitBuf[HELON_REG_D_START_ADDR + temp];
        C_value+= plc_16BitBuf[HELON_REG_D_START_ADDR + temp + 1] * 0x10000;

        C =RAM_C200_ADDR + (C_number - 200) *4;

        if(PLC_R_RAM_32BIT(C) < C_value) //比较溢出值
            PLC_BIT_OFF(COIL_C_OVERFLOW_ADDR+C_number);	// COIL_C_OVERFLOW_ADDR是C溢出线圈的起始位地址
        else
            PLC_BIT_ON(COIL_C_OVERFLOW_ADDR+C_number);
        PLC_Addr+=2;

    }
    else
    {
        C_value=plc_16BitBuf[HELON_REG_D_START_ADDR + C_value];
        p_data= plc_16BitBuf + HELON_REG_C_START_ADDR + C_number;
        if(*p_data<C_value)            //比较溢出值
            PLC_BIT_OFF(COIL_C_OVERFLOW_ADDR+C_number);
        else
            PLC_BIT_ON(COIL_C_OVERFLOW_ADDR+C_number);
    }
    OUT(COIL_C_ENABLE_ADDR+(uint8_t)C_number); // COIL_C_ENABLE_ADDR是C复位线圈的起始位地址
}

static void C_given_value_K(void)	//程序中以K来设定值
{
    if((PLC_ACC_BIT&0X01)==0X01)
        enable_C_K(); //开启计数器
    else
        disable_C_K();
}

static void C_given_value_D(void)	//程序中以D来设定值
{
    C_value=(*PLC_Addr%0x100)/2;
    PLC_Addr++;
    switch(*PLC_Addr/0x100)
    {
        case 0x86:
            C_value+=(*PLC_Addr%0x100)*0x80;
            break;
        case 0x88:
            C_value+=(*PLC_Addr%0x100)*0x80+1000;
            break;
    }
    if(PLC_ACC_BIT&0X01)
        enable_C_D();
    else
        disable_C_D();
}

static void operation_C()
{

    C_number=*PLC_Addr; //将操作计数器的号码送入
    PLC_Addr++;	//下一个功能取是K赋值还是D赋值

    switch(*PLC_Addr/0x100)
    {
        case 0x80:
            C_given_value_K();
            break;  //进行K赋值操作
        case 0x86:
            C_given_value_D();
            break;  //进行D赋值操作
    }
}

//=======================================================================================================
// 函数名称:  static void ASCI(void)
// 功能描述： HEX转ASCII
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2019年4月1日
// 备  注:  kevin
//=======================================================================================================
static void _ASCI(void) // 7步
{
    uint32_t addr,addr1;
    uint16_t n,j;
    uint8_t temp;
    addr = addr_value_prog(); //取S地址
    addr1 = addr_value_prog();//取D地址
    n = cos_value();          //取操作数n
    addr+= n>2?n/2+n%2-1:0;
    if(PLC_BIT_TEST(M8161))//M8161为0 16位转换模式  为 8 位转换模式
    {
        for(j=0; j<n; j++)
        {
            temp = j%2?PLC_R_RAM_8BIT(addr-j/2)&0x0f:PLC_R_RAM_8BIT(addr-j/2)>>4;
            PLC_W_RAM_16BIT((addr1+j*2) ,( temp<10?temp+0x30:temp-10+'A'));
        }
    }
    else//16位模式
    {
        for(j=0; j<n; j++)
        {
            temp = j%2?PLC_R_RAM_8BIT(addr-j/2)&0x0f:PLC_R_RAM_8BIT(addr-j/2)>>4;
            PLC_W_RAM_8BIT((addr1+j) ,( temp<10?temp+0x30:temp-10+'A'));
        }
    }
}
static void ASCI(void) // 7步
{
    if (PLC_ACC_BIT & 0x01)_ASCI();
    else
        PLC_Addr += 6;
}
static void ASCIP(void) // 7步
{
    if (PLC_LDP_TEST())_ASCI();
    else
        PLC_Addr += 6;
}

// kevin，2019年11月优化、新增
// 
//=======================================================================================================
// 函数名称:  static void HEX(void)
// 功能描述： ASCII转HEX
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2019年4月1日
// 备  注:  kevin
//=======================================================================================================
static void _HEX(void) // 7步
{
    uint32_t addr,addr1;
    uint16_t n,j;
    uint16_t temp;
    uint8_t temp8;
    addr = addr_value_prog(); //取S地址
    addr1 = addr_value_prog();//取D地址
    n = cos_value();          //取操作数n
    for(j=0; j<n; j++) PLC_W_RAM_8BIT((addr1+j/2) , 0);
    if(PLC_BIT_TEST(M8161))//M8161为0 16位转换模式  为 8 位转换模式
    {
        addr+=  (n-1)*2;
        for(j=0; j<n; j++)
        {
            temp = (PLC_R_RAM_16BIT(addr-j*2)&0x00ff);
            temp -= temp<'A'?0x30:'A'-10;
            temp8 = PLC_R_RAM_8BIT(addr1+j/2);
            PLC_W_RAM_8BIT((addr1+j/2),temp8 | (temp<<((j%2)*4)));
//            PLC_RAM8(addr1+j/2) |= temp<<((j%2)*4);
        }
    }
    else//16位模式
    {
        addr+= n-1;
        for(j=0; j<n; j++)
        {
            temp = PLC_R_RAM_8BIT(addr-j);
//			if(temp<'A')
//			{
//				temp-=0x30;
//			}
//			else
//			{
//				temp=temp-'A'+10;
//			}
            temp -= temp<'A'?0x30:'A'-10;
            temp8 = PLC_R_RAM_8BIT(addr1+j/2);
            PLC_W_RAM_8BIT((addr1+j/2),temp8 | (temp<<((j%2)*4)));
//            PLC_RAM8(addr1+j/2) |= temp<<((j%2)*4);
        }
    }
}

static void HEX(void) // 7步
{
    if (PLC_ACC_BIT & 0x01)_HEX();
    else
        PLC_Addr += 6;
}

static void HEXP(void) // 7步
{
    if (PLC_LDP_TEST())_HEX();
    else
        PLC_Addr += 6;
}

// kevin，2019年11月优化、新增
// 
static void _CCD(void) // 7步
{
    uint32_t addr;
    uint16_t n,j;
    uint16_t temp=0,temp1;
    addr = addr_value_prog(); //取S地址
    PLC_Addr += 2;
    n = cos_value();          //取操作数n
    PLC_Addr -= 4;
    if(PLC_BIT_TEST(M8161))//M8161为0 16位转换模式  为 8 位转换模式
    {
        for(j=0; j<n; j++)
        {
            temp+=PLC_R_RAM_16BIT(addr+j); //和校验
        }
        temp1 = PLC_R_RAM_16BIT(addr);
        for(j=1; j<n; j++)
        {
            temp1^=PLC_R_RAM_16BIT(addr+j); //异或
        }
        trade = (temp1<<8)+(temp&0x00ff);
        target();
    }
    else//16位模式
    {
        for(j=0; j<n; j++)
        {
            temp+=PLC_R_RAM_8BIT(addr+j); //和校验
        }
        temp1 = PLC_R_RAM_8BIT(addr);
        for(j=1; j<n; j++)
        {
            temp1^=PLC_R_RAM_8BIT(addr+j); //异或
        }
        trade = (temp1<<8)+temp;
        target();
    }
    PLC_Addr += 2;
}

static void CCD(void) // 7步
{
    if (PLC_ACC_BIT & 0x01)_CCD();
    else
        PLC_Addr += 6;
}

static void CCDP(void) // 7步
{
    if (PLC_LDP_TEST())_CCD();
    else
        PLC_Addr += 6;
}


static void VRRD(void) //VRRD 20190415
{
    uint16_t s;
    if (PLC_ACC_BIT & 0x01)
    {
        s = cos_value();
        s&=0x0007;
        //trade = plc_16BitBuf[HELON_REG_D_START_ADDR + 6030 + s]>>4;
        trade =plc_16BitBuf[0x0C26+s];
        target();
    }
    else PLC_Addr += 4;
}

static void VRRDP(void) //VRRD 20190415
{
    uint16_t s;
    if (PLC_LDP_TEST())
    {
        s = cos_value();
        s&=0x0007;
        //trade = plc_16BitBuf[HELON_REG_D_START_ADDR + 6030 + s]>>4;
        trade =plc_16BitBuf[0x0C26+s];
        target();
    }
    else PLC_Addr += 4;
}
static void VRSC(void) //VRSC 20190415
{
    uint16_t s;
    if (PLC_ACC_BIT & 0x01)
    {
        s = cos_value();
        s&=0x0007;
        trade = (plc_16BitBuf[HELON_REG_D_START_ADDR + 6030 + s] / 16 * 10 + 125) / 255;
        target();
    }
    else PLC_Addr += 4;
}
static void VRSCP(void) //VRSC 20190415
{
    uint16_t s;
    if (PLC_LDP_TEST())
    {
        s = cos_value();
        s&=0x0007;
        trade = (plc_16BitBuf[HELON_REG_D_START_ADDR + 6030 + s] / 16 * 10 + 125) / 255;
        target();
    }
    else PLC_Addr += 4;
}

// kevin，2019年11月优化、新增
// 
//=======================================================================================================
// 函数名称:  DECO
// 功能描述： 译码
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2018年4月23日
// 备  注:  kevin
//=======================================================================================================
static void _DECO()
{
    int16_t sx;
    uint16_t nx;
//			  sx = cos_value();  // 要译码的数据
//        dx = addr_value(); // 保存译码结果的位或者字软元件编号
    PLC_Addr += 4;
    nx = cos_value(); // 保存译码结果的软元件的位点数（n=1~8，n=0时不处理）
    PLC_Addr -= 6;
    sx =Special_cos_value(nx); // 要译码的数据
    /* n=1~8，n=0时不处理 */
    if((1 <= nx) && (nx <=8))
    {
        trade = 0x0001 << (sx & DE[nx]);
        SpecialTarget(); // 保存译码结果的位或者字软元件编号
        PLC_Addr += 2;
    }
    else
    {
        PLC_Addr += 4;
    }
}

static void DECO()
{
    if(PLC_ACC_BIT & 0x01)
    {
        _DECO();
    }
    else {
        PLC_Addr += 6;
    }
}

static void DECOP()
{
    if(PLC_LDP_TEST())
    {
        _DECO();
    }
    else {
        PLC_Addr += 6;
    }
}

//=======================================================================================================
// 函数名称:  ENCO
// 功能描述： 编码
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2018年4月23日
// 备  注:  kevin
//=======================================================================================================
static void _ENCO()
{
    int16_t sx;
    uint16_t nx;
    uint8_t i;

    PLC_Addr += 4;
    nx = cos_value();
    PLC_Addr -= 6;
    sx =Special_cos_value(nx); // 要译码的数据
    /* n=1~8，n=0时不处理 */
    if((1 <= nx) && (nx <=8))
    {
        for(i =0; i < 8; i++)
        {
            if(sx & (0x0001 << i))
            {
                trade = i & DE[nx];
                SpecialTarget(); // 编码的数据
                break;
            }
        }
        PLC_Addr += 2;
    }
    else
    {
        PLC_Addr += 4;
    }
}

static void ENCO()
{
    if (PLC_ACC_BIT & 0x01)_ENCO();
    else {
        PLC_Addr += 6;
    }
}

static void ENCOP()
{
    if (PLC_LDP_TEST())_ENCO();
    else {
        PLC_Addr += 6;
    }
}

// kevin，2019年11月优化、新增
// 
//=======================================================================================================
// 函数名称:  ENCO
// 功能描述： 七段码译码
// 输　入:  void
// 输　出:  void
// 全局变量:
// 调用模块:
// 作　者:  kevin
// 日　期:  2018年4月23日
// 备  注:  kevin
//=======================================================================================================
static void SEGD(void)
{
    if (PLC_ACC_BIT & 0x01)
    {
        uint8_t sx;
        sx = (uint8_t)(cos_value() & 0x000F); // 低4位
        trade = SEGD_LED[sx];
        target();
    }
    else
    {
        PLC_Addr += 4;
    }
}

static void SEGDP(void)
{
    if (PLC_LDP_TEST())
    {
        uint8_t sx;
        sx = (uint8_t)(cos_value() & 0x000F); // 低4位
        trade = SEGD_LED[sx];
        target();
    }
    else
    {
        PLC_Addr += 4;
    }
}
//////////////////////////////////////////////////////////////////////////////////////
// kevin，2019年11月优化、新增
// 16bit的指令
static void FNC_AppInstruct(void)
{
    switch(*PLC_Addr)
    {
        case 0x0002:
            PLC_Addr++,expand_OUT();
            break;  //M1535以上的指令
        case 0x0003:
            PLC_Addr++,expand_SET();
            break;  //M1535以上的指令
        case 0x0004:
            PLC_Addr++,expand_RST();
            break;  //M1535以上的指令

        case 0x0005:
            PLC_Addr++,expand_OUT();
            break;
        case 0x0006:
            PLC_Addr++,expand_SET();
            break;
        case 0x0007:
            PLC_Addr++,expand_RST();
            break;
        case 0x0008:
            PLC_Addr++,PLS();
            break;
        case 0x0009:
            PLC_Addr++,PLF();
            break;

//warning "kevin，2017年03月20日新增，MC和MCR指令";
        case 0x000A:
            PLC_Addr++,Func_MC();
            break;
        case 0x000B:
            PLC_Addr++,Func_MCR();
            break;

        case 0x000C:
            PLC_Addr++,RST_T_C();
            break;  //执行RST C&T
        case 0x000D:
            PLC_Addr++,RST_D();
            break;  //执行D寄存器复位

        case 0x0010:
            PLC_Addr++,CJ();
            break;  //CJ
        case 0x1010:
            PLC_Addr++,CJP();
            break;  //CJP
        case 0x0012:
            PLC_Addr++,CALL();
            break;  //CALL
        case 0x1012:
            PLC_Addr++,CALLP();
            break;  //CALLP
        case 0x0014:
            PLC_Addr++,SRET();
            break;  //SRET
        case 0x001C:
            PLC_Addr=PLC_Addr;
            break;  //FEND
//		case 0x0016: PLC_Addr++,IRET();                    break;  //中断返回
//		case 0x0018: PLC_Addr++,EI();                      break;  //开中断
//		case 0x001A: PLC_Addr++,DI();                      break;  //关中断
        case 0x001E:
            PLC_Addr++,WDT();
            break;  //WDT
        case 0x101E:
            PLC_Addr++,WDTP();
            break;  //WDTP
        case 0X0020:
            PLC_Addr++,FOR();
            break;  //kevin 20160929新增，FOR循环
        case 0X0022:
            PLC_Addr++,FOR_NEXT();
            break;  //kevin 20160929新增，FOR_NEST 循环结束

        case 0X0024:
            PLC_Addr++,CMP();
            break;  //16位比较传送指令
        case 0X1024:
            PLC_Addr++,CMPP();
            break;  //16位上升沿比较传送指令
        case 0X0025:
            PLC_Addr++,DCMP();
            break;  //32位比较传送指令
        case 0X1025:
            PLC_Addr++,DCMPP();
            break;  //32位上升沿比较传送指令
        case 0X0026:
            PLC_Addr++,ZCP();
            break;  //16位区间值比较传送指令
        case 0X1026:
            PLC_Addr++,ZCPP();
            break;  //16位区间值比较传送指令
        case 0X0027:
            PLC_Addr++,DZCP();
            break;  //32位区间值比较传送指令
        case 0X1027:
            PLC_Addr++,DZCPP();
            break;  //32位区间值比较传送指令 kevin，2019年11月优化、新增
        case 0x0028:
            PLC_Addr++,MOV();
            break;  //执行16bit传送指令
        case 0x1028:
            PLC_Addr++,MOVP();
            break;  //执行16bit传送指令 kevin，2019年11月优化、新增
        case 0X0029:
            PLC_Addr++,DMOV();
            break;  //DMOV
        case 0X1029:
            PLC_Addr++,DMOVP();
            break;  //DMOV kevin，2019年11月优化、新增
        case 0X002A:
            PLC_Addr++,SMOV();
            break;  //SMOV
        case 0X102A:
            PLC_Addr++,SMOVP();
            break;  //SMOV kevin，2019年11月优化、新增
        case 0X002C:
            PLC_Addr++,CML();
            break;  //CML取反指令
        case 0X102C:
            PLC_Addr++,CMLP();
            break;  //CML取反指令 kevin，2019年11月优化、新增
        case 0X002D:
            PLC_Addr++,DCML();
            break;  //DCML取反指令
        case 0X102D:
            PLC_Addr++,DCMLP();
            break;  //DCML取反指令 kevin，2019年11月优化、新增
        case 0X002E:
            PLC_Addr++,BMOV();
            break;  //成批传送
        case 0X102E:
            PLC_Addr++,BMOVP();
            break;  //成批传送 kevin，2019年11月优化、新增
        case 0X0030:
            PLC_Addr++,FMOV();
            break;  //多点传送
        case 0X1030:
            PLC_Addr++,FMOVP();
            break;  //多点传送 kevin，2019年11月优化、新增
        case 0X0031:
            PLC_Addr++,DFMOV();
            break;  //32位多点传送
        case 0X1031:
            PLC_Addr++,DFMOVP();
            break;  //32位多点传送 kevin，2019年11月优化、新增
        case 0X0032:
            PLC_Addr++,XCH();
            break;  //交换传送
        case 0X1032:
            PLC_Addr++,XCHP();
            break;  //交换传送  kevin，2019年11月优化、新增
        case 0X0033:
            PLC_Addr++,DXCH();
            break;  //32位交换传送
        case 0X1033:
            PLC_Addr++,DXCHP();
            break;  //32位交换传送 kevin，2019年11月优化、新增
        case 0X0034:
            PLC_Addr++,BCD();
            break;  //二进制转换BCD
        case 0X1034:
            PLC_Addr++,BCDP();
            break;  //二进制转换BCD kevin，2019年11月优化、新增
        case 0X0035:
            PLC_Addr++,DBCD();
            break;  //二进制转换DBCD
        case 0X1035:
            PLC_Addr++,DBCDP();
            break;  //二进制转换DBCD kevin，2019年11月优化、新增
        case 0X0036:
            PLC_Addr++,BIN();
            break;  //二进制转换BIN
        case 0X1036:
            PLC_Addr++,BINP();
            break;  //二进制转换BIN kevin，2019年11月优化、新增
        case 0X0037:
            PLC_Addr++,DBIN();
            break;  //二进制转换DBIN
        case 0X1037:
            PLC_Addr++,DBINP();
            break;  //二进制转换DBIN kevin，2019年11月优化、新增

        case 0X0038:
            PLC_Addr++,ADD();
            break;  //加法指令
        case 0X1038:
            PLC_Addr++,ADDP();
            break;  //上升沿加法指令 kevin，2019年11月优化、新增
        case 0x0039:
            PLC_Addr++,DADD();
            break;  //DADD加法运算
        case 0x1039:
            PLC_Addr++,DADDP();
            break;  //DADD上升沿加法运算	kevin，2019年11月优化、新增
        case 0X003A:
            PLC_Addr++,SUB();
            break;  //减法指令
        case 0X103A:
            PLC_Addr++,SUBP();
            break;  //上升沿减法指令 kevin，2019年11月优化、新增
        case 0x003B:
            PLC_Addr++,DSUB();
            break;  //DSUB减法运算
        case 0x103B:
            PLC_Addr++,DSUBP();
            break;  //DSUB上升沿减法运算 kevin，2019年11月优化、新增
        case 0x003C:
            PLC_Addr++,MUL();
            break;  //MUL 乘法指令
        case 0x103C:
            PLC_Addr++,MULP();
            break;  //MUL 乘法指令 kevin，2019年11月优化、新增
        case 0x003D:
            PLC_Addr++,DMUL();
            break;  //DMUL乘法运算
        case 0x103D:
            PLC_Addr++,DMULP();
            break;  //DMUL乘法运算 kevin，2019年11月优化、新增
        case 0x003E:
            PLC_Addr++,DIV();
            break;  //DIV 乘法指令
        case 0x103E:
            PLC_Addr++,DIVP();
            break;  //DIV 乘法指令 kevin，2019年11月优化、新增
        case 0x003F:
            PLC_Addr++,DDIV();
            break;  //DDIV除法运算
        case 0x103F:
            PLC_Addr++,DDIVP();
            break;  //DDIV除法运算 kevin，2019年11月优化、新增
        case 0x0040:
            PLC_Addr++,INC();
            break;  //16位逻辑运算加1指令
        case 0x1040:
            PLC_Addr++,INCP();
            break;  //16位上升沿逻辑运算加1指令
        case 0x0041:
            PLC_Addr++,DINC();
            break;  //32位逻辑运算加1指令
        case 0x1041:
            PLC_Addr++,DINCP();
            break;  //32位上升沿逻辑运算加1指令
        case 0x0042:
            PLC_Addr++,DEC();
            break;  //16位逻辑运算减1指令
        case 0x1042:
            PLC_Addr++,DECP();
            break;  //16位上升沿逻辑运算减1指令
        case 0x0043:
            PLC_Addr++,DDEC();
            break;  //32位逻辑运算减1指令
        case 0x1043:
            PLC_Addr++,DDECP();
            break;  //32位上升沿逻辑运算减1指令 kevin，2019年11月优化、新增
        case 0x0044:
            PLC_Addr++,WAND();
            break;  //逻辑运算与逻辑
        case 0x1044:
            PLC_Addr++,WANDP();
            break;  //逻辑运算与逻辑 kevin，2019年11月优化、新增
        case 0x0045:
            PLC_Addr++,DWAND();
            break;  //32位逻辑运算与逻辑
        case 0x1045:
            PLC_Addr++,DWANDP();
            break;  //32位逻辑运算与逻辑 kevin，2019年11月优化、新增
        case 0x0046:
            PLC_Addr++,WOR();
            break;  //逻辑运算或逻辑
        case 0x1046:
            PLC_Addr++,WORP();
            break;  //逻辑运算或逻辑 kevin，2019年11月优化、新增
        case 0x0047:
            PLC_Addr++,DWOR();
            break;  //32位逻辑运算或逻辑
        case 0x1047:
            PLC_Addr++,DWORP();
            break;  //32位逻辑运算或逻辑 kevin，2019年11月优化、新增
        case 0x0048:
            PLC_Addr++,WXOR();
            break;  //逻辑运算异或逻辑
        case 0x1048:
            PLC_Addr++,WXORP();
            break;  //逻辑运算异或逻辑 kevin，2019年11月优化、新增
        case 0x0049:
            PLC_Addr++,DWXOR();
            break;  //32位逻辑运算异或逻辑
        case 0x1049:
            PLC_Addr++,DWXORP();
            break;  //32位逻辑运算异或逻辑 kevin，2019年11月优化、新增
        case 0x004A:
            PLC_Addr++,NEG();
            break;  //逻辑运算取负数
        case 0x104A:
            PLC_Addr++,NEGP();
            break;  //逻辑运算取负数 kevin，2019年11月优化、新增
        case 0x004B:
            PLC_Addr++,DNEG();
            break;  //32位逻辑运算取负数
        case 0x104B:
            PLC_Addr++,DNEGP();
            break;  //32位逻辑运算取负数 kevin，2019年11月优化、新增

            /* 循环移位指令 */
        case 0x004C:
            PLC_Addr++,ROR();
            break;  //ROR
        case 0x104C:
            PLC_Addr++,RORP();
            break;  //RORP 2019年11月优化、新增 
        case 0x004D:
            PLC_Addr++,DROR();
            break;  //DROR
        case 0x104D:
            PLC_Addr++,DRORP();
            break;  //DRORP 2019年11月优化、新增 
        case 0x004E:
            PLC_Addr++,ROL();
            break;  //ROL
        case 0x104E:
            PLC_Addr++,ROLP();
            break;  //ROLP 2019年11月优化、新增 
        case 0x004F:
            PLC_Addr++,DROL();
            break;  //DROL
        case 0x104F:
            PLC_Addr++,DROLP();
            break;  //DROLP 2019年11月优化、新增 
        case 0x0050:
            PLC_Addr++,RCR();
            break;  //RCR
        case 0x1050:
            PLC_Addr++,RCRP();
            break;  //RCRP 2019年11月优化、新增 
        case 0x0051:
            PLC_Addr++,DRCR();
            break;  //DRCR
        case 0x1051:
            PLC_Addr++,DRCRP();
            break;  //DRCRP 2019年11月优化、新增 
        case 0x0052:
            PLC_Addr++,RCL();
            break;  //RCL
        case 0x1052:
            PLC_Addr++,RCLP();
            break;  //RCLP 2019年11月优化、新增 
        case 0x0053:
            PLC_Addr++,DRCL();
            break;  //DRCL
        case 0x1053:
            PLC_Addr++,DRCLP();
            break;  //DRCLP 2019年11月优化、新增 
        case 0x0054:
            PLC_Addr++,SFTR();
            break;  //SFTR 位左移 2019年11月优化、新增 https:
        case 0x1054:
            PLC_Addr++,SFTRP();
            break;  //SFTRP 位左移 2019年11月优化、新增 
        case 0x0056:
            PLC_Addr++,SFTL();
            break;  //SFTL 位右移 2019年11月优化、新增 
        case 0x1056:
            PLC_Addr++,SFTLP();
            break;  //SFTLP 位右移 2019年11月优化、新增 
        case 0x0058:
            PLC_Addr++,WSFR();
            break;  //WSFR 字左移 2019年11月优化、新增 
        case 0x1058:
            PLC_Addr++,WSFRP();
            break;  //WSFRP 字左移 2019年11月优化、新增 
        case 0x005A:
            PLC_Addr++,WSFL();
            break;  //WSFL 字右移 2019年11月优化、新增 
        case 0x105A:
            PLC_Addr++,WSFLP();
            break;  //WSFLP 字右移 2019年11月优化、新增 
        case 0x005C:
            PLC_Addr++,SFWR();
            break;  //SFWR FIFO写入 2019年11月优化、新增 
        case 0x105C:
            PLC_Addr++,SFWRP();
            break;  //SFWRP FIFO写入 2019年11月优化、新增 
        case 0x005E:
            PLC_Addr++,SFRD();
            break;  //SFRD FIFO读出 2019年11月优化、新增 
        case 0x105E:
            PLC_Addr++,SFRDP();
            break;  //SFRDP FIFO读出 2019年11月优化、新增 

        case 0x0060:
            PLC_Addr++,ZRST();
            break;
        case 0x1060:
            PLC_Addr++,ZRSTP();
            break; //2019年11月优化、新增 
        case 0x0062:
            PLC_Addr++,DECO();
            break; //DECO 译码， 20180423新增
        case 0x1062:
            PLC_Addr++,DECOP();
            break; //DECOP 译码，2019年11月优化、新增 
        case 0x0064:
            PLC_Addr++,ENCO();
            break; //ENCO 编码，20180423新增
        case 0x1064:
            PLC_Addr++,ENCOP();
            break; //ENCO 编码， 2019年11月优化、新增 
        case 0x0066:
            PLC_Addr++,SUM();
            break; //统计NO位数   2019年11月优化、新增 
        case 0x1066:
            PLC_Addr++,SUMP();
            break; //统计NO位数   2019年11月优化、新增 
        case 0x0067:
            PLC_Addr++,DSUM();
            break; //统计NO位数	   2019年11月优化、新增 
        case 0x1067:
            PLC_Addr++,DSUMP();
            break; //统计NO位数32  2019年11月优化、新增 
        case 0x0068:
            PLC_Addr++,BON();
            break; //查询某位状态  2019年11月优化、新增 
        case 0x1068:
            PLC_Addr++,BONP();
            break; //查询某位状态  2019年11月优化、新增 
        case 0x0069:
            PLC_Addr++,DBON();
            break; //查询某位状态	 2019年11月优化、新增 
        case 0x1069:
            PLC_Addr++,DBONP();
            break; //查询某位状态  2019年11月优化、新增 

        case 0x006A:
            PLC_Addr++,MEAN();
            break;	 //MEAN，求平均值指令
        case 0x106A:
            PLC_Addr++,MEANP();
            break;	 //MEANP，求平均值指令 kevin，2019年11月优化、新增
        case 0x006B:
            PLC_Addr++,DMEAN();
            break;	 //DMEAN，32求平均值指令 kevin，2019年11月优化、新增
        case 0x106B:
            PLC_Addr++,DMEANP();
            break;	 //DMEANP，32求平均值指令 kevin，2019年11月优化、新增

        case 0x0070:
            PLC_Addr++,SQR();
            break;  //SQR16位整数开方
        case 0x1070:
            PLC_Addr++,SQRP();
            break;  //SQR16位整数开方 kevin，2019年11月优化、新增
        case 0x0071:
            PLC_Addr++,DSQR();
            break;  //SQR32位整数开方
        case 0x1071:
            PLC_Addr++,DSQRP();
            break;  //SQR32位整数开方 kevin，2019年11月优化、新增

        case 0x0072:
            PLC_Addr++,FLT();
            break;  //16位整数转浮点
        case 0x1072:
            PLC_Addr++,FLTP();
            break;  //16位整数转浮点 kevin，2019年11月优化、新增
        case 0x0073:
            PLC_Addr++,DFLT();
            break;  //32位整数转浮点
        case 0x1073:
            PLC_Addr++,DFLTP();
            break;  //32位整数转浮点 kevin，2019年11月优化、新增

        case 0x0076:
            PLC_Addr++,REFF();
            break;  //REFF
        case 0x0078:
            PLC_Addr++,MTR();
            break;  //MTR
        case 0x007A:
            PLC_Addr++,HSCS();
            break;  //高速计数置位  20160709
        case 0x0082:
            PLC_Addr++;
            break;  // 高速脉冲输出
        case 0x0083:
            PLC_Addr++;
            break;  // 高速脉冲输出 kevin，2019年11月优化、新增
        case 0x0084:
            PLC_Addr++;
            break;  // PWM输出 kevin，2019年11月优化、新增
//		case 0x0086: PLC_Addr++,PLSR();                    break;  // 高速脉冲输出
//		case 0x0087: PLC_Addr++,DPLSR();                    break;  // 高速脉冲输出

            //方便指令
//		case 0x0088: PLC_Addr++,IST();	                   break;  //IST
        case 0x008A:
            PLC_Addr++,SER();
            break;  //SER kevin，2019年11月优化、新增
        case 0x108A:
            PLC_Addr++,SERP();
            break;  //SERP kevin，2019年11月优化、新增
        case 0x008B:
            PLC_Addr++,DSER();
            break;  //DSER kevin，2019年11月优化、新增
        case 0x108B:
            PLC_Addr++,DSERP();
            break;  //DSERP kevin，2019年11月优化、新增
        case 0x008C:
            PLC_Addr++,ABSD();
            break;  //ABSD kevin，2019年11月优化、新增
        case 0x008D:
            PLC_Addr++,DABSD();
            break;  //DABSD kevin，2019年11月优化、新增
        case 0x008E:
            PLC_Addr++,INCD();
            break;  //INCD kevin，2019年11月优化、新增
        case 0x0090:
            PLC_Addr++,TTMR();
            break;  //TTMR kevin，2019年11月优化、新增
        case 0x0092:
            PLC_Addr++,STMR();
            break;  //STMR kevin，2019年11月优化、新增
        case 0x0094:
            PLC_Addr++,ALT();
            break;  //ALT
        case 0x1094:
            PLC_Addr++,ALTP();
            break;  //ALT kevin，2019年11月优化、新增
        case 0x0096:
            PLC_Addr++,RAMP();
            break;  //RAMP kevin，2019年11月优化、新增
//		case 0x0098: PLC_Addr++,ROTC();	                   break;  //ROTC
//		case 0x009A: PLC_Addr++,SORT();	                   break;  //SORT

        case 0x00A2:
            PLC_Addr++,SEGD();
            break;  //SEGD，kevin，20180423新增
        case 0x10A2:
            PLC_Addr++,SEGDP();
            break;  //SEGD，kevin，20180423新增 kevin，2019年11月优化、新增
            // kevin，20161128新增
        case 0x00B0:
            PLC_Addr++,RS();
            break;  // RS，kevin，20161128新增

        case 0x00B4:
            PLC_Addr++,ASCI();
            break;  //ASCI，kevin，20190401新增
        case 0x10B4:
            PLC_Addr++,ASCIP();
            break;  //ASCI，kevin，20190401新增 kevin，2019年11月优化、新增
        case 0x00B6:
            PLC_Addr++,HEX();
            break;  //HEX，kevin，20190401新增
        case 0x10B6:
            PLC_Addr++,HEXP();
            break;  //HEX，kevin，20190401新增 kevin，2019年11月优化、新增
        case 0x00B8:
            PLC_Addr++,CCD();
            break;  //CCD kevin，2019年11月优化、新增
        case 0x10B8:
            PLC_Addr++,CCDP();
            break;  //CCDP kevin，2019年11月优化、新增
        case 0x00BA:
            PLC_Addr++,VRRD();
            break;  //VRRD kevin，2019年11月优化、新增
        case 0x10BA:
            PLC_Addr++,VRRDP();
            break;  //VRRDP kevin，2019年11月优化、新增
        case 0x00BC:
            PLC_Addr++,VRSC();
            break;  //VRSC kevin，2019年11月优化、新增
        case 0x10BC:
            PLC_Addr++,VRSCP();
            break;  //VRSCP kevin，2019年11月优化、新增
        case 0x00C0:
            PLC_Addr++,PID();
            break;  //PID

        case 0x00ED:
            PLC_Addr++,DECMP();
            break;  //DECMP
        case 0x10ED:
            PLC_Addr++,DECMPP();
            break;  //DECMPP kevin，2019年11月优化、新增
        case 0x00EF:
            PLC_Addr++,DEZCP();
            break;  //DEZCP kevin，2019年11月优化、新增
        case 0x10EF:
            PLC_Addr++,DEZCPP();
            break;  //DEZCPP kevin，2019年11月优化、新增
        case 0x00F1:
            PLC_Addr++,DEMOV();
            break;  //3U指令
        case 0x00FD:
            PLC_Addr++,DEBCD();
            break;  //DEBCD   //二进制转十进制浮点 2019年11月优化、新增 
        case 0x10FD:
            PLC_Addr++,DEBCDP();
            break;  //DEBCD   //二进制转十进制浮点P 2019年11月优化、新增 
        case 0x00FF:
            PLC_Addr++,DEBIN();
            break;  //DEBIN //十进制转二进制浮点 2019年11月优化、新增 
        case 0x10FF:
            PLC_Addr++,DEBINP();
            break;  //DEBIN //十进制转二进制浮点P 2019年11月优化、新增 
        case 0x0101:
            PLC_Addr++,DEADD();
            break;  //浮点加法运算
        case 0x1101:
            PLC_Addr++,DEADDP();
            break;  //浮点加法运算 2019年11月优化、新增 
        case 0x0103:
            PLC_Addr++,DESUB();
            break;  //浮点减法运算
        case 0x1103:
            PLC_Addr++,DESUBP();
            break;  //浮点减法运算 2019年11月优化、新增 
        case 0x0107:
            PLC_Addr++,DEDIV();
            break;  //浮点乘法运算
        case 0x1107:
            PLC_Addr++,DEDIVP();
            break;  //浮点乘法运算 2019年11月优化、新增 
        case 0x0105:
            PLC_Addr++,DEMUL();
            break;  //浮点除法运算
        case 0x1105:
            PLC_Addr++,DEMULP();
            break;  //浮点除法运算 2019年11月优化、新增 
        case 0x010F:
            PLC_Addr++,DESQR();
            break;  //DESQR浮点开方
        case 0x110F:
            PLC_Addr++,DESQRP();
            break;  //DESQR浮点开方 2019年11月优化、新增 
        case 0x0112:
            PLC_Addr++,INT();
            break;  //INT
        case 0x1112:
            PLC_Addr++,INTP();
            break;  //INT  2019年11月优化、新增 
        case 0x0113:
            PLC_Addr++,DINT();
            break;  //DINT
        case 0x1113:
            PLC_Addr++,DINTP();
            break;  //DINT 2019年11月优化、新增 
        case 0x0115:
            PLC_Addr++,DSIN();
            break;  //DSIN
        case 0x1115:
            PLC_Addr++,DSINP();
            break;  //DSIN 2019年11月优化、新增 
        case 0x0117:
            PLC_Addr++,DCOS();
            break;  //DCOS
        case 0x1117:
            PLC_Addr++,DCOSP();
            break;  //DCOS 2019年11月优化、新增 
        case 0x0119:
            PLC_Addr++,DTAN();
            break;  //DTAN
        case 0x1119:
            PLC_Addr++,DTANP();
            break;  //DTAN 2019年11月优化、新增 

        case 0x0136:
            PLC_Addr++,SWAP();
            break;  //SWAP
        case 0x1136:
            PLC_Addr++,SWAPP();
            break;  //SWAP kevin，2019年11月优化、新增
        case 0x0137:
            PLC_Addr++,DSWAP();
            break;  //DSWAP
        case 0x1137:
            PLC_Addr++,DSWAPP();
            break;  //DSWAP kevin，2019年11月优化、新增
            //时钟运算
        case 0x0150:
            PLC_Addr++,TCMP();
            break;  //TCMP
        case 0x1150:
            PLC_Addr++,TCMPP();
            break;  //TCMP kevin，2019年11月优化、新增
        case 0x0152:
            PLC_Addr++,TZCP();
            break;  //TZCP
        case 0x1152:
            PLC_Addr++,TZCPP();
            break;  //TZCP kevin，2019年11月优化、新增
        case 0x0154:
            PLC_Addr++,TADD();
            break;  //TADD
        case 0x1154:
            PLC_Addr++,TADDP();
            break;  //TADD kevin，2019年11月优化、新增
        case 0x0156:
            PLC_Addr++,TSUB();
            break;  //TSUB
        case 0x1156:
            PLC_Addr++,TSUBP();
            break;  //TSUB kevin，2019年11月优化、新增
        case 0x015C:
            PLC_Addr++,TRD();
            break;  //TRD
        case 0x115C:
            PLC_Addr++,TRDP();
            break;  //TRD kevin，2019年11月优化、新增
        case 0x015E:
            PLC_Addr++,TWR();
            break;  //TWR
        case 0x115E:
            PLC_Addr++,TWRP();
            break;  //TWR kevin，2019年11月优化、新增
        case 0x0162:
            PLC_Addr++,HOUR();
            break;  //HOUR kevin，2019年11月优化、新增
        case 0x0163:
            PLC_Addr++,DHOUR();
            break;  //DHOUR kevin，2019年11月优化、新增
            //格雷码
        case 0x0164:
            PLC_Addr++,GRY();
            break;  //GRY
        case 0x0170:
            PLC_Addr++,RD3A();
            break;  //

        case 0x0172:
            PLC_Addr++,WR3A();
            break;  //

        case 0x1164:
            PLC_Addr++,GRYP();
            break;  //GRY kevin，2019年11月优化、新增
        case 0x0165:
            PLC_Addr++,DGRY();
            break;  //DGRY
        case 0x1165:
            PLC_Addr++,DGRYP();
            break;  //DGRY kevin，2019年11月优化、新增
        case 0x0166:
            PLC_Addr++,GBIN();
            break;  //GBIN
        case 0x1166:
            PLC_Addr++,GBINP();
            break;  //GBIN kevin，2019年11月优化、新增
        case 0x0167:
            PLC_Addr++,DGBIN();
            break;  //DGBIN
        case 0x1167:
            PLC_Addr++,DGBINP();
            break;  //DGBIN kevin，2019年11月优化、新增


        case 0x01C2:
            PLC_Addr++,expand_LD();
            break;  //M1535以上的指令
        case 0x01C3:
            PLC_Addr++,expand_LDI();
            break;  //
        case 0x01C4:
            PLC_Addr++,expand_AND();
            break;  //
        case 0x01C5:
            PLC_Addr++,expand_ANI();
            break;  //
        case 0x01C6:
            PLC_Addr++,expand_OR();
            break;  //
        case 0x01C7:
            PLC_Addr++,expand_ORI();
            break;  //

        case 0x01CA:
            PLC_Addr++,LDP();
            break;  //上升延处理程序
        case 0x01CB:
            PLC_Addr++,LDF();
            break;  //上升延处理程序
        case 0x01CC:
            PLC_Addr++,ANDP();
            break;  //上升延处理程序
        case 0x01CD:
            PLC_Addr++,ANDF();
            break;  //上升延处理程序
        case 0x01CE:
            PLC_Addr++,ORP();
            break;  //上升延处理程序
        case 0x01CF:
            PLC_Addr++,ORF();
            break;  //上升延处理程序


        case 0X01D0:
            PLC_Addr++,amount();
            break;  //LD 16位等于比较
        case 0X01D1:
            PLC_Addr++,Damount();
            break;  //LD 32位等于比较
        case 0X01D2:
            PLC_Addr++,big();
            break;  //LD 16位大于比较
        case 0X01D3:
            PLC_Addr++,Dbig();
            break;  //LD 32位大于比较
        case 0X01D4:
            PLC_Addr++,less();
            break;  //LD 16位小于比较
        case 0X01D5:
            PLC_Addr++,Dless();
            break;  //LD 32位小于比较
        case 0X01D8:
            PLC_Addr++,no_amount();
            break;  //LD 16位不等于比较指令
        case 0X01D9:
            PLC_Addr++,Dno_amount();
            break;  //LD 32位不等于比较指令
        case 0X01DA:
            PLC_Addr++,less_amount();
            break;  //LD 16位小于等于比较
        case 0X01DB:
            PLC_Addr++,Dless_amount();
            break;  //LD 32位小于等于比较
        case 0X01DC:
            PLC_Addr++,big_amount();
            break;  //LD 16位大于等于比较
        case 0X01DD:
            PLC_Addr++,Dbig_amount();
            break;  //LD 32位大于等于比较

        case 0X01E0:
            PLC_Addr++,amount_and();
            break;  //LD AND 16位等于比较
        case 0X01E1:
            PLC_Addr++,Damount_and();
            break;  //LD AND 32位等于比较
        case 0X01E2:
            PLC_Addr++,big_and();
            break;  //LD AND 16位大于比较
        case 0X01E3:
            PLC_Addr++,Dbig_and();
            break;  //LD AND 32位大于比较
        case 0X01E4:
            PLC_Addr++,less_and();
            break;  //LD AND 16位小于比较
        case 0X01E5:
            PLC_Addr++,Dless_and();
            break;  //LD AND 32位小于比较
        case 0X01E8:
            PLC_Addr++,no_amount_and();
            break;  //LD 16位不等于比较指令
        case 0X01E9:
            PLC_Addr++,Dno_amount_and();
            break;  //LD 32位不等于比较指令
        case 0X01EA:
            PLC_Addr++,less_amount_and();
            break;  //LD AND 16位小于等于比较
        case 0X01EB:
            PLC_Addr++,Dless_amount_and();
            break;  //LD AND 32位小于等于比较
        case 0X01EC:
            PLC_Addr++,big_amount_and();
            break;  //LD AND 16位大于等于比较
        case 0X01ED:
            PLC_Addr++,Dbig_amount_and();
            break;  //LD AND 32位大于等于比较
        case 0X01F0:
            PLC_Addr++,amount_OR();
            break;  //LD OR 16位等于比较
        case 0X01F1:
            PLC_Addr++,Damount_OR();
            break;  //LD OR 32位等于比较
        case 0X01F2:
            PLC_Addr++,big_OR();
            break;  //LD OR 16位大于比较
        case 0X01F3:
            PLC_Addr++,Dbig_OR();
            break;  //LD OR 32位大于比较
        case 0X01F4:
            PLC_Addr++,less_OR();
            break;  //LD OR 16位小于比较
        case 0X01F5:
            PLC_Addr++,Dless_OR();
            break;  //LD OR 32位小于比较
        case 0X01F8:
            PLC_Addr++,no_amount_OR();
            break;  //LD 16位不等于比较指令
        case 0X01F9:
            PLC_Addr++,Dno_amount_OR();
            break;  //LD 32位不等于比较指令
        case 0X01FA:
            PLC_Addr++,less_amount_OR();
            break;  //LD OR 16位小于等于比较
        case 0X01FB:
            PLC_Addr++,Dless_amount_OR();
            break;  //LD OR 32位小于等于比较
        case 0X01FC:
            PLC_Addr++,big_amount_OR();
            break;  //LD OR 16位大于等于比较
        case 0X01FD:
            PLC_Addr++,Dbig_amount_OR();
            break;  //LD OR 32位大于等于比较

        case 0x000F:
            PLC_Addr=PLC_Addr;
            break;  //如果遇到END指令则使后面结束
        case 0XF7FF:
            PLC_Addr++,RET();
            break;  //RET

            /*2018年12月23日新增 */
        case 0x0238:
            PLC_Addr++,ADPRW();
            break;  // Modbus主机指令


//	  case 0x00B2: PLC_Addr++,PRUN();	                 break;  //PRUN八进制位传送
        default:
            PLC_PROG_ERROR(M8065,02);
            PLC_Addr++;
            break;  //遇到不支持的命令
    }
}

void find_p(void)//查找P所在的地址
{
    uint16_t temp;
    PLC_Addr=PLC_LAD_START_ADDR;
    for(temp=0; temp<15999; temp++) //总共16000步
    {
        if((*PLC_Addr&0xFF00)==0xB000)
            PLC_P_Addr[*PLC_Addr%0x100]=PLC_Addr;
        PLC_Addr++;
    }
}

void RST_Y(void)
{
    plc_16BitBuf[80]=0;
    plc_16BitBuf[81]=0;
}

uint16_t find_toend(void)//查找P所在的地址
{
    uint16_t temp;
    PLC_Addr=PLC_LAD_START_ADDR-1;
    temp=0;
    do {
        PLC_Addr++;
        temp++;
    }
    while((!(*PLC_Addr==0x000f))&&(temp<15998));
    return temp;
}


// kevin，20170519新增，PLC在Stop状态下默认初始化
void PLC_StopDefaultInit(void)
{
    PLC_BIT_OFF(M8000);	                     // 没有运行强制M80000为OFF
    PLC_BIT_ON(M8001);	                     // 没有运行强制M80001为on
    D8012=0;
    edit_prog=0;	                          // 编程时要用到
    puls=0; 		                          // 初始化脉冲用到8002 8003
    STOP_LAMP_OFF;                           // 关闭运行灯,如果从运行状态切换到停止状态，需要清除Y输出
    ERR_LAMP_OFF;
    PLC_STL_CMD = 0;
    PLC_STL_Status = 0; 	                   // 上次程序中的步进
}


// kevin，20160929优化
/*
 *PLC 的主入口函数
 */
void PLC_ProInstructParse(void)
{
    uint8_t i =0;
    run_flag=1;
    if(PLC_RUN && powerDownCnt==0 ) // 拨动开关至RUN,运行程序 ADD BY kevin
    {

        if(run_flag ==1)
        {
            run_flag = 0;
//            PLC_RW_RAM_8BIT(0X01E0)=0x09;
            PLC_W_RAM_8BIT(0X01E0,0x09);
        }

        if(PLC_R_RAM_8BIT(0X01E0)==0x09)     // 是否需要运行程序
        {
            PLC_BIT_ON(M8000);	              // 运行强制M80000为ON
            PLC_BIT_OFF(M8001);	              // 运行强制M80001为off
            RUN_LAMP_ON;                      // 亮起运行灯

            if(edit_prog== 0)		            // 判断是否存在程序编辑，如果编辑一次程序后必需重新计算P所在的址
            {
                edit_prog=1;
                find_p();
                if(find_toend()>15998)
                {
//                    PLC_RW_RAM_8BIT(0X01E0)=0x09;
                    PLC_W_RAM_8BIT(0X01E0,0x09);
                    goto all_end;
                }
            }

            if(puls == 0x00)		               // 初始化脉冲用到8002 8003, puls 运行一个周期后置1
            {
                PLC_BIT_ON(M8002);
                PLC_BIT_OFF(M8003);
            }

            PLC_Addr =  PLC_LAD_START_ADDR;       // PLC到起始地址

            //if(Write_Pro_flag == 0)            // 非梯形图写入 删除writePro_flag by kevin
                PLC_IO_Refresh();               // 刷新Y输出 kevin，2019年11月优化、新增

            do
            {
                uint16_t  data=*PLC_Addr;
                switch(*PLC_Addr/0x100)          // 取高8位的数据
                {
                    case 0x06:
                        operation_T(),PLC_Addr++;
                        break;  //operation all timer
                    case 0x0E:
                        operation_C(),PLC_Addr++;
                        break;  //
                        /* 操作S位元件所有的函数 */
                    case 0x20:
                        LD(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x30:
                        LDI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x40:
                        AND(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x50:
                        ANI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x60:
                        OR(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x70:
                        ORI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                        /* 操作S位元件所有的函数 */
                    case 0x21:
                        LD(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x31:
                        LDI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x41:
                        AND(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x51:
                        ANI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x61:
                        OR(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x71:
                        ORI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                        /* 操作S位元件所有的函数 */
                    case 0x22:
                        LD(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x32:
                        LDI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x42:
                        AND(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x52:
                        ANI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x62:
                        OR(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x72:
                        ORI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                        /* 操作S位元件所有的函数 */
                    case 0x23:
                        LD(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x33:
                        LDI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x43:
                        AND(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x53:
                        ANI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x63:
                        OR(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x73:
                        ORI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                        /* 操作X位元件所有的函数 */
                    case 0x24:
                        LD(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x34:
                        LDI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x44:
                        AND(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x54:
                        ANI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x64:
                        OR(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x74:
                        ORI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                        /* 操作Y位元件所有的函数 */
                    case 0x25:
                        LD(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x35:
                        LDI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x45:
                        AND(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;
                    case 0x55:
                        ANI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;
                    case 0x65:
                        OR(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x75:
                        ORI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0XC5:
                        OUT(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0XD5:
                        PLC_BIT_SET(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0XE5:
                        RST(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                        /* 操作T位元件所有的函数 */
                    case 0x26:
                        LD(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x36:
                        LDI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x46:
                        AND(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;
                    case 0x56:
                        ANI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;
                    case 0x66:
                        OR(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x76:
                        ORI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0XC6:
                        OUT(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                        /* 操作T位元件所有的函数 */
                    case 0x27:
                        LD(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x37:
                        LDI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x47:
                        AND(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;
                    case 0x57:
                        ANI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;
                    case 0x67:
                        OR(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x77:
                        ORI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0XC7:
                        OUT(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                        /* 操作M0_255位元件所有的函数 */
                    case 0x28:
                        LD(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x38:
                        LDI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x48:
                        AND(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;
                    case 0x58:
                        ANI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;
                    case 0x68:
                        OR(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x78:
                        ORI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0XC8:
                        OUT(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0XD8:
                        PLC_BIT_SET(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0XE8:
                        RST(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                        /* 操作M256_511位元件所有的函数 */
                    case 0x29:
                        LD(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x39:
                        LDI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x49:
                        AND(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;
                    case 0x59:
                        ANI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;
                    case 0x69:
                        OR(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x79:
                        ORI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0XC9:
                        OUT(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0XD9:
                        PLC_BIT_SET(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0XE9:
                        RST(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                        /* 操作M512_767位元件所有的函数 */
                    case 0x2A:
                        LD(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x3A:
                        LDI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x4A:
                        AND(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;
                    case 0x5A:
                        ANI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;
                    case 0x6A:
                        OR(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x7A:
                        ORI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0XCA:
                        OUT(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0XDA:
                        PLC_BIT_SET(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0XEA:
                        RST(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                        /* 操作M768_1023位元件所有的函数 */
                    case 0x2B:
                        LD(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x3B:
                        LDI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x4B:
                        AND(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;
                    case 0x5B:
                        ANI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;
                    case 0x6B:
                        OR(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x7B:
                        ORI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0XCB:
                        OUT(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0XDB:
                        PLC_BIT_SET(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0XEB:
                        RST(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                        /* 操作M1024_1279位元件所有的函数 */
                    case 0x2C:
                        LD(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x3C:
                        LDI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x4C:
                        AND(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;
                    case 0x5C:
                        ANI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;
                    case 0x6C:
                        OR(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x7C:
                        ORI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0XCC:
                        OUT(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0XDC:
                        PLC_BIT_SET(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0XEC:
                        RST(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                        /* 操作M1280_1535位元件所有的函数 */
                    case 0x2D:
                        LD(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x3D:
                        LDI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x4D:
                        AND(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;
                    case 0x5D:
                        ANI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;
                    case 0x6D:
                        OR(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x7D:
                        ORI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0XCD:
                        OUT(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0XDD:
                        PLC_BIT_SET(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0XED:
                        RST(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                        /* 操作C0-C255位元件所有的函数 */
                    case 0x2E:
                        LD(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x3E:
                        LDI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x4E:
                        AND(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;
                    case 0x5E:
                        ANI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;
                    case 0x6E:
                        OR(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x7E:
                        ORI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                        /*m8000-m8255*/
                    case 0x2F:
                        LD(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x3F:
                        LDI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x4F:
                        AND(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;
                    case 0x5F:
                        ANI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;
                    case 0x6F:
                        OR(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0x7F:
                        ORI(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0XCF:
                        OUT(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0XDF:
                        PLC_BIT_SET(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0XEF:
                        RST(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                        /**********************STL步进模式***************************/
                    case 0xF0:
                        STL(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //S
                    case 0xF1:
                        STL(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0xF2:
                        STL(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //
                    case 0xF3:
                        STL(0X0FFF&*PLC_Addr),PLC_Addr++;
                        break;  //

                        ////////////////////////////////////////////////////////
                        //基本逻辑指令二，如ANB、ORB、MPP、MRD、MPS、INV 等
                    case 0XFF:
                    {
                        other_function(*PLC_Addr);
                        PLC_Addr++;
                        break;   //MPP,MPS
                    }
                    case 0xB0: //指针P标识
                    {
                        PLC_Addr++;
                        break;
                    }
                    case 0x00: //遇到0X001C为FEND,0X000F为END指令
                    {
                        if(((*PLC_Addr%0x100)==0x1C)||((*PLC_Addr%0x100)==0x0F))
                        {
//disable by kevin for modbus and modbus TCP
//                            if( md_config[0][1] ==1) // 第1路modbus主机模式
//                                i =0;
//                            else if(md_config[1][1] ==1) // 第2路modbus主机模式
//                                i =1;

//#if W5500_FUNC
//                            /* TCP 20190929 */
//                            if((gModTcp.sockTcpFunc & 0x8001) ==0x8001)
//                                i =2;
//#endif /* #if W5500_FUNC */
//                            gModH[i].cmdSum = gModH[i].cmdNumStatistics;
//                            gModH[i].cmdNumStatistics =0;

                            goto all_end;
                        }
                    }

                        //////////////////////////////////////////////////////////////
                        // kevin，20160929注释，应用指令
                        //////////////////////////////////////////////////////////////
                    default: //遇到不支持的命令 ,此处需要执行命令为16bit的指令
                    {
                        FNC_AppInstruct();
                        break;
                    }
                }
            } while(1);

            all_end:
            D8010=D8011=D8012=plc_scan_time;       // 保持扫描时间
            plc_scan_time=0;                       // 清除扫描时间
            puls=0x01;
            PLC_BIT_OFF(M8002),PLC_BIT_ON(M8003);  // 初始化脉冲用到8002 8003


            MC.PLC_MC_BIT = 0;
            MC.MC_SFR = 0;


            if(run_flag ==2) // 复原 串口配置
            {
                run_flag = 0;
                //HS_MOD1_MODE =commParm;  //bug:会使串口参数一直是0x1081，恢复运行是应该是保持FX协议，PLC程序有要求初始化时
            }                              //才会进行初始化


            // 485通信切换FX协议和Modbus协议
            //UART_ParmSelect();

            //PLC控制，重设串口参数
            if(PLC_BIT_TEST(0X0FFF & SET_COMPAR))    //M8120=1时，重设串口
            {

                //  UART_ParmSelect 将（D8120）HS_MOD1_MODE数据传入
                //	D8120为0或ffff时，传入默认值 0x91,19200,8，n，1
//                UART_ParmSelect();// kevin，20199612新增，Modbus主从模式切换，串口参数设置 disable modbus by kevin
                PLC_BIT_OFF(SET_COMPAR);
            }
        }
        else  // 软停
        {
            PLC_StopDefaultInit();

//            if(Write_Pro_flag == 0)
//            {
                RST_Y();
                PLC_IO_Refresh();               // 刷新Y输出   kevin，2019年11月优化、新增
                RST_T_D_C_M_data();

//            }

            if(run_flag == 0)
            {
                run_flag = 2;
                // 485通信切换FX协议
//                commParm = HS_MOD1_MODE; // disable by kevin for modbus
//                HS_MOD1_MODE = 0x1081; // disable by kevin for modbus
                // ModBus_Parm_init();
//                UART_ParmSelect();   // disable by kevin for modbus
            }

        }
    }
    else	// 拨动开关至STOP
    {

        PLC_StopDefaultInit();

//        Write_Pro_flag = 0;
        RST_Y();   												 // 如果从运行状态切换到停止状态，需要清除Y输出
        PLC_IO_Refresh(); // kevin，2019年11月优化、新增

        if(run_flag == 0||run_flag == 2)
        {
            run_flag = 1;

//            PLC_RW_RAM_8BIT(0X01E0)=0x0A;
            PLC_W_RAM_8BIT(0X01E0,0x0A);

            plc_scan_time=0;
            RST_T_D_C_M_data();

/* disable by kevin for modbus
//            if( md_config[0][1] ==1) // 第1路modbus主机模式
//                i =0;
//            else if(md_config[1][1] ==1) // 第2路modbus主机模式
//                i =1;
//
//            memset((void *)&gModH[i],0,sizeof(MODH_T));
//
//            // 485通信切换FX协议
//            commParm = HS_MOD1_MODE;
//            HS_MOD1_MODE = 0x1081;
//            //ModBus_Parm_init();
//            UART_ParmSelect();
*/
        }
    }
   // plc_16BitBuf[0X701]=0X000;		          // 设置版本号


}