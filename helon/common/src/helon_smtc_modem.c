/*
 * Copyright (c) 2024, Xi'an Helon-Electric Co., Ltd.
 *
 * Author: kev<PERSON><PERSON><PERSON><PERSON>
 *
 * CONFIDENTIAL AND PROPRIETARY
 * 严格保密，泄露必究
 *
 * Change Logs:
 * Date           Author       Notes
 * 2024-03-27    kevin.zhao  first version
 */ 

#include <rtthread.h>
#include <rtdevice.h>
#include "modem_core.h"
#include "radio_planner.h"
#include "smtc_modem_api.h"
#include "lorawan_api.h"
#include "ralf_drv.h"
#include "smtc_secure_element.h"
#include "rf.h"
#include "smtc_modem_utilities.h"
#include "radio_multi_manager.h"
#include "rf_proxy.h"
#include "radio_state.h"
#include "smtc_modem_hal_dbg_trace.h"
#include "modem_supervisor/modem_supervisor_light.h"
#include "modem_utilities/modem_event_utilities.h"

#define DBG_TAG "helon_modem"
#define DBG_LVL DBG_INFO
#include <rtdbg.h>

// 外部变量声明
extern radio_planner_t modem_radio_planner;
ralf_t modem_radio[BSP_RADIO_NUMBER];
// 声明必要的函数和变量
extern void modem_context_init_light(void (*callback_event)(void), radio_planner_t* rp);
extern void modem_set_radio_ctx(const void* radio_ctx);
extern void modem_supervisor_init(void);
extern uint32_t modem_supervisor_engine(void);
extern void rp_init(radio_planner_t* rp, const ralf_t* radio);
extern rp_hook_status_t rp_hook_init(radio_planner_t* rp, const uint8_t id, void (*callback)(void*), void* hook);
extern bool rp_get_irq_flag(void* obj);

// 射频中断回调函数声明
void rp_radio_irq_callback(void* obj);

/**
 * @brief 空回调函数
 */
static void helon_empty_callback(void* ctx)
{
    // 什么都不做
    (void)ctx;
}

// 宏定义
#ifndef SMTC_MODEM_HAL_PANIC_ON_FAILURE
#define SMTC_MODEM_HAL_PANIC_ON_FAILURE(x) \
    do { \
        if (!(x)) { \
            LOG_E("HAL Panic: %s, line %d", __FILE__, __LINE__); \
            while (1) {}; \
        } \
    } while (0)
#endif

/**
 * @brief 重写的LBM库初始化函数
 */
void smtc_modem_init_extended(int radio_index, void (*callback_event)(void))
{
    LOG_I("Helon Multi-Radio: smtc_modem_init called");
    
    // 应用射频代理
    rf_proxy_apply();
    
    // 调用原始函数
    SMTC_MODEM_HAL_TRACE_INFO("Modem Initialization\n");
    
    // 初始化射频
    SMTC_MODEM_HAL_PANIC_ON_FAILURE(ral_reset(&(modem_radio[radio_index].ral)) == RAL_STATUS_OK);
    SMTC_MODEM_HAL_PANIC_ON_FAILURE(ral_init(&(modem_radio[radio_index].ral)) == RAL_STATUS_OK);
    SMTC_MODEM_HAL_PANIC_ON_FAILURE(ral_set_sleep(&(modem_radio[radio_index].ral), true) == RAL_STATUS_OK);
    smtc_modem_hal_set_ant_switch(false);
    
    // 初始化射频规划器
    rp_init(&modem_radio_planner[radio_index], (const ralf_t*)&modem_radio[radio_index]);
    
    // 设置中断回调
    smtc_modem_hal_irq_config_radio_irq(rp_radio_irq_callback, &modem_radio_planner[radio_index]);
    
    // 初始化挂起钩子
    rp_hook_status_t hook_status = rp_hook_init(&modem_radio_planner[radio_index], RP_HOOK_ID_SUSPEND, (void (*)(void*))(helon_empty_callback),
                 &modem_radio_planner[radio_index]);
    if (hook_status != RP_HOOK_STATUS_OK) {
        LOG_W("Hook initialization failed with status %d", hook_status);
    }
    
    #ifdef BSP_USING_HELONET_CRYPTO
    smtc_secure_element_init();
    #endif
    
    modem_supervisor_init();
    modem_context_init_light(callback_event, &modem_radio_planner);
    
    #ifdef USE_TX_PROTOCOL_MANAGEMENT
    modem_tx_protcol_manager_init(&modem_radio_planner[radio_index]);
    #endif
    
    // 发送复位事件
    increment_asynchronous_msgnumber(SMTC_MODEM_EVENT_RESET, 0, 0xFF);
    
    // 保存LBM库初始化后的上下文
    rf_proxy_save_original_context();
    
    // 恢复射频代理
    rf_proxy_restore();
    
    LOG_I("Helon Multi-Radio: LBM initialization completed");
}

/**
 * @brief 重写的LBM库运行引擎函数
 */
uint32_t smtc_modem_run_engine(void)
{
    uint32_t time_ms;
    
    // 应用射频代理
    rf_proxy_apply();
    
    // 处理所有射频的中断
    radio_multi_process_irq();
    
    // 调用原始函数
    time_ms = modem_supervisor_engine();
    
    // 恢复射频代理
    rf_proxy_restore();
    
    return time_ms;
}

/**
 * @brief 重写的LBM库设置射频上下文函数
 */
rt_weak void smtc_modem_set_radio_context(const void* radio_ctx)
{
    uint8_t radio_index = radio_multi_get_active();
    
    // 更新当前活动射频的上下文
    ralf_t* radio = radio_multi_get_device(radio_index);
    if (radio != NULL) {
        radio->ral.context = (void*)radio_ctx;
    }
    
    // 如果是射频0，也调用原始函数更新上下文
    if (radio_index == 0) {
        modem_set_radio_ctx((void*)radio_ctx);
    }
    
    LOG_D("Radio %d context set", radio_index);
}

/**
 * @brief 重写的LBM库获取射频上下文函数
 */
rt_weak const void* smtc_modem_get_radio_context(void)
{
    uint8_t radio_index = radio_multi_get_active();
    ralf_t* radio = radio_multi_get_device(radio_index);
    
    if (radio != NULL) {
        return radio->ral.context;
    }
    
    return NULL;
}

/**
 * @brief 重写的LBM库检查中断标志函数
 */
rt_weak bool smtc_modem_is_irq_flag_pending(void)
{
    uint8_t radio_index = radio_multi_get_active();
    
    // 检查当前活动射频的中断挂起标志
    if (radio_index < BSP_RADIO_NUMBER) {
        if (g_radio_manager.irq_pending_flags[radio_index]) {
            return true;
        }
    }
    
    // 对于射频0，也检查原始规划器的标志
    if (radio_index == 0) {
        return rp_get_irq_flag((void*)&modem_radio_planner);
    }
    
    // 检查当前射频规划器的标志
    radio_planner_t* rp = radio_multi_get_planner(radio_index);
    if (rp != NULL) {
        return rp_get_irq_flag((void*)rp);
    }
    
    return false;
}




// /* 射频设备实例数组 */
// rf_t rf_devices[BSP_RADIO_NUMBER] = {0};

/* 定时器设备实例 - 从smtc_modem_hal.c引用而非定义 */
extern rt_device_t modem_timer_device;

/* SPI总线互斥锁 */
rt_mutex_t spi_mutex = RT_NULL;



/* 初始化函数，处理与原始LBM库的兼容 */
int radio_instances_init(void)
{
#if defined(BSP_USING_RADIO1) 
#if defined( BSP_RADIO1_USING_SX128X )
 ralf_t temp_radio = RALF_SX128X_INSTANTIATE( rf_devices[0] );
modem_radio[0] = temp_radio;
#elif defined( BSP_RADIO1_USING_SX126X )
ralf_t temp_radio = RALF_SX126X_INSTANTIATE( rf_devices[0] );
modem_radio[0] = temp_radio;
#elif defined( BSP_RADIO1_USING_LLCC68 )
ralf_t temp_radio = RALF_LLCC68_INSTANTIATE( rf_devices[0] );
modem_radio[0] = temp_radio;
#elif defined( BSP_RADIO1_USING_LR11XX )
ralf_t temp_radio = RALF_LR11XX_INSTANTIATE( rf_devices[0] );
modem_radio[0] = temp_radio;
#elif defined( BSP_RADIO1_USING_SX127X )
ralf_t temp_radio = RALF_SX127X_INSTANTIATE( rf_devices[0] );
modem_radio[0] = temp_radio;
#endif
#endif

#if defined(BSP_USING_RADIO2) || defined(BSP_USING_RADIO3) || defined(BSP_USING_RADIO4)
#if defined( BSP_RADIO2_USING_SX128X )
ralf_t temp_radio1 = RALF_SX128X_INSTANTIATE( rf_devices[1] );
modem_radio[1] = temp_radio1;
#elif defined( BSP_RADIO2_USING_SX126X )
ralf_t temp_radio1 = RALF_SX126X_INSTANTIATE( rf_devices[1] );
modem_radio[1] = temp_radio1;
#elif defined( BSP_RADIO2_USING_LLCC68 )
ralf_t temp_radio1 = RALF_LLCC68_INSTANTIATE( rf_devices[1] );
modem_radio[1] = temp_radio1;
#elif defined( BSP_RADIO2_USING_LR11XX )
ralf_t temp_radio1 = RALF_LR11XX_INSTANTIATE( rf_devices[1] );
modem_radio[1] = temp_radio1;
#elif defined( BSP_RADIO2_USING_SX127X )
ralf_t temp_radio1 = RALF_SX127X_INSTANTIATE(  rf_devices[1] );
modem_radio[1] = temp_radio1;
#endif
#endif

#if defined(BSP_USING_RADIO3) || defined(BSP_USING_RADIO4)
#if defined( BSP_RADIO3_USING_SX128X )
ralf_t temp_radio2 = RALF_SX128X_INSTANTIATE( rf_devices[2] );
modem_radio[2] = temp_radio2;
#elif defined( BSP_RADIO3_USING_SX126X )
ralf_t temp_radio2 = RALF_SX126X_INSTANTIATE( rf_devices[2] );
#elif defined( BSP_RADIO3_USING_LLCC68 )
ralf_t temp_radio2 = RALF_LLCC68_INSTANTIATE( rf_devices[2] );
modem_radio[2] = temp_radio2;
#elif defined( BSP_RADIO3_USING_LR11XX )
ralf_t temp_radio2 = RALF_LR11XX_INSTANTIATE( rf_devices[2] );
modem_radio[2] = temp_radio2;
#elif defined( BSP_RADIO3_USING_SX127X )
        static sx127x_t g_sx127x2;
ralf_t temp_radio2 = RALF_SX127X_INSTANTIATE( rf_devices[2] );
modem_radio[2] = temp_radio2;
#endif
#endif

#if defined(BSP_USING_RADIO4)
#if defined( BSP_RADIO4_USING_SX128X )
ralf_t temp_radio3 = RALF_SX128X_INSTANTIATE( rf_devices[3] );
modem_radio[3] = temp_radio3;
#elif defined( BSP_RADIO4_USING_SX126X )
ralf_t temp_radio3 = RALF_SX126X_INSTANTIATE( rf_devices[3] );
#elif defined( BSP_RADIO4_USING_LLCC68 )
ralf_t temp_radio3 = RALF_LLCC68_INSTANTIATE( rf_devices[3] );
modem_radio[3] = temp_radio3;
#elif defined( BSP_RADIO4_USING_LR11XX )
ralf_t temp_radio3 = RALF_LR11XX_INSTANTIATE( rf_devices[3] );
modem_radio[3] = temp_radio3;
#elif defined( BSP_RADIO4_USING_SX127X )
ralf_t temp_radio3 = RALF_SX127X_INSTANTIATE( rf_devices[3] );
modem_radio[3] = temp_radio3;
#endif
#endif

    /* 创建SPI互斥锁 */
    if (spi_mutex == RT_NULL)
    {
        spi_mutex = rt_mutex_create("spi_mutex", RT_IPC_FLAG_FIFO);
        if (spi_mutex == RT_NULL)
        {
            rt_kprintf("Failed to create SPI mutex\n");
            return -RT_ENOMEM;
        }
    }

    rt_kprintf("Radio instances initialized\n");
    
    return RT_EOK;
}



void smtc_modem_set_radio_context( int radio_index, const void* radio_ctx ) // add by kevin
{
#if defined( BSP_RADIO2_USING_SX127X )
    // update modem_radio context with provided one
    ( ( sx127x_t* ) modem_radio[radio_index].ral.context )->hal_context = radio_ctx;
#else
    // update modem_radio context with provided one
    modem_radio[radio_index].ral.context = radio_ctx;
#endif
    // Save modem radio context in case of direct access to radio by the modem
    modem_set_radio_ctx( modem_radio[radio_index].ral.context );
}

/* 导出到自动初始化 */
INIT_PREV_EXPORT(radio_instances_init); 