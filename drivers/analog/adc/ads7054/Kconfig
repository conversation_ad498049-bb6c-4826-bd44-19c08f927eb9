menu "ADS7054 ADC Configuration"

config BSP_USING_ADS7054
    bool "Enable ADS7054 Support"
    default n
    select BSP_USING_SPI
    help
        Enable ADS7054 14-bit precision ADC device support

if BSP_USING_ADS7054
    config ADS7054_SPI_BUS_NAME
        string "ADS7054 SPI Bus Name"
        default "spi1"
        help
            Set ADS7054 SPI bus name

    config ADS7054_CS_PIN
        string "ADS7054 CS Pin Number"
        default "PA.0"  
        help
            Set ADS7054 chip select pin number

    config ADS7054_USING_DMA
        bool "Enable DMA Support"
        default y
        help
            Enable DMA for ADS7054 SPI communication

    config ADS7054_USING_AVERAGING
        bool "Enable Averaging"
        default y
        help
            Enable averaging for ADS7054 readings

    if ADS7054_USING_AVERAGING
        config ADS7054_AVERAGING_SAMPLES
            int "Number of Samples for Averaging"
            range 2 64
            default 8
            help
                Set number of samples for averaging (2-64)
    endif

    choice
        prompt "ADS7054 Operating Mode"
        default ADS7054_MODE_NORMAL

        config ADS7054_MODE_NORMAL
            bool "Normal Mode"
            help
                Set ADS7054 to normal operating mode

        config ADS7054_MODE_POWER_DOWN
            bool "Power Down Mode"
            help
                Set ADS7054 to power down mode

        config ADS7054_MODE_AUTO_POWER_DOWN
            bool "Auto Power Down Mode"
            help
                Set ADS7054 to auto power down mode
    endchoice

    choice
        prompt "ADS7054 Reference Mode"
        default ADS7054_REF_INTERNAL

        config ADS7054_REF_INTERNAL
            bool "Internal Reference"
            help
                Use ADS7054 internal reference

        config ADS7054_REF_EXTERNAL
            bool "External Reference"
            help
                Use external reference for ADS7054
    endchoice
endif

endmenu 