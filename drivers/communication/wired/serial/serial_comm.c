/*
 * Copyright (c) 2024, Xi'an Helon-Electric Co., Ltd.
 *
 * Author: kevin<PERSON><PERSON><PERSON>
 *
 * CONFIDENTIAL AND PROPRIETARY
 * 严格保密，泄露必究
 *
 * Change Logs:
 * Date           Author       Notes
 * 2024-08-01    kevin.zhao  first version
 */

#include <rtthread.h>
#include <rtdevice.h>
#include "serial_comm.h"

#define DBG_TAG     "serial.comm"
#define DBG_LVL     DBG_INFO
#include <rtdbg.h>

/* 线程参数 */
#define SERIAL_RX_THREAD_TIMESLICE     10

/* DMA发送完成回调函数 */
static void (*_dma_tx_done_cb)(serial_comm_t *comm) = RT_NULL;

/* 多串口管理器全局实例 */
static serial_multi_manager_t g_serial_manager = {0};

/* 根据设备查找通信实例 - 使用g_serial_manager统一管理 */
static serial_comm_t *find_comm_by_device(rt_device_t dev)
{
    int i;
    
    if (dev == RT_NULL) {
        return RT_NULL;
    }
    
    /* 遍历所有实例查找匹配的设备 */
    for (i = 0; i < BSP_SERIAL_NUMBER; i++) {
        if (g_serial_manager.instances[i] != RT_NULL && 
            g_serial_manager.instances[i]->device == dev) {
            return g_serial_manager.instances[i];
        }
    }
    
    return RT_NULL;
}

/* 内部函数前向声明 */
static int serial_recv_forever(serial_comm_t *comm, void *buffer, rt_size_t size);
static rt_err_t wait_for_rx_event(serial_comm_t *comm, rt_int32_t timeout);
static rt_bool_t is_break_received(serial_comm_t *comm);

/**
 * 串口接收中断回调函数
 */
static rt_err_t serial_rx_ind(rt_device_t dev, rt_size_t size)
{
    serial_comm_t *comm = (serial_comm_t *)(dev->user_data);
    
    /* 参数检查 */
    if (comm == RT_NULL) {
        return -RT_EINVAL;
    }
    
    /* 检查接收缓冲区是否可能溢出 */
    if (size > SERIAL_GET_RX_BUFSZ(comm)) {
        /* 更新溢出错误统计 */
        comm->stats.overflow_errors++;
        LOG_W("Receive buffer overflow detected, size: %d, buffer: %d", 
              (int)size, SERIAL_GET_RX_BUFSZ(comm));
    }
    
    /* 触发接收事件 */
    return rt_event_send(comm->event, SERIAL_RX_EVENT);
}

/**
 * 串口接收线程
 */
static void serial_rx_thread(void *param)
{
    serial_comm_t *comm = (serial_comm_t *)param;
    rt_event_t event = comm->event;
    rt_uint32_t recved;
    struct serial_event_msg msg;
    
    /* 等待启动 */
    while (comm->status != SERIAL_STATUS_CONNECTED) {
        rt_thread_mdelay(10);
        
        /* 检查是否需要退出 */
        if (comm->status == SERIAL_STATUS_CLOSED) {
            LOG_I("Receive thread exit");
            return;
        }
    }
    
    LOG_D("Receive thread started");
    
    while (1) {
        /* 等待事件 */
        if (rt_event_recv(event, SERIAL_RX_EVENT, RT_EVENT_FLAG_OR | RT_EVENT_FLAG_CLEAR, 
                         RT_WAITING_FOREVER, &recved) != RT_EOK) {
            continue;
        }
        
        /* 检查是否需要退出 */
        if (comm->status == SERIAL_STATUS_CLOSED) {
            LOG_I("Receive thread exit");
            return;
        }
        
        /* 检查回调是否有效 */
        if (comm->event_cb == RT_NULL) {
            continue;
        }
        
        /* 准备消息 */
        msg.type = SERIAL_EVENT_RECV_DATA;
        msg.param = 0;
        
        /* 调用回调 */
        comm->event_cb(comm, &msg, comm->user_data);
    }
}

/**
 * 创建通用串口通信实例
 */
serial_comm_t *serial_create(serial_init_param_t *param)
{
    serial_comm_t *comm;
    char thread_name[RT_NAME_MAX];
    
    /* 参数检查 */
    if (param == RT_NULL || param->device_name == RT_NULL) {
        LOG_E("Invalid configuration");
        return RT_NULL;
    }
    
    /* 检查类型范围 */
    if (param->type > SERIAL_TYPE_RS232) {
        LOG_E("Invalid device type %d", param->type);
        return RT_NULL;
    }
    
    /* 分配内存 */
    comm = (serial_comm_t *)rt_malloc(sizeof(serial_comm_t));
    if (comm == RT_NULL) {
        LOG_E("Failed to allocate memory for serial communication");
        return RT_NULL;
    }
    
    /* 初始化结构体 */
    rt_memset(comm, 0, sizeof(serial_comm_t));
    
    /* 查找RT-Thread串口设备 */
    comm->device = rt_device_find(param->device_name);
    if (comm->device == RT_NULL) {
        LOG_E("Failed to find device %s", param->device_name);
        rt_free(comm);
        return RT_NULL;
    }
    
    /* 获取串口设备结构体 */
    comm->serial_dev = (struct rt_serial_device *)comm->device;
    
    /* 检查设备类型 */
    if (comm->device->type != RT_Device_Class_Char) {
        LOG_E("Device %s is not a character device", param->device_name);
        rt_free(comm);
        return RT_NULL;
    }
    
    /* 初始化互斥锁 */
    comm->lock = rt_mutex_create("serial_lock", RT_IPC_FLAG_FIFO);
    if (comm->lock == RT_NULL) {
        LOG_E("Failed to create mutex");
        rt_free(comm);
        return RT_NULL;
    }
    
    /* 初始化事件 */
    comm->event = rt_event_create("serial_event", RT_IPC_FLAG_FIFO);
    if (comm->event == RT_NULL) {
        LOG_E("Failed to create event");
        rt_mutex_delete(comm->lock);
        rt_free(comm);
        return RT_NULL;
    }
    
    /* 设置设备类型 */
    comm->type = param->type;
    
    /* 设置初始状态 */
    comm->status = SERIAL_STATUS_INITIALIZED;
    
    /* 初始化统计信息 */
    rt_memset(&comm->stats, 0, sizeof(serial_stats_t));
    
    /* 直接设置到RT-Thread串口设备配置中 */
    comm->serial_dev->config.baud_rate = param->baudrate;
    comm->serial_dev->config.data_bits = param->data_bits;
    comm->serial_dev->config.stop_bits = param->stop_bits;
    comm->serial_dev->config.parity = param->parity;
    comm->serial_dev->config.bit_order = BIT_ORDER_LSB;
    comm->serial_dev->config.invert = NRZ_NORMAL;
    comm->serial_dev->config.rx_bufsz = param->rx_buf_size;
    comm->serial_dev->config.tx_bufsz = param->tx_buf_size;
    comm->serial_dev->config.flowcontrol = RT_SERIAL_FLOWCONTROL_NONE;
    
    /* 设置扩展参数（只存储在 serial_comm_t 中） */
    comm->rs485_en_pin = param->rs485_en_pin;
    comm->use_dma = param->use_dma;
    comm->timeout = param->recv_timeout > 0 ? param->recv_timeout : SERIAL_RECV_TMO_DEFAULT;
    comm->byte_tmo = param->byte_timeout > 0 ? param->byte_timeout : 0;  /* 0表示自动计算 */
    comm->tx_delay_us = param->tx_delay_us;
    comm->rx_delay_us = param->rx_delay_us;
    
    /* 初始化接收缓冲区 */
    comm->rx_buffer = rt_malloc(SERIAL_GET_RX_BUFSZ(comm));
    if (comm->rx_buffer == RT_NULL) {
        LOG_E("Failed to allocate receive buffer");
        rt_event_delete(comm->event);
        rt_mutex_delete(comm->lock);
        rt_free(comm);
        return RT_NULL;
    }
    
    /* 创建接收线程 - 使用全局计数器确保唯一性 */
    static rt_uint8_t serial_instance_count = 0;
    rt_uint8_t instance_id = serial_instance_count++;
    
    if (param->device_name && rt_strlen(param->device_name) >= 5 && 
        rt_strncmp(param->device_name, "uart", 4) == 0) {
        /* 对于标准的uartX格式，使用srxX_Y命名(X=设备号，Y=实例号) */
        const char *uart_num = param->device_name + 4;  /* 跳过"uart"前缀 */
        rt_snprintf(thread_name, sizeof(thread_name), "srx%s_%d", 
                    uart_num, instance_id);
    } else {
        /* 对于其他格式或空设备名，使用实例编号 */
        rt_snprintf(thread_name, sizeof(thread_name), "srx_%d", instance_id);
    }
    
    /* 确保名称不超过RT_NAME_MAX限制 */
    if (rt_strlen(thread_name) >= RT_NAME_MAX) {
        /* 如果名称过长，回退到简单的实例编号 */
        rt_snprintf(thread_name, sizeof(thread_name), "srx_%d", instance_id);
    }
    
    comm->rx_thread = rt_thread_create(thread_name, 
                                     serial_rx_thread, 
                                     comm, 
                                     SERIAL_RX_THREAD_STACK_SIZE, 
                                     SERIAL_RX_THREAD_PRIORITY, 
                                     SERIAL_RX_THREAD_TIMESLICE);
    if (comm->rx_thread == RT_NULL) {
        LOG_E("Failed to create receive thread");
        rt_free(comm->rx_buffer);
        rt_event_delete(comm->event);
        rt_mutex_delete(comm->lock);
        rt_free(comm);
        return RT_NULL;
    }
    
    /* 启动线程 */
    rt_thread_startup(comm->rx_thread);
    
    /* 为RS485初始化引脚 */
    if (comm->type == SERIAL_TYPE_RS485) {
        /* 初始化RS485使能引脚 */
        if (comm->rs485_en_pin >= 0) {
            rt_pin_mode(comm->rs485_en_pin, PIN_MODE_OUTPUT);
            rt_pin_write(comm->rs485_en_pin, PIN_LOW);  /* 默认接收模式 */
        }
        
        /* 计算默认字节超时(如果未设置) */
        if (comm->serial_dev->config.baud_rate > 0 && comm->byte_tmo == 0) {
            comm->byte_tmo = calc_byte_timeout(comm->serial_dev->config.baud_rate);
            LOG_D("Auto byte timeout: %d ms for baudrate %d", comm->byte_tmo, comm->serial_dev->config.baud_rate);
        }
        
        LOG_I("Created RS485 instance for %s, EN pin: %d", 
              param->device_name, comm->rs485_en_pin);
    } else {
        LOG_I("Created serial instance for %s, type: %d", param->device_name, comm->type);
    }
    
    return comm;
}

/**
 * 销毁串口通信实例
 */
int serial_destroy(serial_comm_t *comm)
{
    /* 参数检查 */
    if (comm == RT_NULL) {
        return -RT_EINVAL;
    }
    
    /* 检查是否已连接，如果是则先断开 */
    if (comm->status == SERIAL_STATUS_CONNECTED) {
        serial_disconnect(comm);
    }
    
    /* 设置状态为关闭 */
    comm->status = SERIAL_STATUS_CLOSED;
    
    /* 触发接收线程退出 */
    rt_event_send(comm->event, SERIAL_RX_EVENT);
    
    /* 等待接收线程退出 */
    rt_thread_mdelay(10);
    
    /* 释放资源 */
    rt_free(comm->rx_buffer);
    rt_event_delete(comm->event);
    rt_mutex_delete(comm->lock);
    rt_free(comm);
    
    LOG_I("Destroyed serial communication instance");
    return RT_EOK;
}

/**
 * 连接串口设备
 */
int serial_connect(serial_comm_t *comm)
{
    rt_uint32_t oflag;
    
    /* 参数检查 */
    if (comm == RT_NULL || comm->device == RT_NULL) {
        return -RT_EINVAL;
    }
    
    /* 检查状态 */
    if (comm->status == SERIAL_STATUS_CONNECTED) {
        LOG_D("Device already connected");
        return RT_EOK;
    }
    
    /* 锁定设备 */
    if (rt_mutex_take(comm->lock, SERIAL_LOCK_TIMEOUT) != RT_EOK) {
        LOG_E("Failed to take mutex");
        return -RT_ETIMEOUT;
    }
    
    /* 确定打开标志 */
    oflag = RT_DEVICE_FLAG_RDWR | RT_DEVICE_FLAG_INT_RX;
    
    /* 如果支持DMA，优先使用DMA发送 */
    if (comm->device->flag & RT_DEVICE_FLAG_DMA_TX) {
        oflag |= RT_DEVICE_FLAG_DMA_TX;
        comm->use_dma = RT_TRUE;
        LOG_D("Device supports DMA TX, enabled");
    } else {
        comm->use_dma = RT_FALSE;
    }
    
    /* 打开设备 */
    if (rt_device_open(comm->device, oflag) != RT_EOK) {
        LOG_E("Failed to open device");
        rt_mutex_release(comm->lock);
        return -RT_ERROR;
    }
    
    /* 设置流控 */
    if (comm->serial_dev->config.flowcontrol == RT_SERIAL_FLOWCONTROL_CTSRTS) {
        if (rt_device_control(comm->device, RT_DEVICE_CTRL_SET_INT, 
                             (void *)RT_SERIAL_FLOWCONTROL_CTSRTS) != RT_EOK) {
            LOG_W("Failed to set hardware flow control, not supported?");
        }
    }
    
    /* 设置串口配置 */
    if (rt_device_control(comm->device, RT_DEVICE_CTRL_CONFIG, &comm->serial_dev->config) != RT_EOK) {
        LOG_E("Failed to configure serial device");
        rt_device_close(comm->device);
        rt_mutex_release(comm->lock);
        return -RT_ERROR;
    }
    
    /* 设置接收回调 */
    comm->device->user_data = (void*)comm;
    rt_device_set_rx_indicate(comm->device, serial_rx_ind);
    
    /* 更新状态 */
    comm->status = SERIAL_STATUS_CONNECTED;
    
    rt_mutex_release(comm->lock);
    LOG_I("Device connected");
    return RT_EOK;
}

/**
 * 断开串口设备
 */
int serial_disconnect(serial_comm_t *comm)
{
    /* 参数检查 */
    if (comm == RT_NULL || comm->device == RT_NULL) {
        return -RT_EINVAL;
    }
    
    /* 检查状态 */
    if (comm->status != SERIAL_STATUS_CONNECTED) {
        LOG_D("Device not connected");
        return RT_EOK;
    }
    
    /* 锁定设备 */
    if (rt_mutex_take(comm->lock, SERIAL_LOCK_TIMEOUT) != RT_EOK) {
        LOG_E("Failed to take mutex");
        return -RT_ETIMEOUT;
    }
    
    /* 关闭设备 */
    if (rt_device_close(comm->device) != RT_EOK) {
        LOG_E("Failed to close device");
        rt_mutex_release(comm->lock);
        return -RT_ERROR;
    }
    
    /* 更新状态 */
    comm->status = SERIAL_STATUS_DISCONNECTED;
    
    rt_mutex_release(comm->lock);
    LOG_I("Device disconnected");
    return RT_EOK;
}

/**
 * 尝试读取数据并处理字节间隔超时 (内部函数)
 * @return >0:接收字节数, 0:未接收到数据, <0:错误或超时
 */
static int try_read_bytes(serial_comm_t *comm, rt_uint8_t *buf, rt_size_t size, 
                         rt_size_t offset, rt_int32_t timeout_remaining)
{
    rt_size_t recv_len;
    rt_err_t result;
    
    /* 读取数据 */
    recv_len = rt_device_read(comm->device, 0, buf + offset, size - offset);
    if (recv_len == 0) {
        return 0; /* 未读取到数据 */
    }
    
    /* 已读取到数据，检查是否需要字节间隔超时 */
    if (offset + recv_len < size && comm->byte_tmo > 0) {
        /* 计算等待时间 */
        rt_int32_t byte_wait = comm->byte_tmo;
        if (timeout_remaining > 0 && timeout_remaining < byte_wait) {
            byte_wait = timeout_remaining;
        }
        
        /* 等待下一个字节或超时 */
        result = wait_for_rx_event(comm, byte_wait);
        
        /* 字节间隔超时，提前结束 */
        if (result == -RT_ETIMEOUT) {
            LOG_D("Byte timeout after %d ms, received %d bytes", 
                 byte_wait, (int)(offset + recv_len));
            return -2; /* 特殊返回值表示字节超时 */
        }
        
        /* 检查是否被中断 */
        if (result == RT_EOK && is_break_received(comm)) {
            LOG_D("Receive interrupted");
            return -1; /* 特殊返回值表示接收中断 */
        }
    }
    
    return recv_len;
}

/**
 * 带超时的接收数据实现
 */
int serial_recv_with_timeout(serial_comm_t *comm, void *buffer, rt_size_t size, int timeout)
{
    rt_size_t recv_total = 0;
    int read_result;
    rt_tick_t start_tick, curr_tick;
    rt_err_t result;
    rt_uint8_t *buf = (rt_uint8_t *)buffer;
    
    /* 参数检查 */
    SERIAL_CHECK_PARAM(comm != RT_NULL && buffer != RT_NULL && size > 0, -RT_EINVAL);
    SERIAL_CHECK_PARAM(SERIAL_CHECK_TMO_RANGE(timeout), -RT_EINVAL);
    SERIAL_CHECK_CONN(comm, SERIAL_ERROR_NOT_CONNECTED);

    if (comm->type == SERIAL_TYPE_RS485) {
        /* 如果是RS485模式，先设置为接收模式 */
        RS485_RX_ENABLE(comm);
    }
    
    /* 特殊情况处理：立即返回模式 */
    if (timeout == 0) {
        return rt_device_read(comm->device, 0, buffer, size);
    }
    
    /* 无限等待模式 */
    if (timeout < 0) {
        return serial_recv_forever(comm, buffer, size);
    }
    
    /* 有限等待模式 */
    start_tick = rt_tick_get();
    rt_int32_t timeout_remaining = timeout;
    
    while (recv_total < size && timeout_remaining > 0) {
        /* 尝试读取数据 */
        read_result = try_read_bytes(comm, buf, size, recv_total, timeout_remaining);
        
        if (read_result > 0) {
            /* 成功读取数据 */
            recv_total += read_result;
        } else if (read_result == -2) {
            /* 字节间隔超时，返回已接收数据 */
            return recv_total;
        } else if (read_result == -1) {
            /* 接收被中断 */
            return recv_total > 0 ? recv_total : -RT_EINTR;
        } else {
            /* 无数据，等待事件 */
            result = wait_for_rx_event(comm, timeout_remaining);
            
            /* 处理等待结果 */
            if (result != RT_EOK) {
                if (result == -RT_ETIMEOUT) {
                    LOG_D("Timeout after %d ms, received %d bytes", 
                         timeout - timeout_remaining, recv_total);
                    if (recv_total > 0) {
                        return recv_total;
                    }
                    comm->stats.rx_timeouts++;
                    return SERIAL_ERROR_TIMEOUT;
                } else {
                    LOG_E("Event receive error: %d", result);
                    return -RT_ERROR;
                }
            }
            
            /* 检查中断标志 */
            if (is_break_received(comm)) {
                LOG_D("Receive interrupted");
                return recv_total > 0 ? recv_total : -RT_EINTR;
            }
        }
        
        /* 更新剩余超时时间 */
        curr_tick = rt_tick_get();
        timeout_remaining = timeout - rt_tick_from_millisecond(curr_tick - start_tick);
    }
    
    return recv_total;
}

/**
 * 等待接收事件 (内部函数)
 * @return RT_EOK-成功, -RT_ETIMEOUT-超时, 其他-错误
 */
static rt_err_t wait_for_rx_event(serial_comm_t *comm, rt_int32_t timeout)
{
    rt_uint32_t recved = 0;
    rt_err_t result;
    
    /* 等待接收或中断事件 */
    result = rt_event_recv(comm->event, 
                          SERIAL_RX_EVENT | SERIAL_RX_BREAK_EVENT,
                          RT_EVENT_FLAG_OR | RT_EVENT_FLAG_CLEAR,
                          timeout, 
                          &recved);
    
    /* 触发了事件且需要保存接收标志 */
    if (result == RT_EOK) {
        comm->last_event_flag = recved;
    }
    
    return result;
}

/**
 * 检查是否收到中断请求 (内部函数)
 */
static rt_bool_t is_break_received(serial_comm_t *comm)
{
    return (comm->last_event_flag & SERIAL_RX_BREAK_EVENT) ? RT_TRUE : RT_FALSE;
}

/**
 * 无限等待接收实现 (内部函数)
 */
static int serial_recv_forever(serial_comm_t *comm, void *buffer, rt_size_t size)
{
    rt_size_t recv_total = 0;
    rt_size_t recv_len = 0;
    rt_err_t result;
    rt_uint8_t *buf = (rt_uint8_t *)buffer;
    
    while (recv_total < size) {
        /* 尝试读取数据 */
        recv_len = rt_device_read(comm->device, 0, buf + recv_total, size - recv_total);
        
        if (recv_len > 0) {
            recv_total += recv_len;
            
            /* 字节间隔超时处理 */
            if (recv_total < size && comm->byte_tmo > 0) {
                /* 等待下一个字节或超时 */
                result = wait_for_rx_event(comm, comm->byte_tmo);
                
                /* 字节间隔超时，返回已接收数据 */
                if (result == -RT_ETIMEOUT) {
                    LOG_D("Byte timeout after %d ms, received %d bytes", 
                          comm->byte_tmo, recv_total);
                    return recv_total;
                }
                
                /* 检查是否被中断 */
                if (result == RT_EOK && is_break_received(comm)) {
                    LOG_D("Receive interrupted");
                    return recv_total;
                }
            }
        } else {
            /* 等待有数据 */
            result = wait_for_rx_event(comm, RT_WAITING_FOREVER);
            
            /* 检查接收结果 */
            if (result != RT_EOK) {
                LOG_E("Event receive error: %d", result);
                return -RT_ERROR;
            }
            
            /* 检查中断标志 */
            if (is_break_received(comm)) {
                LOG_D("Receive interrupted");
                return recv_total > 0 ? recv_total : -RT_EINTR;
            }
        }
    }
    
    return recv_total;
}

/**
 * 注册DMA发送完成回调函数 (内部函数)
 */
rt_err_t serial_register_dma_tx_done(serial_comm_t *comm, void (*cb)(serial_comm_t *))
{
    /* 参数检查 */
    if (comm == RT_NULL || cb == RT_NULL) {
        return -RT_EINVAL;
    }
    
    /* 检查是否支持DMA */
    if (!comm->use_dma || !(comm->device->flag & RT_DEVICE_FLAG_DMA_TX)) {
        LOG_W("DMA not enabled or not supported for this device");
        return -RT_EINVAL;
    }
    
    /* 设置回调函数 */
    _dma_tx_done_cb = cb;
    
    LOG_D("Registered DMA TX done internal callback");
    return RT_EOK;
}

/**
 * DMA发送完成中断处理函数 (供HAL层调用)
 */
void serial_dma_tx_complete_isr(rt_device_t dev)
{
    serial_comm_t *comm = find_comm_by_device(dev);
    
    /* 找到对应通信实例并执行回调 */
    if (comm != RT_NULL && _dma_tx_done_cb != RT_NULL) {
        _dma_tx_done_cb(comm);
    }
}

/**
 * 通用串口发送函数
 */
int serial_send(serial_comm_t *comm, const void *buffer, int size)
{
    int ret;
    
    /* 参数检查 */
    SERIAL_CHECK_PARAM(comm != RT_NULL && buffer != RT_NULL && size > 0, -RT_EINVAL);
    SERIAL_CHECK_CONN(comm, SERIAL_ERROR_NOT_CONNECTED);
    
    /* 锁定设备 */
    SERIAL_LOCK(comm, -RT_ETIMEOUT);

    if (comm->type == SERIAL_TYPE_RS485) {
        /* 如果是RS485模式，先设置为发送模式 */
        RS485_TX_ENABLE(comm);
    }
    
    /* 发送数据 */
    ret = rt_device_write(comm->device, 0, buffer, size);

    if (comm->type == SERIAL_TYPE_RS485) {
        /* 如果是RS485模式，先设置为接收模式 */
        RS485_RX_ENABLE(comm);
    }
    
    /* 更新统计信息 */
    if (ret > 0) {
        comm->stats.tx_bytes += ret;
        comm->stats.tx_packets++;
    } else {
        comm->stats.tx_errors++;
    }
    
    SERIAL_UNLOCK(comm);
    return ret;
}

/**
 * 通用串口接收函数
 */
int serial_recv(serial_comm_t *comm, void *buffer, int size)
{
    /* 参数检查 */
    SERIAL_CHECK_PARAM(comm != RT_NULL && buffer != RT_NULL && size > 0, -RT_EINVAL);
    
    /* 使用默认超时接收 */
    return serial_recv_timeout(comm, buffer, size, comm->timeout);
}

/**
 * 使用指定超时时间接收数据
 */
int serial_recv_timeout(serial_comm_t *comm, void *buffer, int size, int timeout)
{
    int ret;
    
    /* 参数检查 */
    SERIAL_CHECK_PARAM(comm != RT_NULL && buffer != RT_NULL && size > 0, -RT_EINVAL);
    SERIAL_CHECK_PARAM(SERIAL_CHECK_TMO_RANGE(timeout), -RT_EINVAL);
    SERIAL_CHECK_CONN(comm, SERIAL_ERROR_NOT_CONNECTED);
    
    /* 锁定设备 */
    SERIAL_LOCK(comm, -RT_ETIMEOUT);
    
    /* 使用内部带超时接收函数 */
    ret = serial_recv_with_timeout(comm, buffer, size, timeout);
    
    /* 更新统计信息 */
    if (ret > 0) {
        comm->stats.rx_bytes += ret;
        comm->stats.rx_packets++;
    } else if (ret < 0) {
        comm->stats.rx_errors++;
    } else {
        comm->stats.rx_timeouts++;
    }
    
    SERIAL_UNLOCK(comm);
    return ret;
}

/**
 * 发送数据然后接收响应数据
 */
int serial_send_then_recv(serial_comm_t *comm, const void *send_buf, int send_len, 
                         void *recv_buf, int recv_size)
{
    /* 使用默认超时 */
    return serial_send_then_recv_timeout(comm, send_buf, send_len, recv_buf, recv_size, comm->timeout);
}

/**
 * 发送数据然后接收响应数据，使用指定超时
 */
int serial_send_then_recv_timeout(serial_comm_t *comm, const void *send_buf, int send_len, 
                                 void *recv_buf, int recv_size, int timeout)
{
    int send_ret, recv_ret;
    
    /* 参数检查 */
    if (comm == RT_NULL || send_buf == RT_NULL || send_len <= 0 || 
        recv_buf == RT_NULL || recv_size <= 0) {
        return -RT_EINVAL;
    }
    
    /* 检查超时范围 */
    if (!SERIAL_CHECK_TMO_RANGE(timeout)) {
        LOG_E("Invalid timeout %d", timeout);
        return -RT_EINVAL;
    }
    
    /* 检查设备状态 */
    if (comm->status != SERIAL_STATUS_CONNECTED) {
        LOG_E("Device not connected");
        return -SERIAL_ERROR_DEVICE;
    }
    
    /* 锁定设备 */
    if (rt_mutex_take(comm->lock, SERIAL_LOCK_TIMEOUT) != RT_EOK) {
        LOG_E("Failed to take mutex");
        return -RT_ETIMEOUT;
    }
    
    if (comm->type == SERIAL_TYPE_RS485) {
        /* 如果是RS485模式，先设置为发送模式 */
        RS485_TX_ENABLE(comm);
    }
    
    /* 发送数据 */
    send_ret = rt_device_write(comm->device, 0, send_buf, send_len);
    
    /* 更新发送统计信息 */
    if (send_ret > 0) {
        comm->stats.tx_bytes += send_ret;
        comm->stats.tx_packets++;
    } else {
        comm->stats.tx_errors++;
        rt_mutex_release(comm->lock);
        return send_ret;
    }
    /* 接收数据，使用指定的超时 */
    recv_ret = serial_recv_with_timeout(comm, recv_buf, recv_size, timeout);
    
    /* 更新接收统计信息 */
    if (recv_ret > 0) {
        comm->stats.rx_bytes += recv_ret;
        comm->stats.rx_packets++;
    } else if (recv_ret < 0) {
        comm->stats.rx_errors++;
    } else {
        comm->stats.rx_timeouts++;
    }
    
    rt_mutex_release(comm->lock);
    return recv_ret;
}

/**
 * 中断接收等待
 */
int serial_break_recv(serial_comm_t *comm)
{
    /* 参数检查 */
    if (comm == RT_NULL) {
        return -RT_EINVAL;
    }
    
    /* 触发接收中断事件 */
    return rt_event_send(comm->event, SERIAL_RX_BREAK_EVENT);
}


/**
 * 批量发送数据包
 */
int serial_send_packets(serial_comm_t *comm, const void **buf_array, 
                       const int *size_array, int packet_count)
{
    int total = 0;
    int ret = 0;
    int i;
    
    /* 参数检查 */
    if (comm == RT_NULL || buf_array == RT_NULL || size_array == RT_NULL || packet_count <= 0) {
        return -RT_EINVAL;
    }
    
    /* 检查设备状态 */
    if (comm->status != SERIAL_STATUS_CONNECTED) {
        LOG_E("Device not connected");
        return -SERIAL_ERROR_DEVICE;
    }
    
    /* 锁定设备 */
    if (rt_mutex_take(comm->lock, SERIAL_LOCK_TIMEOUT) != RT_EOK) {
        LOG_E("Failed to take mutex");
        return -RT_ETIMEOUT;
    }

    if (comm->type == SERIAL_TYPE_RS485) {
        /* 如果是RS485模式，先设置为发送模式 */
        RS485_TX_ENABLE(comm);
    }
    
    /* 依次发送每个数据包 */
    for (i = 0; i < packet_count; i++) {
        /* 检查参数有效性 */
        if (buf_array[i] == RT_NULL || size_array[i] <= 0) {
            continue;
        }
        
        /* 发送数据 */
        ret = rt_device_write(comm->device, 0, buf_array[i], size_array[i]);
        
        /* 更新统计信息 */
        if (ret > 0) {
            comm->stats.tx_bytes += ret;
            total += ret;
            comm->stats.tx_packets++;
        } else {
            comm->stats.tx_errors++;
            rt_mutex_release(comm->lock);
            return ret;
        }
    }
    
    if (comm->type == SERIAL_TYPE_RS485) {
        /* 如果是RS485模式，先设置为接收模式 */
        RS485_RX_ENABLE(comm);
    }
    
    rt_mutex_release(comm->lock);
    return total;
}

/**
 * 注册发送完成回调函数
 */
rt_err_t serial_register_tx_done(serial_comm_t *comm, serial_tx_done_callback callback)
{
    /* 参数检查 */
    if (comm == RT_NULL || callback == RT_NULL) {
        return -RT_EINVAL;
    }
    
    /* 锁定设备 */
    SERIAL_LOCK(comm, -RT_ETIMEOUT);
    
    /* 设置发送完成回调 */
    comm->tx_done_cb = callback;
    
    /* 解锁设备 */
    SERIAL_UNLOCK(comm);
    
    return RT_EOK;
}

int calc_byte_timeout(int baudrate)
{
    int tmo;
    
    /* 无效波特率处理 */
    if (baudrate <= 0) {
        return SERIAL_BYTE_TMO_MAX;
    }
    
    /* 计算超时：40位（起始位+数据位+校验位+停止位）传输时间(毫秒) */
    tmo = (40 * 1000) / baudrate;
    
    /* 确保字节间隔超时在有效范围内 */
    if (tmo < SERIAL_BYTE_TMO_MIN) {
        tmo = SERIAL_BYTE_TMO_MIN;
    } else if (tmo > SERIAL_BYTE_TMO_MAX) {
        tmo = SERIAL_BYTE_TMO_MAX;
    }
    
    return tmo;
}

/**
 * 设置通信参数 - 基于RT-Thread标准配置
 */
int serial_configure(serial_comm_t *comm, struct serial_configure *config)
{
    rt_bool_t is_connected = RT_FALSE;
    
    /* 参数检查 */
    SERIAL_CHECK_PARAM(comm != RT_NULL && config != RT_NULL, -RT_EINVAL);
    SERIAL_CHECK_PARAM(comm->serial_dev != RT_NULL, -RT_EINVAL);
    
    /* 记录原始连接状态 */
    is_connected = (comm->status == SERIAL_STATUS_CONNECTED);
    
    /* 如果已经连接，需要先断开 */
    if (is_connected) {
        LOG_D("Disconnecting before configuration");
        serial_disconnect(comm);
    }
    
    /* 锁定设备 */
    SERIAL_LOCK(comm, -RT_ETIMEOUT);
    
    /* 复制配置到通信实例的serial_dev中 */
    comm->serial_dev->config = *config;
    
    /* 自动计算字节间隔超时 */
    if (comm->byte_tmo == 0 || comm->type == SERIAL_TYPE_RS485) {
        comm->byte_tmo = calc_byte_timeout(config->baud_rate);
        LOG_D("Auto updated byte timeout to %d ms for baudrate %d", comm->byte_tmo, config->baud_rate);
    }
    
    /* 应用配置到RT-Thread串口设备 */
    if (comm->serial_dev->ops->configure) {
        if (comm->serial_dev->ops->configure(comm->serial_dev, config) != RT_EOK) {
            LOG_E("Failed to configure serial device");
            rt_mutex_release(comm->lock);
            return -RT_ERROR;
        }
    }
    
    rt_mutex_release(comm->lock);
    
    /* 如果之前是连接状态，需要重新连接 */
    if (is_connected) {
        LOG_D("Reconnecting after configuration");
        return serial_connect(comm);
    }
    
    return RT_EOK;
}

/**
 * 设置接收数据超时时间 - 直接操作serial_comm_t结构体
 */
int serial_set_recv_tmo(serial_comm_t *comm, int tmo_ms)
{
    /* 参数检查 */
    if (comm == RT_NULL) {
        return -RT_EINVAL;
    }
    
    /* 检查超时范围 */
    if (!SERIAL_CHECK_TMO_RANGE(tmo_ms)) {
        LOG_E("Invalid timeout %d", tmo_ms);
        return -RT_EINVAL;
    }
    
    /* 锁定设备 */
    if (rt_mutex_take(comm->lock, SERIAL_LOCK_TIMEOUT) != RT_EOK) {
        LOG_E("Failed to take mutex");
        return -RT_ETIMEOUT;
    }
    
    /* 直接设置超时 */
    comm->timeout = tmo_ms;
    
    rt_mutex_release(comm->lock);
    LOG_D("Set receive timeout to %d ms", tmo_ms);
    return RT_EOK;
}

/**
 * 设置字节间隔超时时间 - 直接操作serial_comm_t结构体
 */
int serial_set_byte_tmo(serial_comm_t *comm, int tmo_ms)
{
    /* 参数检查 */
    if (comm == RT_NULL) {
        return -RT_EINVAL;
    }
    
    /* 锁定设备 */
    if (rt_mutex_take(comm->lock, SERIAL_LOCK_TIMEOUT) != RT_EOK) {
        LOG_E("Failed to take mutex");
        return -RT_ETIMEOUT;
    }
    
    /* 检查超时范围 */
    if (tmo_ms != 0) {
        if (tmo_ms < SERIAL_BYTE_TMO_MIN) {
            LOG_W("Byte timeout %d ms too small, adjusted to %d ms", tmo_ms, SERIAL_BYTE_TMO_MIN);
            tmo_ms = SERIAL_BYTE_TMO_MIN;
        } else if (tmo_ms > SERIAL_BYTE_TMO_MAX) {
            LOG_W("Byte timeout %d ms too large, adjusted to %d ms", tmo_ms, SERIAL_BYTE_TMO_MAX);
            tmo_ms = SERIAL_BYTE_TMO_MAX;
        }
    }
    
    /* 直接设置超时 */
    if (tmo_ms == 0) {
        /* 自动计算超时，基于当前波特率 */
        comm->byte_tmo = calc_byte_timeout(SERIAL_GET_BAUDRATE(comm));
        LOG_D("Byte timeout set to auto calculation mode: %d ms", comm->byte_tmo);
    } else {
        comm->byte_tmo = tmo_ms;
        LOG_D("Set byte timeout to %d ms", tmo_ms);
    }
    
    rt_mutex_release(comm->lock);
    return RT_EOK;
}

/**
 * 设置延时参数 - 直接操作serial_comm_t结构体
 */
int serial_set_delay(serial_comm_t *comm, rt_uint32_t tx_delay_us, rt_uint32_t rx_delay_us)
{
    /* 参数检查 */
    if (comm == RT_NULL) {
        return -RT_EINVAL;
    }
    
    /* 锁定设备 */
    if (rt_mutex_take(comm->lock, SERIAL_LOCK_TIMEOUT) != RT_EOK) {
        LOG_E("Failed to take mutex");
        return -RT_ETIMEOUT;
    }
    
    /* 直接设置延时参数 */
    comm->tx_delay_us = tx_delay_us;
    comm->rx_delay_us = rx_delay_us;
    
    rt_mutex_release(comm->lock);
    LOG_D("Set TX delay to %u us, RX delay to %u us", tx_delay_us, rx_delay_us);
    return RT_EOK;
}

/**
 * 设置事件回调函数 - 直接操作serial_comm_t结构体
 */
int serial_set_event_cb(serial_comm_t *comm, serial_event_cb cb, void *user_data)
{
    /* 参数检查 */
    if (comm == RT_NULL) {
        return -RT_EINVAL;
    }
    
    /* 锁定设备 */
    if (rt_mutex_take(comm->lock, SERIAL_LOCK_TIMEOUT) != RT_EOK) {
        LOG_E("Failed to take mutex");
        return -RT_ETIMEOUT;
    }
    
    /* 直接设置回调函数和用户数据 */
    comm->event_cb = cb;
    comm->user_data = user_data;
    
    rt_mutex_release(comm->lock);
    LOG_D("Set event callback %s", cb ? "succeeded" : "cleared");
    return RT_EOK;
}

/**
 * 设置DMA发送完成回调函数 - 直接操作serial_comm_t结构体
 */
int serial_set_dma_tx_done_cb(serial_comm_t *comm, serial_dma_tx_done_cb cb, void *user_data)
{
    /* 参数检查 */
    if (comm == RT_NULL) {
        return -RT_EINVAL;
    }
    
    /* 检查是否支持DMA */
    if (!comm->use_dma || !(comm->device->flag & RT_DEVICE_FLAG_DMA_TX)) {
        LOG_W("DMA not enabled or not supported for this device");
        return -SERIAL_ERROR_NOT_SUPPORT;
    }
    
    /* 锁定设备 */
    if (rt_mutex_take(comm->lock, SERIAL_LOCK_TIMEOUT) != RT_EOK) {
        LOG_E("Failed to take mutex");
        return -RT_ETIMEOUT;
    }
    
    /* 直接设置DMA回调函数和用户数据 */
    comm->dma_tx_done_cb = cb;
    comm->dma_tx_done_data = user_data;
    
    rt_mutex_release(comm->lock);
    LOG_D("Set DMA TX done callback %s", cb ? "succeeded" : "cleared");
    return RT_EOK;
}

/* ========== 便利函数实现 ========== */

/**
 * 设置波特率（便利函数）
 */
int serial_set_baudrate(serial_comm_t *comm, rt_uint32_t baudrate)
{
    
    /* 参数检查 */
    if (comm == RT_NULL || baudrate == 0) {
        return -RT_EINVAL;
    }
    
    /* 获取当前配置并修改波特率 */
    SERIAL_SET_BAUDRATE(comm, baudrate);
    
    /* 应用新配置 */
    return serial_configure(comm, SERIAL_GET_CONFIG(comm));
}

/**
 * 设置数据格式（便利函数）
 */
int serial_set_format(serial_comm_t *comm, rt_uint32_t data_bits, 
                     rt_uint32_t parity, rt_uint32_t stop_bits)
{
    
    /* 参数检查 */
    if (comm == RT_NULL) {
        return -RT_EINVAL;
    }
    
    /* 设置数据格式 */
    SERIAL_SET_DATA_BITS(comm, data_bits);
    SERIAL_SET_PARITY(comm, parity);
    SERIAL_SET_STOP_BITS(comm, stop_bits);
    
    /* 应用新配置 */
    return serial_configure(comm, SERIAL_GET_CONFIG(comm));
}

/**
 * 设置通信参数（便利函数）
 */
int serial_set_config_data(serial_comm_t *comm, rt_uint32_t baudrate, 
                          rt_uint32_t data_bits, rt_uint32_t parity, rt_uint32_t stop_bits)
{
    
    /* 参数检查 */
    if (comm == RT_NULL) {
        return -RT_EINVAL;
    }
    
    /* 设置波特率 */
    SERIAL_SET_BAUDRATE(comm, baudrate);
    
    /* 设置数据格式 */
    SERIAL_SET_DATA_BITS(comm, data_bits);   
    SERIAL_SET_PARITY(comm, parity);
    SERIAL_SET_STOP_BITS(comm, stop_bits);
    
    /* 应用新配置 */        
    return serial_configure(comm, SERIAL_GET_CONFIG(comm)); 
}

/**
 * 初始化多串口配置
 */
static void init_serial_configs(void)
{
    /* 初始化串口1配置 */
    #ifdef BSP_USING_SERIAL1
    g_serial_manager.configs[0].device_name = BSP_SERIAL1_UART_NAME ;
    g_serial_manager.configs[0].type = SERIAL1_TYPE;
    g_serial_manager.configs[0].baudrate = BAUD_RATE_115200;
    g_serial_manager.configs[0].data_bits = DATA_BITS_8;
    g_serial_manager.configs[0].stop_bits = STOP_BITS_1;
    g_serial_manager.configs[0].parity = PARITY_NONE;
    g_serial_manager.configs[0].rx_buf_size = BSP_SERIAL1_RX_BUFSZ;
    g_serial_manager.configs[0].tx_buf_size = BSP_SERIAL1_TX_BUFSZ ;    
    if (SERIAL1_TYPE == SERIAL_TYPE_RS485) {
			g_serial_manager.configs[0].rs485_en_pin =  rt_pin_get(BSP_SERIAL1_RS485_EN_PIN);
      g_serial_manager.configs[0].tx_delay_us = BSP_SERIAL1_TX_DELAY_US ? BSP_SERIAL1_TX_DELAY_US : 10;
			g_serial_manager.configs[0].rx_delay_us = BSP_SERIAL1_RX_DELAY_US ? BSP_SERIAL1_RX_DELAY_US : 10;
    }
    g_serial_manager.configs[0].recv_timeout = SERIAL_RECV_TMO_DEFAULT;
    g_serial_manager.configs[0].byte_timeout = 0;
    g_serial_manager.configs[0].use_dma = RT_FALSE;
    
    #endif

    /* 初始化串口2配置 */
    #if SERIAL2_ENABLED
    g_serial_manager.configs[1].device_name = BSP_SERIAL2_UART_NAME ;
    g_serial_manager.configs[1].type = SERIAL2_TYPE;
    g_serial_manager.configs[1].baudrate = BAUD_RATE_115200;
    g_serial_manager.configs[1].data_bits = DATA_BITS_8;
    g_serial_manager.configs[1].stop_bits = STOP_BITS_1;
    g_serial_manager.configs[1].parity = PARITY_NONE;
    g_serial_manager.configs[1].rx_buf_size = BSP_SERIAL2_RX_BUFSZ;
    g_serial_manager.configs[1].tx_buf_size = BSP_SERIAL2_TX_BUFSZ;
    
    if (SERIAL2_TYPE == SERIAL_TYPE_RS485) {
        g_serial_manager.configs[1].rs485_en_pin =  rt_pin_get(BSP_SERIAL2_RS485_EN_PIN);
        g_serial_manager.configs[1].tx_delay_us = BSP_SERIAL2_TX_DELAY_US ? BSP_SERIAL2_TX_DELAY_US : 10;
        g_serial_manager.configs[1].rx_delay_us = BSP_SERIAL2_RX_DELAY_US ? BSP_SERIAL2_RX_DELAY_US : 10;
    }
    g_serial_manager.configs[1].recv_timeout = SERIAL_RECV_TMO_DEFAULT;
    g_serial_manager.configs[1].byte_timeout = 0;
    g_serial_manager.configs[1].use_dma = RT_FALSE;
    
    #endif

    /* 初始化串口3配置 */
    #ifdef BSP_USING_SERIAL3
    g_serial_manager.configs[2].device_name = BSP_SERIAL3_UART_NAME ;
    g_serial_manager.configs[2].type = SERIAL3_TYPE;
    g_serial_manager.configs[2].baudrate = BAUD_RATE_115200;
    g_serial_manager.configs[2].data_bits = DATA_BITS_8;
    g_serial_manager.configs[2].stop_bits = STOP_BITS_1;
    g_serial_manager.configs[2].parity = PARITY_NONE;
    g_serial_manager.configs[2].rx_buf_size = BSP_SERIAL3_RX_BUFSZ;
    g_serial_manager.configs[2].tx_buf_size = BSP_SERIAL3_TX_BUFSZ;
    /* RS485引脚处理：如果提供了引脚字符串则解析，否则不使用 */
   
    if (SERIAL3_TYPE == SERIAL_TYPE_RS485) {
         g_serial_manager.configs[2].rs485_en_pin =  rt_pin_get(BSP_SERIAL3_RS485_EN_PIN);
        g_serial_manager.configs[2].tx_delay_us = BSP_SERIAL3_TX_DELAY_US ? BSP_SERIAL3_TX_DELAY_US : 10;
        g_serial_manager.configs[2].rx_delay_us = BSP_SERIAL3_RX_DELAY_US ? BSP_SERIAL3_RX_DELAY_US : 10;
    }
    g_serial_manager.configs[2].recv_timeout = SERIAL_RECV_TMO_DEFAULT;
    g_serial_manager.configs[2].byte_timeout = 0;
    g_serial_manager.configs[2].use_dma = RT_FALSE;
    #endif

    /* 初始化串口4配置 */
    #ifdef BSP_USING_SERIAL4
    g_serial_manager.configs[3].device_name = BSP_SERIAL4_UART_NAME ;
    g_serial_manager.configs[3].type = SERIAL4_TYPE;
    g_serial_manager.configs[3].baudrate = BAUD_RATE_115200;
    g_serial_manager.configs[3].data_bits = DATA_BITS_8;
    g_serial_manager.configs[3].stop_bits = STOP_BITS_1;
    g_serial_manager.configs[3].parity = PARITY_NONE;
    g_serial_manager.configs[3].rx_buf_size = BSP_SERIAL4_RX_BUFSZ;
    g_serial_manager.configs[3].tx_buf_size = BSP_SERIAL4_TX_BUFSZ;
    /* RS485引脚处理：如果提供了引脚字符串则解析，否则不使用 */
    
    if (SERIAL4_TYPE == SERIAL_TYPE_RS485) {
        g_serial_manager.configs[3].rs485_en_pin =  rt_pin_get(BSP_SERIAL4_RS485_EN_PIN);
        g_serial_manager.configs[3].tx_delay_us = BSP_SERIAL4_TX_DELAY_US ? BSP_SERIAL4_TX_DELAY_US : 10;
        g_serial_manager.configs[3].rx_delay_us = BSP_SERIAL4_RX_DELAY_US ? BSP_SERIAL4_RX_DELAY_US : 10;
    }
    g_serial_manager.configs[3].recv_timeout = SERIAL_RECV_TMO_DEFAULT;
    g_serial_manager.configs[3].byte_timeout = 0;
    g_serial_manager.configs[3].use_dma = RT_FALSE;
    #endif
}

/**
 * 初始化多串口管理器
 */
int serial_multi_init(void)
{
    int i;
    
    /* 检查是否已初始化 */
    if (g_serial_manager.initialized) {
        LOG_W("Serial multi manager already initialized");
        return RT_EOK;
    }
    
    /* 清空管理器 */
    rt_memset(&g_serial_manager, 0, sizeof(g_serial_manager));
    
    /* 初始化配置 */
    init_serial_configs();
    
    /* 创建已启用的串口实例 */
    for (i = 0; i < BSP_SERIAL_NUMBER; i++) {
        /* 检查配置是否有效（device_name不为空表示已启用） */
        if (g_serial_manager.configs[i].device_name != RT_NULL) {
            if (serial_multi_create_instance((serial_index_t)i) != RT_EOK) {
                LOG_E("Failed to create serial instance %d", i + 1);
                /* 继续创建其他实例，不直接返回错误 */
            }
        }
    }
    
    /* 设置初始化标志 */
    g_serial_manager.initialized = 1;
    
    LOG_I("Serial multi manager initialized");
    return RT_EOK;
}

/**
 * 反初始化多串口管理器
 */
int serial_multi_deinit(void)
{
    int i;
    
    /* 检查是否已初始化 */
    if (!g_serial_manager.initialized) {
        LOG_W("Serial multi manager not initialized");
        return RT_EOK;
    }
    
    /* 销毁所有实例 */
    for (i = 0; i < BSP_SERIAL_NUMBER; i++) {
        if (g_serial_manager.instances[i] != RT_NULL) {
            serial_multi_destroy_instance((serial_index_t)i);
        }
    }
    
    /* 清空管理器 */
    rt_memset(&g_serial_manager, 0, sizeof(g_serial_manager));
    
    LOG_I("Serial multi manager deinitialized");
    return RT_EOK;
}

/**
 * 获取指定索引的串口实例
 */
serial_comm_t *serial_multi_get_instance(serial_index_t index)
{
    /* 参数检查 */
    if (index >= BSP_SERIAL_NUMBER) {
        LOG_E("Invalid serial index %d", index);
        return RT_NULL;
    }
    
    /* 检查管理器是否初始化 */
    if (!g_serial_manager.initialized) {
        LOG_E("Serial multi manager not initialized");
        return RT_NULL;
    }
    
    return g_serial_manager.instances[index];
}

/**
 * 根据UART名称获取串口实例
 */
serial_comm_t *serial_multi_get_by_name(const char *uart_name)
{
    int i;
    
    /* 参数检查 */
    if (uart_name == RT_NULL) {
        return RT_NULL;
    }
    
    /* 检查管理器是否初始化 */
    if (!g_serial_manager.initialized) {
        LOG_E("Serial multi manager not initialized");
        return RT_NULL;
    }
    
    /* 遍历查找匹配的UART名称 */
    for (i = 0; i < BSP_SERIAL_NUMBER; i++) {
        if (g_serial_manager.configs[i].device_name != RT_NULL &&
            rt_strcmp(g_serial_manager.configs[i].device_name, uart_name) == 0) {
            return g_serial_manager.instances[i];
        }
    }
    
    LOG_W("Serial instance with UART name '%s' not found", uart_name);
    return RT_NULL;
}

/**
 * 创建指定索引的串口实例
 */
int serial_multi_create_instance(serial_index_t index)
{
    serial_init_param_t *param;
    
    /* 参数检查 */
    if (index >= BSP_SERIAL_NUMBER) {
        LOG_E("Invalid serial index %d", index);
        return -RT_EINVAL;
    }
    
    /* 获取配置信息 */
    param = &g_serial_manager.configs[index];
    if (param->device_name == RT_NULL) {
        LOG_W("Serial %d is not enabled", index + 1);
        return -RT_EINVAL;
    }
    
    /* 检查是否已创建 */
    if (g_serial_manager.instances[index] != RT_NULL) {
        LOG_W("Serial %d instance already exists", index + 1);
        return RT_EOK;
    }
    
    /* 直接使用存储的参数创建实例 */
    g_serial_manager.instances[index] = serial_create(param);
    if (g_serial_manager.instances[index] == RT_NULL) {
        LOG_E("Failed to create serial %d instance", index + 1);
        return -RT_ERROR;
    }
    
    /* 自动连接串口 */
    if (serial_connect(g_serial_manager.instances[index]) != RT_EOK) {
        LOG_E("Failed to connect serial %d", index + 1);
        serial_destroy(g_serial_manager.instances[index]);
        g_serial_manager.instances[index] = RT_NULL;
        return -RT_ERROR;
    }
    
    LOG_I("Created serial %d instance: %s (%s)", index + 1, 
          param->device_name, 
          param->type == SERIAL_TYPE_RS485 ? "RS485" : 
          param->type == SERIAL_TYPE_RS232 ? "RS232" : "TTL");
    
    return RT_EOK;
}

/**
 * 销毁指定索引的串口实例
 */
int serial_multi_destroy_instance(serial_index_t index)
{
    /* 参数检查 */
    if (index >= BSP_SERIAL_NUMBER) {
        LOG_E("Invalid serial index %d", index);
        return -RT_EINVAL;
    }
    
    /* 检查实例是否存在 */
    if (g_serial_manager.instances[index] == RT_NULL) {
        LOG_W("Serial %d instance does not exist", index + 1);
        return RT_EOK;
    }
    
    /* 销毁实例 */
    serial_destroy(g_serial_manager.instances[index]);
    g_serial_manager.instances[index] = RT_NULL;
    
    LOG_I("Destroyed serial %d instance", index + 1);
    return RT_EOK;
}

/**
 * 连接指定索引的串口
 */
int serial_multi_connect(serial_index_t index)
{
    /* 参数检查 */
    if (index >= BSP_SERIAL_NUMBER) {
        LOG_E("Invalid serial index %d", index);
        return -RT_EINVAL;
    }
    
    /* 检查实例是否存在 */
    if (g_serial_manager.instances[index] == RT_NULL) {
        LOG_E("Serial %d instance does not exist", index + 1);
        return -RT_EINVAL;
    }
    
    return serial_connect(g_serial_manager.instances[index]);
}

/**
 * 断开指定索引的串口
 */
int serial_multi_disconnect(serial_index_t index)
{
    /* 参数检查 */
    if (index >= BSP_SERIAL_NUMBER) {
        LOG_E("Invalid serial index %d", index);
        return -RT_EINVAL;
    }
    
    /* 检查实例是否存在 */
    if (g_serial_manager.instances[index] == RT_NULL) {
        LOG_E("Serial %d instance does not exist", index + 1);
        return -RT_EINVAL;
    }
    
    return serial_disconnect(g_serial_manager.instances[index]);
}

/**
 * 获取多串口管理器状态
 */
int serial_multi_is_initialized(void)
{
    return g_serial_manager.initialized;
}

#if SERIAL_FRAMEWORK_ENABLED
/* 系统启动时自动初始化多串口管理器 */
INIT_DEVICE_EXPORT(serial_multi_init);
#endif 

/**
 * 获取统计信息
 */
int serial_get_stats(serial_comm_t *comm, serial_stats_t *stats)
{
    /* 参数检查 */
    SERIAL_CHECK_PARAM(comm != RT_NULL && stats != RT_NULL, -RT_EINVAL);
    
    /* 锁定设备 */
    SERIAL_LOCK(comm, -RT_ETIMEOUT);
    
    /* 复制统计信息 */
    *stats = comm->stats;
    
    SERIAL_UNLOCK(comm);
    return RT_EOK;
}

/**
 * 清除统计信息
 */
int serial_clear_stats(serial_comm_t *comm)
{
    /* 参数检查 */
    SERIAL_CHECK_PARAM(comm != RT_NULL, -RT_EINVAL);
    
    /* 锁定设备 */
    SERIAL_LOCK(comm, -RT_ETIMEOUT);
    
    /* 清零统计信息 */
    rt_memset(&comm->stats, 0, sizeof(serial_stats_t));
    
    SERIAL_UNLOCK(comm);
    LOG_D("Statistics cleared");
    return RT_EOK;
}

/**
 * 获取设备名称
 */
const char *serial_get_device_name(serial_comm_t *comm)
{
    /* 参数检查 */
    if (comm == RT_NULL || comm->device == RT_NULL) {
        return RT_NULL;
    }
    
    return comm->device->parent.name;
}

/**
 * 获取设备类型
 */
int serial_get_type(serial_comm_t *comm)
{
    /* 参数检查 */
    if (comm == RT_NULL) {
        return -RT_EINVAL;
    }
    
    return (int)comm->type;
}

/**
 * 获取设备连接状态
 */
int serial_get_status(serial_comm_t *comm)
{
    /* 参数检查 */
    if (comm == RT_NULL) {
        return -RT_EINVAL;
    }
    
    return (int)comm->status;
}

/**
 * 检查设备是否支持DMA
 */
int serial_is_dma_support(serial_comm_t *comm)
{
    /* 参数检查 */
    if (comm == RT_NULL || comm->device == RT_NULL) {
        return -RT_EINVAL;
    }
    
    /* 检查设备标志中是否支持DMA */
    if (comm->device->flag & RT_DEVICE_FLAG_DMA_TX) {
        return 1; /* 支持DMA */
    }
    
    return 0; /* 不支持DMA */
}

/**
 * 打印统计信息
 */
int serial_print_stats(serial_comm_t *comm)
{
    serial_stats_t stats;
    int ret;
    
    /* 参数检查 */
    if (comm == RT_NULL) {
        return -RT_EINVAL;
    }
    
    /* 获取统计信息 */
    ret = serial_get_stats(comm, &stats);
    if (ret != RT_EOK) {
        return ret;
    }
    
    /* 打印统计信息 */
    rt_kprintf("\n=== Serial Communication Statistics ===\n");
    rt_kprintf("Device: %s\n", serial_get_device_name(comm));
    rt_kprintf("Type: %s\n", 
              comm->type == SERIAL_TYPE_RS485 ? "RS485" :
              comm->type == SERIAL_TYPE_RS232 ? "RS232" : "TTL");
    rt_kprintf("Status: %s\n",
              comm->status == SERIAL_STATUS_CONNECTED ? "Connected" :
              comm->status == SERIAL_STATUS_DISCONNECTED ? "Disconnected" : 
              comm->status == SERIAL_STATUS_INITIALIZED ? "Initialized" : "Closed");
    rt_kprintf("\n--- Transmission Statistics ---\n");
    rt_kprintf("TX Bytes:    %u\n", stats.tx_bytes);
    rt_kprintf("TX Packets:  %u\n", stats.tx_packets);
    rt_kprintf("TX Errors:   %u\n", stats.tx_errors);
    rt_kprintf("\n--- Reception Statistics ---\n");
    rt_kprintf("RX Bytes:    %u\n", stats.rx_bytes);
    rt_kprintf("RX Packets:  %u\n", stats.rx_packets);
    rt_kprintf("RX Errors:   %u\n", stats.rx_errors);
    rt_kprintf("RX Timeouts: %u\n", stats.rx_timeouts);
    rt_kprintf("Overflow Errors: %u\n", stats.overflow_errors);
    rt_kprintf("=====================================\n\n");
    
    return RT_EOK;
}

/**
 * 获取指定索引串口的统计信息
 */
int serial_multi_get_stats(serial_index_t index, serial_stats_t *stats)
{
    serial_comm_t *comm;
    
    /* 参数检查 */
    if (index >= BSP_SERIAL_NUMBER || stats == RT_NULL) {
        return -RT_EINVAL;
    }
    
    /* 获取串口实例 */
    comm = serial_multi_get_instance(index);
    if (comm == RT_NULL) {
        return -RT_EINVAL;
    }
    
    /* 获取统计信息 */
    return serial_get_stats(comm, stats);
}

/**
 * 清除指定索引串口的统计信息
 */
int serial_multi_clear_stats(serial_index_t index)
{
    serial_comm_t *comm;
    
    /* 参数检查 */
    if (index >= BSP_SERIAL_NUMBER) {
        return -RT_EINVAL;
    }
    
    /* 获取串口实例 */
    comm = serial_multi_get_instance(index);
    if (comm == RT_NULL) {
        return -RT_EINVAL;
    }
    
    /* 清除统计信息 */
    return serial_clear_stats(comm);
}

/**
 * 打印指定索引串口的统计信息
 */
int serial_multi_print_stats(serial_index_t index)
{
    serial_comm_t *comm;
    
    /* 参数检查 */
    if (index >= BSP_SERIAL_NUMBER) {
        return -RT_EINVAL;
    }
    
    /* 获取串口实例 */
    comm = serial_multi_get_instance(index);
    if (comm == RT_NULL) {
        return -RT_EINVAL;
    }
    
    /* 打印统计信息 */
    rt_kprintf("=== Serial %d Statistics ===\n", index + 1);
    return serial_print_stats(comm);
}

/**
 * 打印所有串口的统计信息
 */
int serial_multi_print_all_stats(void)
{
    int i;
    rt_bool_t has_stats = RT_FALSE;
    
    /* 检查管理器是否初始化 */
    if (!g_serial_manager.initialized) {
        rt_kprintf("Serial multi manager not initialized\n");
        return -RT_ERROR;
    }
    
    rt_kprintf("\n=== All Serial Communication Statistics ===\n");
    
    /* 遍历所有串口实例 */
    for (i = 0; i < BSP_SERIAL_NUMBER; i++) {
        if (g_serial_manager.instances[i] != RT_NULL) {
            rt_kprintf("\n--- Serial %d ---\n", i + 1);
            serial_print_stats(g_serial_manager.instances[i]);
            has_stats = RT_TRUE;
        }
    }
    
    if (!has_stats) {
        rt_kprintf("No active serial instances found.\n");
    }
    
    rt_kprintf("===========================================\n\n");
    
    return RT_EOK;
}

#ifdef RT_USING_FINSH
#include <finsh.h>

/**
 * 显示串口统计信息Shell命令
 */
static int cmd_serial_stats(int argc, char **argv)
{
    int index;
    
    if (argc == 1) {
        /* 显示所有串口统计信息 */
        return serial_multi_print_all_stats();
    } else if (argc == 2) {
        /* 显示指定串口统计信息 */
        index = strtol(argv[1], RT_NULL, 10);
        if (index < 1 || index > BSP_SERIAL_NUMBER) {
            rt_kprintf("Invalid serial index: %d (valid range: 1-%d)\n", 
                       index, BSP_SERIAL_NUMBER);
            return -RT_EINVAL;
        }
        return serial_multi_print_stats((serial_index_t)(index - 1));
    } else {
        rt_kprintf("Usage: serial_stats [serial_number]\n");
        rt_kprintf("  serial_number: 1-%d, if not specified, show all\n", BSP_SERIAL_NUMBER);
        return -RT_EINVAL;
    }
}

/**
 * 清除串口统计信息Shell命令
 */
static int cmd_serial_clear_stats(int argc, char **argv)
{
    int index;
    int i;
    
    if (argc == 1) {
        /* 清除所有串口统计信息 */
        rt_kprintf("Clearing all serial statistics...\n");
        for (i = 0; i < BSP_SERIAL_NUMBER; i++) {
            if (g_serial_manager.instances[i] != RT_NULL) {
                serial_multi_clear_stats((serial_index_t)i);
                rt_kprintf("Serial %d statistics cleared\n", i + 1);
            }
        }
        rt_kprintf("All serial statistics cleared\n");
        return RT_EOK;
    } else if (argc == 2) {
        /* 清除指定串口统计信息 */
        index = strtol(argv[1], RT_NULL, 10);
        if (index < 1 || index > BSP_SERIAL_NUMBER) {
            rt_kprintf("Invalid serial index: %d (valid range: 1-%d)\n", 
                       index, BSP_SERIAL_NUMBER);
            return -RT_EINVAL;
        }
        
        if (serial_multi_clear_stats((serial_index_t)(index - 1)) == RT_EOK) {
            rt_kprintf("Serial %d statistics cleared\n", index);
        } else {
            rt_kprintf("Failed to clear serial %d statistics\n", index);
        }
        return RT_EOK;
    } else {
        rt_kprintf("Usage: serial_clear_stats [serial_number]\n");
        rt_kprintf("  serial_number: 1-%d, if not specified, clear all\n", BSP_SERIAL_NUMBER);
        return -RT_EINVAL;
    }
}

/**
 * 串口管理器状态Shell命令
 */
static int cmd_serial_status(int argc, char **argv)
{
    int i;
    serial_comm_t *comm;
    const char *status_str;
    const char *type_str;
    
    rt_kprintf("\n=== Serial Communication Manager Status ===\n");
    rt_kprintf("Initialized: %s\n", g_serial_manager.initialized ? "Yes" : "No");
    rt_kprintf("Max Serial Devices: %d\n", BSP_SERIAL_NUMBER);
    
    rt_kprintf("\n--- Serial Instances ---\n");
    for (i = 0; i < BSP_SERIAL_NUMBER; i++) {
        comm = g_serial_manager.instances[i];
        
        if (comm != RT_NULL) {
            /* 获取状态字符串 */
            switch (comm->status) {
                case SERIAL_STATUS_CONNECTED:
                    status_str = "Connected";
                    break;
                case SERIAL_STATUS_DISCONNECTED:
                    status_str = "Disconnected";
                    break;
                case SERIAL_STATUS_INITIALIZED:
                    status_str = "Initialized";
                    break;
                case SERIAL_STATUS_CLOSED:
                    status_str = "Closed";
                    break;
                default:
                    status_str = "Unknown";
                    break;
            }
            
            /* 获取类型字符串 */
            switch (comm->type) {
                case SERIAL_TYPE_RS485:
                    type_str = "RS485";
                    break;
                case SERIAL_TYPE_RS232:
                    type_str = "RS232";
                    break;
                case SERIAL_TYPE_TTL:
                    type_str = "TTL";
                    break;
                default:
                    type_str = "Unknown";
                    break;
            }
            
            rt_kprintf("Serial %d: %s (%s) - %s - %d bps\n", 
                       i + 1, 
                       serial_get_device_name(comm),
                       type_str,
                       status_str,
                       SERIAL_GET_BAUDRATE(comm));
        } else {
            rt_kprintf("Serial %d: Not initialized\n", i + 1);
        }
    }
    
    rt_kprintf("==========================================\n\n");
    return RT_EOK;
}

/* 注册Shell命令 */
MSH_CMD_EXPORT_ALIAS(cmd_serial_stats, serial_stats, Show serial communication statistics);
MSH_CMD_EXPORT_ALIAS(cmd_serial_clear_stats, serial_clear_stats, Clear serial communication statistics);
MSH_CMD_EXPORT_ALIAS(cmd_serial_status, serial_status, Show serial communication status);

#endif /* RT_USING_FINSH */

/**
 * 获取指定索引的串口配置参数
 */
const serial_init_param_t *serial_multi_get_config(serial_index_t index)
{
    /* 参数检查 */
    if (index >= BSP_SERIAL_NUMBER) {
        LOG_E("Invalid serial index %d", index);
        return RT_NULL;
    }
    
    /* 检查管理器是否初始化 */
    if (!g_serial_manager.initialized) {
        LOG_E("Serial multi manager not initialized");
        return RT_NULL;
    }
    
    /* 检查配置是否有效 */
    if (g_serial_manager.configs[index].device_name == RT_NULL) {
        LOG_W("Serial %d is not enabled", index + 1);
        return RT_NULL;
    }
    
    return &g_serial_manager.configs[index];
}


