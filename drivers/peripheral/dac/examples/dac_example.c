/*
 * Copyright (c) 2024, Xi'an Helon-Electric Co., Ltd.
 *
 * Author: kevin<PERSON><PERSON><PERSON>
 *
 * CONFIDENTIAL AND PROPRIETARY
 * 严格保密，泄露必究
 *
 * Change Logs:
 * Date           Author       Notes
 * 2024-07-05     kevin.zhao   first version
 */

#include <rtthread.h>
#include <rtdevice.h>
#include <math.h>

/* 定义相关宏 */
#define DAC_DEV_NAME        "dac1"       /* DAC设备名称 */
#define DAC_CHANNEL         1            /* DAC通道 */
#define RESOLUTION          (1 << 12)    /* 12位DAC, 分辨率4096 */
#define VREF                3300         /* 参考电压，单位mV */
#define PI                  3.14159265358979323846

/* 定义线程堆栈大小和优先级 */
#define THREAD_STACK_SIZE   1024
#define THREAD_PRIORITY     10
#define THREAD_TIMESLICE    10

/* 波形类型定义 */
#define WAVE_SINE           0
#define WAVE_SQUARE         1
#define WAVE_TRIANGLE       2
#define WAVE_SAWTOOTH       3

/* 全局变量 */
static rt_dac_device_t dac_dev = RT_NULL;
static rt_mutex_t dac_mutex = RT_NULL;
static rt_uint16_t sine_table[256];
static rt_uint16_t *wave_buffer = RT_NULL;
static rt_bool_t dac_running = RT_FALSE;

/**
 * @brief   初始化正弦波表
 * @note    预计算正弦波以提高性能
 */
static void init_sine_table(void)
{
    for (int i = 0; i < 256; i++)
    {
        /* 计算正弦值并转换到DAC值范围 */
        float sine_val = sinf(2 * PI * i / 256);
        sine_table[i] = (rt_uint16_t)((sine_val + 1.0f) * RESOLUTION / 2);
    }
    rt_kprintf("Sine table initialized\n");
}

/**
 * @brief   校准DAC输出值
 * @param   raw_value - 原始DAC值
 * @return  校准后的DAC值
 */
static rt_uint16_t calibrate_dac_value(rt_uint16_t raw_value)
{
    /* 示例校准参数，实际应通过校准实验获取 */
    float gain = 0.995f;
    float offset = 5.0f;
    
    /* 应用校准 */
    float calibrated = raw_value * gain + offset;
    
    /* 确保在有效范围内 */
    if (calibrated < 0) calibrated = 0;
    if (calibrated > RESOLUTION - 1) calibrated = RESOLUTION - 1;
    
    return (rt_uint16_t)calibrated;
}

/**
 * @brief   将电压值转换为DAC值
 * @param   voltage - 目标电压值(mV)
 * @return  对应的DAC值
 */
static rt_uint16_t voltage_to_dac(rt_uint32_t voltage)
{
    /* 确保电压不超过参考电压 */
    if (voltage > VREF)
    {
        voltage = VREF;
    }
    
    /* 计算对应的DAC值 */
    rt_uint16_t value = (rt_uint16_t)(voltage * RESOLUTION / VREF);
    return value;
}

/**
 * @brief   输出特定电压
 * @param   dac_dev - DAC设备
 * @param   channel - DAC通道
 * @param   voltage - 目标电压(mV)
 */
static rt_err_t output_voltage(rt_dac_device_t dac_dev, rt_uint8_t channel, rt_uint32_t voltage)
{
    rt_err_t ret;
    rt_uint16_t value;
    
    /* 电压转换为DAC值 */
    value = voltage_to_dac(voltage);
    
    /* 应用校准 */
    value = calibrate_dac_value(value);
    
    /* 获取互斥量 */
    if (dac_mutex != RT_NULL)
    {
        ret = rt_mutex_take(dac_mutex, RT_WAITING_FOREVER);
        if (ret != RT_EOK)
        {
            rt_kprintf("Take DAC mutex failed: %d\n", ret);
            return ret;
        }
    }
    
    /* 写入DAC通道 */
    ret = rt_dac_write(dac_dev, channel, value);
    
    /* 释放互斥量 */
    if (dac_mutex != RT_NULL)
    {
        rt_mutex_release(dac_mutex);
    }
    
    if (ret == RT_EOK)
    {
        rt_kprintf("Output voltage: %d mV (DAC value: %d)\n", voltage, value);
    }
    else
    {
        rt_kprintf("Output voltage failed: %d\n", ret);
    }
    
    return ret;
}

/**
 * @brief   生成特定波形数据
 * @param   wave_type - 波形类型
 * @param   buffer - 输出缓冲区
 * @param   size - 缓冲区大小
 * @param   amplitude - 幅度(0-100%)
 * @param   offset - 偏移量(0-100%)
 */
static void generate_wave(rt_uint8_t wave_type, rt_uint16_t *buffer, rt_uint16_t size,
                         rt_uint8_t amplitude, rt_uint8_t offset)
{
    rt_uint16_t i;
    float amp = amplitude / 100.0f;
    float off = offset / 100.0f;
    rt_uint16_t mid_value = RESOLUTION / 2;
    rt_uint16_t max_amp = (rt_uint16_t)(mid_value * amp);
    rt_uint16_t off_value = (rt_uint16_t)(RESOLUTION * off);
    
    switch (wave_type)
    {
    case WAVE_SINE:  /* 正弦波 */
        for (i = 0; i < size; i++)
        {
            float angle = 2 * PI * i / size;
            buffer[i] = (rt_uint16_t)(mid_value + sinf(angle) * max_amp + off_value);
            if (buffer[i] >= RESOLUTION) buffer[i] = RESOLUTION - 1;
        }
        break;
        
    case WAVE_SQUARE:  /* 方波 */
        for (i = 0; i < size; i++)
        {
            if (i < size / 2)
                buffer[i] = mid_value + max_amp + off_value;
            else
                buffer[i] = mid_value - max_amp + off_value;
                
            if (buffer[i] >= RESOLUTION) buffer[i] = RESOLUTION - 1;
        }
        break;
        
    case WAVE_TRIANGLE:  /* 三角波 */
        for (i = 0; i < size; i++)
        {
            if (i < size / 2)
                buffer[i] = mid_value - max_amp + (i * 4 * max_amp / size) + off_value;
            else
                buffer[i] = mid_value + max_amp - ((i - size / 2) * 4 * max_amp / size) + off_value;
                
            if (buffer[i] >= RESOLUTION) buffer[i] = RESOLUTION - 1;
        }
        break;
        
    case WAVE_SAWTOOTH:  /* 锯齿波 */
        for (i = 0; i < size; i++)
        {
            buffer[i] = mid_value - max_amp + (i * 2 * max_amp / size) + off_value;
            if (buffer[i] >= RESOLUTION) buffer[i] = RESOLUTION - 1;
        }
        break;
        
    default:  /* 默认输出直流 */
        for (i = 0; i < size; i++)
        {
            buffer[i] = mid_value + off_value;
            if (buffer[i] >= RESOLUTION) buffer[i] = RESOLUTION - 1;
        }
    }
}

/**
 * @brief   波形输出线程
 * @param   parameter - 线程参数
 */
static void dac_wave_thread_entry(void *parameter)
{
    rt_err_t ret;
    rt_uint8_t wave_type = WAVE_SINE;
    rt_uint16_t size = 256;
    rt_uint16_t index = 0;
    rt_uint32_t period_ms = 20;  /* 波形周期(ms) */
    
    /* 分配波形缓冲区 */
    wave_buffer = (rt_uint16_t *)rt_malloc(sizeof(rt_uint16_t) * size);
    if (wave_buffer == RT_NULL)
    {
        rt_kprintf("Allocate wave buffer failed\n");
        return;
    }
    
    /* 生成初始波形 */
    generate_wave(wave_type, wave_buffer, size, 80, 50);
    rt_kprintf("Wave buffer initialized, type: %d\n", wave_type);
    
    dac_running = RT_TRUE;
    
    /* 波形输出主循环 */
    while (dac_running)
    {
        /* 获取互斥量 */
        if (dac_mutex != RT_NULL)
        {
            ret = rt_mutex_take(dac_mutex, RT_WAITING_FOREVER);
            if (ret != RT_EOK)
            {
                rt_thread_mdelay(1);
                continue;
            }
        }
        
        /* 写入DAC通道 */
        rt_uint16_t value = calibrate_dac_value(wave_buffer[index]);
        ret = rt_dac_write(dac_dev, DAC_CHANNEL, value);
        
        /* 释放互斥量 */
        if (dac_mutex != RT_NULL)
        {
            rt_mutex_release(dac_mutex);
        }
        
        /* 更新索引 */
        index = (index + 1) % size;
        
        /* 周期控制 */
        rt_thread_mdelay(period_ms / size);
    }
    
    /* 释放资源 */
    if (wave_buffer != RT_NULL)
    {
        rt_free(wave_buffer);
        wave_buffer = RT_NULL;
    }
}

/**
 * @brief   错误恢复函数
 * @param   dac_dev - DAC设备
 * @param   channel - DAC通道
 * @return  操作结果
 */
static rt_err_t dac_recovery(rt_dac_device_t dac_dev, rt_uint8_t channel)
{
    rt_err_t ret;
    
    /* 先关闭通道 */
    rt_dac_disable(dac_dev, channel);
    
    /* 延时一段时间 */
    rt_thread_mdelay(10);
    
    /* 重新使能通道 */
    ret = rt_dac_enable(dac_dev, channel);
    if (ret != RT_EOK)
    {
        rt_kprintf("DAC channel recovery failed: %d\n", ret);
        return ret;
    }
    
    rt_kprintf("DAC channel recovered successfully\n");
    return RT_EOK;
}

/**
 * @brief   DAC设备初始化
 * @return  操作结果
 */
static rt_err_t dac_device_init(void)
{
    rt_err_t ret;
    
    /* 查找DAC设备 */
    dac_dev = (rt_dac_device_t)rt_device_find(DAC_DEV_NAME);
    if (dac_dev == RT_NULL)
    {
        rt_kprintf("Cannot find %s device!\n", DAC_DEV_NAME);
        return -RT_ERROR;
    }
    
    /* 使能DAC通道 */
    ret = rt_dac_enable(dac_dev, DAC_CHANNEL);
    if (ret != RT_EOK)
    {
        rt_kprintf("Enable DAC channel %d failed: %d\n", DAC_CHANNEL, ret);
        return ret;
    }
    
    /* 创建互斥量 */
    dac_mutex = rt_mutex_create("dac_mutex", RT_IPC_FLAG_FIFO);
    if (dac_mutex == RT_NULL)
    {
        rt_kprintf("Create DAC mutex failed\n");
        rt_dac_disable(dac_dev, DAC_CHANNEL);
        return -RT_ERROR;
    }
    
    /* 初始化正弦波表 */
    init_sine_table();
    
    rt_kprintf("DAC device initialized successfully\n");
    return RT_EOK;
}

/**
 * @brief   DAC设备反初始化
 * @return  操作结果
 */
static rt_err_t dac_device_deinit(void)
{
    /* 停止波形输出 */
    dac_running = RT_FALSE;
    rt_thread_mdelay(100);
    
    /* 关闭DAC通道 */
    if (dac_dev != RT_NULL)
    {
        rt_dac_disable(dac_dev, DAC_CHANNEL);
    }
    
    /* 删除互斥量 */
    if (dac_mutex != RT_NULL)
    {
        rt_mutex_delete(dac_mutex);
        dac_mutex = RT_NULL;
    }
    
    rt_kprintf("DAC device deinitialized\n");
    return RT_EOK;
}

/* 示例1: 基本DC电压输出 */
void dac_dc_voltage_sample(void)
{
    rt_err_t ret;
    
    /* 初始化DAC设备 */
    ret = dac_device_init();
    if (ret != RT_EOK)
    {
        return;
    }
    
    /* 输出不同电压示例 */
    output_voltage(dac_dev, DAC_CHANNEL, 500);  /* 输出0.5V */
    rt_thread_mdelay(2000);
    
    output_voltage(dac_dev, DAC_CHANNEL, 1000); /* 输出1.0V */
    rt_thread_mdelay(2000);
    
    output_voltage(dac_dev, DAC_CHANNEL, 1500); /* 输出1.5V */
    rt_thread_mdelay(2000);
    
    output_voltage(dac_dev, DAC_CHANNEL, 2000); /* 输出2.0V */
    rt_thread_mdelay(2000);
    
    output_voltage(dac_dev, DAC_CHANNEL, 2500); /* 输出2.5V */
    rt_thread_mdelay(2000);
    
    output_voltage(dac_dev, DAC_CHANNEL, 3000); /* 输出3.0V */
    rt_thread_mdelay(2000);
    
    /* 反初始化DAC设备 */
    dac_device_deinit();
}

/* 示例2: 波形生成 */
void dac_wave_generator_sample(void)
{
    rt_err_t ret;
    rt_thread_t wave_thread = RT_NULL;
    
    /* 初始化DAC设备 */
    ret = dac_device_init();
    if (ret != RT_EOK)
    {
        return;
    }
    
    /* 创建波形输出线程 */
    wave_thread = rt_thread_create("dac_wave", dac_wave_thread_entry, RT_NULL,
                                  THREAD_STACK_SIZE, THREAD_PRIORITY, THREAD_TIMESLICE);
    if (wave_thread != RT_NULL)
    {
        rt_thread_startup(wave_thread);
        rt_kprintf("Wave generation thread started\n");
    }
    else
    {
        rt_kprintf("Create wave generation thread failed\n");
        dac_device_deinit();
        return;
    }
    
    /* 运行20秒后退出 */
    rt_thread_mdelay(20000);
    
    /* 停止波形输出并清理资源 */
    dac_running = RT_FALSE;
    rt_thread_mdelay(100);
    
    /* 反初始化DAC设备 */
    dac_device_deinit();
}

/* 示例3: 错误处理和恢复 */
void dac_error_handling_sample(void)
{
    rt_err_t ret;
    
    /* 初始化DAC设备 */
    ret = dac_device_init();
    if (ret != RT_EOK)
    {
        return;
    }
    
    /* 正常输出 */
    rt_kprintf("Normal output test\n");
    output_voltage(dac_dev, DAC_CHANNEL, 1500);
    rt_thread_mdelay(1000);
    
    /* 测试错误处理: 无效通道 */
    rt_kprintf("\nInvalid channel test\n");
    ret = rt_dac_write(dac_dev, 99, 2000);
    if (ret != RT_EOK)
    {
        rt_kprintf("Write to invalid channel failed as expected: %d\n", ret);
    }
    
    /* 测试错误处理: 超出范围值 */
    rt_kprintf("\nOut of range value test\n");
    ret = output_voltage(dac_dev, DAC_CHANNEL, 5000);  /* 超过参考电压 */
    if (ret == RT_EOK)
    {
        rt_kprintf("Value was clamped to valid range\n");
    }
    
    /* 测试恢复机制 */
    rt_kprintf("\nRecovery mechanism test\n");
    ret = dac_recovery(dac_dev, DAC_CHANNEL);
    if (ret == RT_EOK)
    {
        rt_kprintf("Recovery successful\n");
        output_voltage(dac_dev, DAC_CHANNEL, 2000);
    }
    
    /* 反初始化DAC设备 */
    dac_device_deinit();
}

/* 导出到FinSH命令 */
#ifdef RT_USING_FINSH
#include <finsh.h>

/* 导出DC电压示例 */
MSH_CMD_EXPORT(dac_dc_voltage_sample, DAC DC voltage output sample);

/* 导出波形生成示例 */
MSH_CMD_EXPORT(dac_wave_generator_sample, DAC wave generator sample);

/* 导出错误处理示例 */
MSH_CMD_EXPORT(dac_error_handling_sample, DAC error handling sample);
#endif /* RT_USING_FINSH */ 