#ifndef __SLAVE_H
#define __SLAVE_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <rtthread.h>
#include "modbus.h"
#include "modbus_slave_util.h"
#include "serial_comm.h"

extern rt_mutex_t slave_mtx;
extern const modbus_slave_util_t slave_util;

/* Modbus功能码定义 */
#define MODBUS_FC_READ_COILS                  0x01
#define MODBUS_FC_READ_DISCRETE_INPUTS        0x02
#define MODBUS_FC_READ_HOLDING_REGISTERS      0x03
#define MODBUS_FC_READ_INPUT_REGISTERS        0x04
#define MODBUS_FC_WRITE_SINGLE_COIL           0x05
#define MODBUS_FC_WRITE_SINGLE_REGISTER       0x06
#define MODBUS_FC_WRITE_MULTIPLE_COILS        0x0F
#define MODBUS_FC_WRITE_MULTIPLE_REGISTERS    0x10
/* 自定义扩展功能码定义放到protocol_converter.h中统一管理 */

/* Modbus异常码定义 */
#define MODBUS_EXCEPTION_ILLEGAL_FUNCTION     0x01
#define MODBUS_EXCEPTION_ILLEGAL_DATA_ADDRESS 0x02
#define MODBUS_EXCEPTION_ILLEGAL_DATA_VALUE   0x03
#define MODBUS_EXCEPTION_SLAVE_DEVICE_FAILURE 0x04

/* Modbus请求结构体定义 */
typedef struct {
    uint8_t slave;      /* 从站地址 */
    uint8_t function;   /* 功能码 */
    uint8_t *data;      /* 数据部分 */
    int length;         /* 数据长度 */
} modbus_request_t;

/* 使用modbus.h中已有的modbus_backend定义 */
typedef struct modbus_slave_backend {
    rt_err_t (*init)(void);
    rt_err_t (*deinit)(void);
    rt_err_t (*send)(uint8_t *data, int length);
    rt_err_t (*receive)(uint8_t *data, int *length);
} modbus_slave_backend_t;

/* Modbus从站上下文 */
typedef struct {
    uint8_t slave_addr;         /* 从站地址 */
    const modbus_slave_backend_t *backend;  /* 后端接口 */
    struct {
        rt_err_t (*get_coils_status)(uint16_t addr, uint8_t *status);  /* 获取线圈状态回调 */
        rt_err_t (*get_discrete_inputs)(uint16_t addr, uint8_t *status);  /* 获取离散输入状态回调 */
        rt_err_t (*get_holding_registers)(uint16_t addr, uint16_t *reg);  /* 获取保持寄存器回调 */
        rt_err_t (*get_input_registers)(uint16_t addr, uint16_t *reg);  /* 获取输入寄存器回调 */
        rt_err_t (*set_coil)(uint16_t addr, uint8_t status);  /* 设置单个线圈回调 */
        rt_err_t (*set_coils)(uint16_t addr, uint16_t nb, const uint8_t *data);  /* 设置多个线圈回调 */
        rt_err_t (*set_holding_register)(uint16_t addr, uint16_t value);  /* 设置单个保持寄存器回调 */
        rt_err_t (*set_holding_registers)(uint16_t addr, uint16_t nb, const uint16_t *data);  /* 设置多个保持寄存器回调 */
    } callbacks;
    uint8_t response_buffer[256]; /* 响应缓冲区 */
} modbus_slave_context_t;

/* 全局从站上下文 */
extern modbus_slave_context_t modbus_slave_ctx;

/* 初始化与反初始化函数 */
rt_err_t modbus_slave_init(uint8_t slave_addr, const modbus_slave_backend_t *backend);
rt_err_t modbus_slave_deinit(void);

/* 回调设置函数 */
rt_err_t modbus_slave_set_get_coils_callback(rt_err_t (*callback)(uint16_t addr, uint8_t *status));
rt_err_t modbus_slave_set_get_discrete_inputs_callback(rt_err_t (*callback)(uint16_t addr, uint8_t *status));
rt_err_t modbus_slave_set_get_holding_registers_callback(rt_err_t (*callback)(uint16_t addr, uint16_t *reg));
rt_err_t modbus_slave_set_get_input_registers_callback(rt_err_t (*callback)(uint16_t addr, uint16_t *reg));
rt_err_t modbus_slave_set_set_coil_callback(rt_err_t (*callback)(uint16_t addr, uint8_t status));
rt_err_t modbus_slave_set_set_coils_callback(rt_err_t (*callback)(uint16_t addr, uint16_t nb, const uint8_t *data));
rt_err_t modbus_slave_set_set_holding_register_callback(rt_err_t (*callback)(uint16_t addr, uint16_t value));
rt_err_t modbus_slave_set_set_holding_registers_callback(rt_err_t (*callback)(uint16_t addr, uint16_t nb, const uint16_t *data));

/* 请求处理函数 */
rt_err_t modbus_slave_poll(void);
int modbus_parse_request(uint8_t *req, int req_length, modbus_request_t *request);

/* 响应函数声明 */
rt_err_t respond_to_read_coils_request(modbus_request_t *request);
rt_err_t respond_to_read_discrete_inputs_request(modbus_request_t *request);
rt_err_t respond_to_read_registers_request(modbus_request_t *request);
rt_err_t respond_to_extended_read_registers_request(modbus_request_t *request);
rt_err_t respond_to_write_single_coil_request(modbus_request_t *request);
rt_err_t respond_to_write_single_register_request(modbus_request_t *request);
rt_err_t respond_to_write_multiple_coils_request(modbus_request_t *request);
rt_err_t respond_to_write_multiple_registers_request(modbus_request_t *request);
rt_err_t respond_with_exception(uint8_t slave, uint8_t function, uint8_t exception_code);

/* 帧构建函数声明 */
int build_function_read_coils_frame(uint8_t slave, uint8_t function, uint16_t addr, uint16_t nb, uint8_t *dest);
int build_function_read_discrete_inputs_frame(uint8_t slave, uint8_t function, uint16_t addr, uint16_t nb, uint8_t *dest);
int build_function_read_registers_frame(uint8_t slave, uint8_t function, uint16_t addr, uint16_t nb, uint8_t *dest);
int build_function_write_single_coil_frame(uint8_t slave, uint8_t function, uint16_t addr, uint16_t status, uint8_t *dest);
int build_function_write_single_register_frame(uint8_t slave, uint8_t function, uint16_t addr, uint16_t value, uint8_t *dest);
int build_function_write_multiple_coils_frame(uint8_t slave, uint8_t function, uint16_t addr, uint16_t nb, uint8_t *dest);
int build_function_write_registers_frame(uint8_t slave, uint8_t function, uint16_t addr, uint16_t nb, uint8_t *dest);
int build_exception_frame(uint8_t slave, uint8_t function, uint8_t exception_code, uint8_t *dest);

/* 扩展函数的声明放到protocol_converter.h中 */

/* 外部引用 */
int read_registers(uint16_t start_addr, uint16_t nb);
int write_register(uint16_t start_addr, uint16_t value);
int write_registers(uint16_t start_addr, uint16_t nb, uint8_t *data);
int build_report_frame(uint8_t function, uint16_t start_addr, uint16_t nb, uint8_t *data, uint8_t *out_frame);
int report_registers(uint8_t function, uint16_t start_addr, uint16_t nb, uint8_t *data);

/* 新增功能码函数 */
int read_coils(uint16_t start_addr, uint16_t nb);
int read_discrete_inputs(uint16_t start_addr, uint16_t nb);
int read_input_registers(uint16_t start_addr, uint16_t nb);
int write_single_coil(uint16_t coil_addr, uint8_t status);
int write_multiple_coils(uint16_t start_addr, uint16_t nb, uint8_t *data);

/* 寄存器映射管理函数 */
int get_register_maps_count(void);
int add_register_map(uint16_t start_addr, uint16_t end_addr, 
                     int (*get_func)(void *buf, int bufsz),
                     int (*set_func)(int index, int len, void *buf, int bufsz));
int remove_register_map(uint16_t start_addr, uint16_t end_addr);
void clear_register_maps(void);

/* 辅助函数 */
int custom_get_map_buf(void *buf, int bufsz, uint16_t *custom_data, int data_size);
int custom_set_map_buf(int index, int len, void *buf, int bufsz, 
                       uint16_t *custom_data, int data_size,
                       void (*callback)(uint16_t *data, int start, int len));

/* 条件编译区域 */
#if MODBUS_USING_COILS
/**
 * @brief 处理读线圈请求
 * 
 * @param address 线圈地址
 * @param quantity 线圈数量
 * @return rt_err_t 处理结果
 */
rt_err_t respond_to_read_bits_request(modbus_request_t *request);

/**
 * @brief 处理写单个线圈请求
 * 
 * @param address 线圈地址
 * @param value 线圈值
 * @return rt_err_t 处理结果
 */
rt_err_t respond_to_write_single_coil_request(modbus_request_t *request);

/**
 * @brief 处理写多个线圈请求
 * 
 * @param data 请求数据
 * @param length 数据长度
 * @return rt_err_t 处理结果
 */
rt_err_t respond_to_write_multiple_coils_request(modbus_request_t *request);
#endif

#if MODBUS_USING_DISCRETE_INPUTS
/**
 * @brief 处理读离散输入请求
 * 
 * @param address 输入地址
 * @param quantity 输入数量
 * @return rt_err_t 处理结果
 */
rt_err_t respond_to_read_discrete_inputs_request(modbus_request_t *request);
#endif

#if MODBUS_USING_INPUT_REGISTERS
/**
 * @brief 处理读输入寄存器请求
 * 
 * @param address 寄存器地址
 * @param quantity 寄存器数量
 * @return rt_err_t 处理结果
 */
rt_err_t respond_to_read_input_registers_request(modbus_request_t *request);
#endif

#ifdef __cplusplus
}
#endif

#endif /* __SLAVE_H */
