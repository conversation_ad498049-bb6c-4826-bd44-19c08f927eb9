#include "slave.h"

static uint8_t _tab_input_bits[256] = {0};

static int get_map_buf(void *buf, int bufsz)
{
    uint8_t *ptr = (uint8_t *)buf;

    rt_mutex_take(slave_mtx, RT_WAITING_FOREVER);
    for (int i = 0; i < sizeof(_tab_input_bits); i++) {
        ptr[i] = _tab_input_bits[i];
    }
    rt_mutex_release(slave_mtx);

    return 0;
}

const modbus_slave_util_map_t input_bit_maps[1] = {
    {0x0000, 0x0001, get_map_buf, NULL}};
