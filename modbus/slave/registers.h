/*
 * Copyright (c) 2024, Xi'an Helon-Electric Co., Ltd.
 *
 * Author: kevin<PERSON><PERSON><PERSON>
 *
 * CONFIDENTIAL AND PROPRIETARY
 * 严格保密，泄露必究
 *
 * Change Logs:
 * Date           Author       Notes
 * 2024-07-14    kevin.zhao  first version
 */

#ifndef __MODBUS_REGISTERS_H
#define __MODBUS_REGISTERS_H

#ifdef __cplusplus
extern "C" {
#endif

#include <rtthread.h>
#include "modbus.h"
#include "modbus_slave_util.h"

/* 最大支持的寄存器映射区域数量 */
#ifndef MODBUS_MAX_REGISTER_MAPS
#define MODBUS_MAX_REGISTER_MAPS  16
#endif

/**
 * @brief 添加寄存器映射区域
 * 
 * @param start_addr 起始地址
 * @param end_addr 结束地址
 * @param reg_buf 寄存器数据缓冲区
 * @param reg_count 寄存器数量
 * @return int 0:成功, -1:失败
 */
int register_add_map(uint16_t start_addr, uint16_t end_addr, uint16_t *reg_buf, uint16_t reg_count);

/**
 * @brief 删除寄存器映射区域
 * 
 * @param start_addr 起始地址
 * @param end_addr 结束地址
 * @return int 0:成功, -1:失败
 */
int register_remove_map(uint16_t start_addr, uint16_t end_addr);

/**
 * @brief 获取寄存器映射表
 * 
 * @param count 返回映射区域数量
 * @return const modbus_slave_util_map_t* 映射表指针
 */
const modbus_slave_util_map_t *get_register_maps(uint8_t *count);

#ifdef __cplusplus
}
#endif

#endif /* __MODBUS_REGISTERS_H */ 