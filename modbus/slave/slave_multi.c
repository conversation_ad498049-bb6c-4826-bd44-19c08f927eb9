/*
 * Copyright (c) 2024, Xi'an Helon-Electric Co., Ltd.
 *
 * Author: kevin<PERSON><PERSON><PERSON>
 *
 * CONFIDENTIAL AND PROPRIETARY
 * 严格保密，泄露必究
 *
 * Change Logs:
 * Date           Author       Notes
 * 2024-12-19     kevin.zhao   Multi-instance Modbus Slave implementation
 */

#include "slave.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <rtthread.h>
#include <rtconfig.h>

#define DBG_ENABLE
#define DBG_COLOR
#define DBG_SECTION_NAME "slave_multi"
#define DBG_LEVEL        DBG_LOG
#include <rtdbg.h>

/* 全局多实例管理器 */
modbus_slave_manager_t g_modbus_slave_manager = {0};

/* 默认配置 */
static const modbus_slave_config_t default_configs[BSP_SERIAL_NUMBER] = {
#if BSP_SERIAL_NUMBER >= 1
    {
        .slave_addr = 1,
        .serial_index = SERIAL_INDEX_1,
        .baudrate = 9600,
        .data_bits = DATA_BITS_8,
        .stop_bits = STOP_BITS_1,
        .parity = PARITY_NONE,
        .enabled = RT_FALSE
    },
#endif
#if BSP_SERIAL_NUMBER >= 2
    {
        .slave_addr = 2,
        .serial_index = SERIAL_INDEX_2,
        .baudrate = 9600,
        .data_bits = DATA_BITS_8,
        .stop_bits = STOP_BITS_1,
        .parity = PARITY_NONE,
        .enabled = RT_FALSE
    },
#endif
#if BSP_SERIAL_NUMBER >= 3
    {
        .slave_addr = 3,
        .serial_index = SERIAL_INDEX_3,
        .baudrate = 9600,
        .data_bits = DATA_BITS_8,
        .stop_bits = STOP_BITS_1,
        .parity = PARITY_NONE,
        .enabled = RT_FALSE
    },
#endif
#if BSP_SERIAL_NUMBER >= 4
    {
        .slave_addr = 4,
        .serial_index = SERIAL_INDEX_4,
        .baudrate = 9600,
        .data_bits = DATA_BITS_8,
        .stop_bits = STOP_BITS_1,
        .parity = PARITY_NONE,
        .enabled = RT_FALSE
    },
#endif
};

/* 前向声明 */
static void modbus_slave_thread_entry(void *parameter);
static rt_err_t modbus_slave_instance_init(modbus_slave_instance_t *instance, const modbus_slave_config_t *config);
static rt_err_t modbus_slave_instance_deinit(modbus_slave_instance_t *instance);

/**
 * @brief   初始化多实例 Modbus Slave 管理器
 */
rt_err_t modbus_slave_multi_init(void)
{
    rt_err_t ret = RT_EOK;
    
    /* 检查是否已初始化 */
    if (g_modbus_slave_manager.initialized) {
        LOG_W("Modbus slave multi manager already initialized");
        return RT_EOK;
    }
    
    /* 清空管理器 */
    rt_memset(&g_modbus_slave_manager, 0, sizeof(g_modbus_slave_manager));
    
    /* 创建管理器互斥锁 */
    g_modbus_slave_manager.manager_mutex = rt_mutex_create("mb_mgr_mtx", RT_IPC_FLAG_FIFO);
    if (g_modbus_slave_manager.manager_mutex == RT_NULL) {
        LOG_E("Failed to create manager mutex");
        return -RT_ERROR;
    }
    
    /* 初始化所有实例为默认配置 */
    for (int i = 0; i < BSP_SERIAL_NUMBER; i++) {
        modbus_slave_instance_t *instance = &g_modbus_slave_manager.instances[i];
        
        /* 复制默认配置 */
        rt_memcpy(&instance->config, &default_configs[i], sizeof(modbus_slave_config_t));
        
        /* 初始化其他字段 */
        instance->serial_comm = RT_NULL;
        instance->ctx = RT_NULL;
        instance->thread = RT_NULL;
        instance->running = RT_FALSE;
        instance->mutex = RT_NULL;
    }
    
    /* 设置初始化标志 */
    g_modbus_slave_manager.initialized = 1;
    
    LOG_I("Modbus slave multi manager initialized");
    return ret;
}

/**
 * @brief   反初始化多实例 Modbus Slave 管理器
 */
rt_err_t modbus_slave_multi_deinit(void)
{
    rt_err_t ret = RT_EOK;
    
    /* 检查是否已初始化 */
    if (!g_modbus_slave_manager.initialized) {
        LOG_W("Modbus slave multi manager not initialized");
        return RT_EOK;
    }
    
    /* 停止并销毁所有实例 */
    for (int i = 0; i < BSP_SERIAL_NUMBER; i++) {
        modbus_slave_destroy_instance((serial_index_t)i);
    }
    
    /* 删除管理器互斥锁 */
    if (g_modbus_slave_manager.manager_mutex != RT_NULL) {
        rt_mutex_delete(g_modbus_slave_manager.manager_mutex);
        g_modbus_slave_manager.manager_mutex = RT_NULL;
    }
    
    /* 清除初始化标志 */
    g_modbus_slave_manager.initialized = 0;
    
    LOG_I("Modbus slave multi manager deinitialized");
    return ret;
}

/**
 * @brief   创建 Modbus Slave 实例
 */
rt_err_t modbus_slave_create_instance(const modbus_slave_config_t *config)
{
    rt_err_t ret = RT_EOK;
    modbus_slave_instance_t *instance;
    
    /* 参数检查 */
    if (config == RT_NULL) {
        LOG_E("Invalid config parameter");
        return -RT_EINVAL;
    }
    
    if (config->serial_index >= BSP_SERIAL_NUMBER) {
        LOG_E("Invalid serial index: %d", config->serial_index);
        return -RT_EINVAL;
    }
    
    /* 检查管理器是否初始化 */
    if (!g_modbus_slave_manager.initialized) {
        LOG_E("Manager not initialized");
        return -RT_ERROR;
    }
    
    /* 获取实例 */
    instance = &g_modbus_slave_manager.instances[config->serial_index];
    
    /* 锁定管理器 */
    rt_mutex_take(g_modbus_slave_manager.manager_mutex, RT_WAITING_FOREVER);
    
    /* 检查实例是否已存在 */
    if (instance->ctx != RT_NULL) {
        LOG_W("Instance for serial %d already exists", config->serial_index + 1);
        rt_mutex_release(g_modbus_slave_manager.manager_mutex);
        return RT_EOK;
    }
    
    /* 初始化实例 */
    ret = modbus_slave_instance_init(instance, config);
    if (ret != RT_EOK) {
        LOG_E("Failed to initialize instance for serial %d", config->serial_index + 1);
    } else {
        LOG_I("Created Modbus slave instance for serial %d, slave addr %d", 
              config->serial_index + 1, config->slave_addr);
    }
    
    rt_mutex_release(g_modbus_slave_manager.manager_mutex);
    return ret;
}

/**
 * @brief   销毁 Modbus Slave 实例
 */
rt_err_t modbus_slave_destroy_instance(serial_index_t serial_index)
{
    rt_err_t ret = RT_EOK;
    modbus_slave_instance_t *instance;
    
    /* 参数检查 */
    if (serial_index >= BSP_SERIAL_NUMBER) {
        LOG_E("Invalid serial index: %d", serial_index);
        return -RT_EINVAL;
    }
    
    /* 检查管理器是否初始化 */
    if (!g_modbus_slave_manager.initialized) {
        LOG_E("Manager not initialized");
        return -RT_ERROR;
    }
    
    /* 获取实例 */
    instance = &g_modbus_slave_manager.instances[serial_index];
    
    /* 锁定管理器 */
    rt_mutex_take(g_modbus_slave_manager.manager_mutex, RT_WAITING_FOREVER);
    
    /* 检查实例是否存在 */
    if (instance->ctx == RT_NULL) {
        LOG_W("Instance for serial %d does not exist", serial_index + 1);
        rt_mutex_release(g_modbus_slave_manager.manager_mutex);
        return RT_EOK;
    }
    
    /* 先停止实例 */
    if (instance->running) {
        modbus_slave_stop_instance(serial_index);
    }
    
    /* 反初始化实例 */
    ret = modbus_slave_instance_deinit(instance);
    if (ret != RT_EOK) {
        LOG_E("Failed to deinitialize instance for serial %d", serial_index + 1);
    } else {
        LOG_I("Destroyed Modbus slave instance for serial %d", serial_index + 1);
    }
    
    rt_mutex_release(g_modbus_slave_manager.manager_mutex);
    return ret;
}

/**
 * @brief   获取 Modbus Slave 实例
 */
modbus_slave_instance_t *modbus_slave_get_instance(serial_index_t serial_index)
{
    /* 参数检查 */
    if (serial_index >= BSP_SERIAL_NUMBER) {
        LOG_E("Invalid serial index: %d", serial_index);
        return RT_NULL;
    }
    
    /* 检查管理器是否初始化 */
    if (!g_modbus_slave_manager.initialized) {
        LOG_E("Manager not initialized");
        return RT_NULL;
    }
    
    modbus_slave_instance_t *instance = &g_modbus_slave_manager.instances[serial_index];
    
    /* 检查实例是否存在 */
    if (instance->ctx == RT_NULL) {
        return RT_NULL;
    }
    
    return instance;
}
