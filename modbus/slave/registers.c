/*
 * Copyright (c) 2006-2018, RT-Thread Development Team
 * Copyright (c) 2024, Xi'an Helon-Electric Co., Ltd.
 * 
 * SPDX-License-Identifier: Apache-2.0
 * 
 * Change Logs:
 * Date           Author       Notes
 * 2024-01-25     kevin.zhao   first version
 */

#include <modbus_slave_util.h>
#include <rtdevice.h>
#include <rtthread.h>
#include <string.h>
#include "slave.h"

#ifndef MODBUS_SLAVE_REGISTER_MAP_SIZE
#define MODBUS_SLAVE_REGISTER_MAP_SIZE 16 // 默认值，请根据实际需要在配置文件中修改
#endif

#if (MODBUS_SLAVE_REGISTER_MAP_SIZE <= 0)
    #error "MODBUS_SLAVE_REGISTER_MAP_SIZE must be defined and greater than 0"
#endif

static modbus_slave_util_map_t register_maps_array[MODBUS_SLAVE_REGISTER_MAP_SIZE];
static uint8_t register_maps_count = 0;
static rt_mutex_t register_maps_mutex = RT_NULL;

/* 寄存器映射数组和当前映射数量 */
#ifndef MODBUS_SLAVE_REGISTER_MAP_SIZE
#define MODBUS_SLAVE_REGISTER_MAP_SIZE 16  /* 默认支持16个地址映射 */
#endif


static uint16_t D[256] = {0};
static modbus_slave_util_map_t register_maps_array[MODBUS_SLAVE_REGISTER_MAP_SIZE];

/* 对外暴露的register_maps指针，初始指向第一个元素 */
const modbus_slave_util_map_t *register_maps = register_maps_array;

/* 获取当前register_maps的数量 */
int get_register_maps_count(void)
{
    return register_maps_count;
}

/* 基本的get_map_buf实现 */
static int get_map_buf(void *buf, int bufsz)
{
    uint16_t *ptr = (uint16_t *)buf;

    rt_mutex_take(slave_mtx, RT_WAITING_FOREVER);
    for (int i = 0; i < sizeof(D) / sizeof(D[0]); i++) {
        ptr[i] = D[i];
    }
    rt_mutex_release(slave_mtx);

    return 0;
}

/* 基本的set_map_buf实现 */
static int set_map_buf(int index, int len, void *buf, int bufsz)
{
    uint16_t *ptr = (uint16_t *)buf;

    rt_mutex_take(slave_mtx, RT_WAITING_FOREVER);
    for (int i = 0; i < len; i++) {
        D[index + i] = ptr[index + i];
    }
    rt_mutex_release(slave_mtx);

    return 0;
}

/* 自定义排序函数，确保未被赋值的映射不参与排序 */
static void register_maps_sort(void)
{
    int i, j;
    modbus_slave_util_map_t temp;
    
    /* 只对已使用的映射进行排序 */
    for (i = 0; i < register_maps_count - 1; i++) {
        for (j = 0; j < register_maps_count - i - 1; j++) {
            /* 按起始地址排序 */
            if (register_maps_array[j].start_addr > register_maps_array[j + 1].start_addr) {
                /* 交换两个元素 */
                temp = register_maps_array[j];
                register_maps_array[j] = register_maps_array[j + 1];
                register_maps_array[j + 1] = temp;
            }
        }
    }
}

/* 检查地址范围是否有重叠 */
static int check_address_overlap(uint16_t start_addr, uint16_t end_addr)
{
    for (int i = 0; i < register_maps_count; i++) {
        /* 判断是否与已有映射重叠 */
        if ((start_addr <= register_maps_array[i].end_addr && end_addr >= register_maps_array[i].start_addr) ||
            (register_maps_array[i].start_addr <= end_addr && register_maps_array[i].end_addr >= start_addr)) {
            return -1; /* 地址重叠 */
        }
    }
    return 0; /* 无重叠 */
}

/**
 * @brief 添加一个寄存器地址映射
 * 
 * @param start_addr 起始地址
 * @param end_addr 结束地址
 * @param get_func 获取数据回调函数
 * @param set_func 设置数据回调函数
 * @return int 0:成功, -1:失败(空间不足或地址重叠)
 */
int add_register_map(uint16_t start_addr, uint16_t end_addr, 
                    int (*get_func)(void *buf, int bufsz),
                    int (*set_func)(int index, int len, void *buf, int bufsz))
{
    /* 检查映射数组是否已满 */
    if (register_maps_count >= MODBUS_SLAVE_REGISTER_MAP_SIZE) {
        rt_kprintf("Register maps array is full\n");
        return -1;
    }

    /* 检查地址是否合法 */
    if (start_addr > end_addr) {
        rt_kprintf("Invalid address range: start_addr > end_addr\n");
        return -1;
    }

    /* 检查地址是否重叠 */
    if (check_address_overlap(start_addr, end_addr) != 0) {
        rt_kprintf("Address range overlaps with existing maps\n");
        return -1;
    }

    /* 添加新映射 */
    register_maps_array[register_maps_count].start_addr = start_addr;
    register_maps_array[register_maps_count].end_addr = end_addr;
    register_maps_array[register_maps_count].get = get_func ? get_func : get_map_buf;
    register_maps_array[register_maps_count].set = set_func ? set_func : set_map_buf;
    
    register_maps_count++;

    /* 按起始地址排序 */
    register_maps_sort();

    return 0;
}

/**
 * @brief 移除指定地址范围的映射
 * 
 * @param start_addr 起始地址
 * @param end_addr 结束地址
 * @return int 0:成功, -1:失败(未找到匹配项)
 */
int remove_register_map(uint16_t start_addr, uint16_t end_addr)
{
    int found = -1;
    
    /* 查找匹配的映射 */
    for (int i = 0; i < register_maps_count; i++) {
        if (register_maps_array[i].start_addr == start_addr && 
            register_maps_array[i].end_addr == end_addr) {
            found = i;
            break;
        }
    }
    
    if (found < 0) {
        return -1; /* 未找到匹配项 */
    }
    
    /* 移除找到的映射,并将后面的元素前移 */
    if (found < (register_maps_count - 1)) {
        memmove(&register_maps_array[found], &register_maps_array[found + 1], 
                (register_maps_count - found - 1) * sizeof(modbus_slave_util_map_t));
    }
    
    register_maps_count--;
    return 0;
}

/**
 * @brief 清空所有寄存器映射
 */
void clear_register_maps(void)
{
    register_maps_count = 0;
}

/**
 * @brief 创建一个自定义的get_map_buf函数
 * 
 * @param custom_data 用户自定义数据缓冲区
 * @param data_size 数据大小(16位寄存器个数)
 * @return int (*)(void *, int) 返回创建的函数指针
 */
int custom_get_map_buf(void *buf, int bufsz, uint16_t *custom_data, int data_size)
{
    uint16_t *ptr = (uint16_t *)buf;

    rt_mutex_take(slave_mtx, RT_WAITING_FOREVER);
    int copy_size = (bufsz < data_size * sizeof(uint16_t)) ? 
                    (bufsz / sizeof(uint16_t)) : data_size;
    
    for (int i = 0; i < copy_size; i++) {
        ptr[i] = custom_data[i];
    }
    rt_mutex_release(slave_mtx);

    return 0;
}

/**
 * @brief 创建一个自定义的set_map_buf函数
 * 
 * @param custom_data 用户自定义数据缓冲区
 * @param data_size 数据大小(16位寄存器个数)
 * @param callback 数据变化回调函数
 * @return int (*)(int, int, void *, int) 返回创建的函数指针
 */
int custom_set_map_buf(int index, int len, void *buf, int bufsz, 
                      uint16_t *custom_data, int data_size,
                      void (*callback)(uint16_t *data, int start, int len))
{
    uint16_t *ptr = (uint16_t *)buf;

    if (index + len > data_size) {
        return -1; /* 超出范围 */
    }

    rt_mutex_take(slave_mtx, RT_WAITING_FOREVER);
    for (int i = 0; i < len; i++) {
        if (index + i < data_size) {
            custom_data[index + i] = ptr[index + i];
        }
    }
    
    /* 如果提供了回调函数，则调用 */
    if (callback) {
        callback(custom_data, index, len);
    }
    
    rt_mutex_release(slave_mtx);

    return 0;
}

/* 初始化时添加默认映射 */
static int register_maps_init(void)
{
    /* 添加默认映射 */
    add_register_map(0x0000, 0x00FF, get_map_buf, set_map_buf);
    return 0;
}
INIT_COMPONENT_EXPORT(register_maps_init);
