#include "slave.h"

static uint16_t _tab_input_registers[256] = {0};

static int get_map_buf(void *buf, int bufsz)
{
    uint16_t *ptr = (uint16_t *)buf;

    rt_mutex_take(slave_mtx, RT_WAITING_FOREVER);
    for (int i = 0; i < sizeof(_tab_input_registers) / sizeof(_tab_input_registers[0]); i++) {
        ptr[i] = _tab_input_registers[i];
    }
    rt_mutex_release(slave_mtx);

    return 0;
}

const modbus_slave_util_map_t input_register_maps[1] = {
    {0x0000, 0x00FF, get_map_buf, NULL}};
